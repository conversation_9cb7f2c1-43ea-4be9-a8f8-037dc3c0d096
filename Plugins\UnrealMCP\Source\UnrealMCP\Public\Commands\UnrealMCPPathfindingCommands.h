#pragma once

#include "CoreMinimal.h"
#include "Json.h"

// Core Navigation APIs - UE 5.6.1 Modern
#include "AI/Navigation/NavigationTypes.h"
#include "NavigationSystem.h"
#include "AI/Navigation/NavAgentInterface.h"
#include "AI/Navigation/NavQueryFilter.h"
#include "NavigationSystem.h"
#include "NavigationSystemTypes.h"
#include "NavigationPath.h"
#include "AbstractNavData.h"

// Advanced Pathfinding APIs - UE 5.6.1 Experimental
#include "NavigationTestingActor.h"
#include "NavLinkCustomInterface.h"
#include "NavLinkCustomComponent.h"

// Editor APIs - UE 5.6.1 Enhanced
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"

// Utilities
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "UObject/Package.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "UObject/NoExportTypes.h"
#include "UnrealMCPPathfindingCommands.generated.h"

// ========================================
// MODERN UE 5.6.1 PATHFINDING STRUCTURES
// ========================================

/**
 * Multilayer path structure for complex pathfinding
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FMultilayerPath
{
    GENERATED_USTRUCT_BODY()

    /** Path points across multiple layers */
    UPROPERTY(BlueprintReadOnly)
    TArray<FVector> PathPoints;

    /** Layer indices for each path point */
    UPROPERTY(BlueprintReadOnly)
    TArray<int32> LayerIndices;

    /** Transition types between points */
    UPROPERTY(BlueprintReadOnly)
    TArray<FString> TransitionTypes;

    /** Total path cost */
    UPROPERTY(BlueprintReadOnly)
    float TotalCost = 0.0f;

    /** Creation timestamp for cache expiration */
    double CreationTime = 0.0;

    /** Whether path is valid */
    UPROPERTY(BlueprintReadOnly)
    bool bIsValid = false;

    FMultilayerPath()
    {
        CreationTime = FPlatformTime::Seconds();
    }
};

/**
 * Handler class for Advanced Multilayer Pathfinding System
 * 
 * Implements PRODUCTION READY pathfinding tools for Auracron's 3-layer MOBA system
 * with advanced A* algorithm, vertical navigation costs, and mobile optimization.
 * 
 * Features:
 * - Custom A* algorithm for 3 vertical layers (Planície Radiante, Firmamento Zephyr, Abismo Umbral)
 * - Vertical transition cost calculation (portals, elevators, bridges)
 * - Mobile-optimized navigation assistance
 * - Hierarchical pathfinding for performance
 * - Custom query filters per layer
 * - Heuristic optimization for multilayer scenarios
 * - Integration with portal/elevator systems
 * 
 * All implementations are PRODUCTION READY with:
 * - Thread safety (Game Thread execution)
 * - Robust parameter validation
 * - Mandatory disk saving
 * - Detailed logging
 * - Modern UE 5.6.1 APIs only
 */
class UNREALMCP_API FUnrealMCPPathfindingCommands
{
public:
    FUnrealMCPPathfindingCommands();

    /**
     * Main command handler dispatcher
     * @param CommandType The specific command to execute
     * @param Params JSON parameters for the command
     * @return JSON response with execution results
     */
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);

private:
    // ========================================
    // CORE PATHFINDING COMMANDS
    // ========================================

    /**
     * Creates advanced multilayer pathfinding system with custom A* algorithm
     * 
     * Parameters:
     * - system_name (string): Name of the pathfinding system
     * - layer_count (int): Number of vertical layers (default: 3)
     * - layer_heights (array): Z-coordinates for each layer
     * - transition_costs (object): Cost modifiers for vertical transitions
     * - heuristic_settings (object): A* heuristic configuration
     * 
     * Implementation:
     * - Creates custom NavigationQueryFilter for each layer
     * - Sets up vertical transition cost calculation
     * - Configures A* heuristic scaling per layer
     * - Implements hierarchical pathfinding optimization
     * 
     * @param Params JSON parameters
     * @return JSON response with pathfinding system details
     */
    TSharedPtr<FJsonObject> HandleCreateMultilayerPathfinding(const TSharedPtr<FJsonObject>& Params);

    /**
     * Configures custom query filters for layer-specific pathfinding
     * 
     * Parameters:
     * - filter_name (string): Name of the custom filter
     * - target_layer (string): Layer this filter applies to
     * - area_costs (object): Cost modifiers per area type
     * - heuristic_scale (float): A* heuristic multiplier
     * - backtracking_enabled (bool): Allow reverse pathfinding
     * 
     * Implementation:
     * - Creates UNavigationQueryFilter subclass
     * - Configures area costs and penalties
     * - Sets up heuristic scaling for layer
     * - Enables/disables backtracking for one-way links
     * 
     * @param Params JSON parameters
     * @return JSON response with filter configuration results
     */
    TSharedPtr<FJsonObject> HandleCreateCustomQueryFilter(const TSharedPtr<FJsonObject>& Params);

    /**
     * Sets up vertical transition costs for portals, elevators, and bridges
     * 
     * Parameters:
     * - transition_type (string): "portal", "elevator", "bridge"
     * - base_cost (float): Base cost for using transition
     * - time_penalty (float): Additional cost based on transition time
     * - interruption_risk (float): Cost modifier for vulnerability
     * - layer_distance_multiplier (float): Cost per layer traversed
     * 
     * Implementation:
     * - Modifies NavigationQueryFilter area costs
     * - Creates custom nav link cost calculation
     * - Integrates with existing portal/elevator systems
     * - Applies dynamic cost based on game state
     * 
     * @param Params JSON parameters
     * @return JSON response with transition cost setup results
     */
    TSharedPtr<FJsonObject> HandleSetupVerticalTransitionCosts(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // MOBILE OPTIMIZATION COMMANDS
    // ========================================

    /**
     * Creates mobile-optimized navigation assistance system
     * 
     * Parameters:
     * - assistance_level (string): "basic", "advanced", "full"
     * - auto_pathfinding (bool): Enable automatic path calculation
     * - visual_indicators (bool): Show path visualization
     * - route_suggestions (bool): AI-powered route optimization
     * - simplified_controls (bool): Touch-friendly navigation
     * 
     * Implementation:
     * - Creates simplified pathfinding interface
     * - Implements auto-path calculation with caching
     * - Sets up visual path indicators
     * - Configures touch gesture recognition
     * 
     * @param Params JSON parameters
     * @return JSON response with mobile assistance setup results
     */
    TSharedPtr<FJsonObject> HandleCreateMobileNavigationAssistance(const TSharedPtr<FJsonObject>& Params);

    /**
     * Optimizes pathfinding performance for mobile devices
     * 
     * Parameters:
     * - performance_level (string): "low", "medium", "high"
     * - max_search_nodes (int): Limit A* node exploration
     * - hierarchical_enabled (bool): Use hierarchical pathfinding
     * - cache_size (int): Path cache size for reuse
     * - update_frequency (float): Path recalculation frequency
     * 
     * Implementation:
     * - Configures A* search limits
     * - Enables hierarchical pathfinding
     * - Sets up path caching system
     * - Optimizes update frequencies
     * 
     * @param Params JSON parameters
     * @return JSON response with performance optimization results
     */
    TSharedPtr<FJsonObject> HandleOptimizePathfindingPerformance(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // ADVANCED PATHFINDING FEATURES
    // ========================================

    /**
     * Creates custom A* algorithm implementation for multilayer scenarios
     * 
     * Parameters:
     * - algorithm_name (string): Name of the custom algorithm
     * - layer_weights (array): Weight multipliers per layer
     * - vertical_preference (float): Preference for staying in same layer
     * - emergency_exit_costs (object): Costs for emergency transitions
     * - dynamic_cost_updates (bool): Enable real-time cost updates
     * 
     * Implementation:
     * - Extends FAbstractQueryFilter with custom logic
     * - Implements layer-aware heuristic calculation
     * - Creates dynamic cost update system
     * - Integrates with game state for real-time adjustments
     * 
     * @param Params JSON parameters
     * @return JSON response with custom algorithm creation results
     */
    TSharedPtr<FJsonObject> HandleCreateCustomAStarAlgorithm(const TSharedPtr<FJsonObject>& Params);

    /**
     * Sets up pathfinding debugging and visualization tools
     * 
     * Parameters:
     * - debug_level (string): "basic", "detailed", "full"
     * - visual_debug (bool): Enable visual debugging
     * - cost_display (string): "total", "heuristic", "real"
     * - step_by_step (bool): Enable step-by-step A* visualization
     * - performance_metrics (bool): Show performance data
     * 
     * Implementation:
     * - Creates NavigationTestingActor instances
     * - Configures debug visualization
     * - Sets up cost display options
     * - Enables performance profiling
     * 
     * @param Params JSON parameters
     * @return JSON response with debugging setup results
     */
    TSharedPtr<FJsonObject> HandleSetupPathfindingDebugTools(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // UTILITY METHODS
    // ========================================

    /**
     * Validates required parameters for commands
     * @param Params Input parameters
     * @param RequiredFields Array of required field names
     * @param OutError Error message if validation fails
     * @return true if all required fields are present
     */
    bool ValidateRequiredParams(const TSharedPtr<FJsonObject>& Params, 
                               const TArray<FString>& RequiredFields, 
                               FString& OutError);

    /**
     * Ensures command execution on Game Thread
     * @param Command Lambda to execute on Game Thread
     */
    void ExecuteOnGameThread(TFunction<void()> Command);

    /**
     * Creates standardized error response
     * @param ErrorMessage The error message
     * @return JSON error response
     */
    TSharedPtr<FJsonObject> CreateErrorResponse(const FString& ErrorMessage);

    /**
     * Creates standardized success response
     * @param CommandName The executed command name
     * @param ResultData Additional result data
     * @return JSON success response
     */
    TSharedPtr<FJsonObject> CreateSuccessResponse(const FString& CommandName, 
                                                 const TSharedPtr<FJsonObject>& ResultData);

    // ========================================
    // INTERNAL PATHFINDING LOGIC
    // ========================================

    /**
     * Calculates optimal path between layers considering all transition types
     * @param StartLocation Starting position
     * @param EndLocation Target position
     * @param StartLayer Starting layer index
     * @param EndLayer Target layer index
     * @param QueryFilter Custom filter to use
     * @return Calculated path with costs
     */
    FNavPathSharedPtr CalculateMultilayerPath(const FVector& StartLocation, 
                                             const FVector& EndLocation,
                                             int32 StartLayer, 
                                             int32 EndLayer,
                                             FSharedConstNavQueryFilter QueryFilter);

    /**
     * Calculates cost for vertical transitions between layers
     * @param TransitionType Type of transition (portal, elevator, bridge)
     * @param LayerDistance Number of layers to traverse
     * @param GameState Current game state for dynamic costs
     * @return Calculated transition cost
     */
    float CalculateVerticalTransitionCost(const FString& TransitionType, 
                                        int32 LayerDistance,
                                        const TSharedPtr<FJsonObject>& GameState);

    /**
     * Creates optimized path cache for mobile devices
     * @param CacheSize Maximum number of cached paths
     * @param ExpirationTime How long paths remain valid
     * @return Success status
     */
    bool CreateMobilePathCache(int32 CacheSize, float ExpirationTime);

    /**
     * Cleans up expired paths from cache using modern UE 5.6.1 APIs
     * @param ExpirationTime Maximum age of paths in seconds
     */
    void CleanupExpiredPaths(float ExpirationTime);

private:
    // Cached navigation data for each layer
    TMap<int32, TWeakObjectPtr<ANavigationData>> LayerNavigationData;
    
    // Custom query filters per layer
    TMap<FString, TSharedPtr<FNavigationQueryFilter>> CustomQueryFilters;
    
    // Vertical transition cost modifiers
    TMap<FString, float> VerticalTransitionCosts;
    
    // Mobile path cache
    TMap<FString, FNavPathSharedPtr> MobilePathCache;

    // Multilayer path cache for complex pathfinding
    TMap<FString, FMultilayerPath> MultilayerPathCache;

    // Timer handles for cache cleanup
    TArray<FTimerHandle> CacheCleanupTimers;

    // Performance optimization settings
    struct FPathfindingPerformanceSettings
    {
        int32 MaxSearchNodes = 2048;
        bool bHierarchicalEnabled = true;
        int32 CacheSize = 100;
        float UpdateFrequency = 0.1f;
    } PerformanceSettings;
};
