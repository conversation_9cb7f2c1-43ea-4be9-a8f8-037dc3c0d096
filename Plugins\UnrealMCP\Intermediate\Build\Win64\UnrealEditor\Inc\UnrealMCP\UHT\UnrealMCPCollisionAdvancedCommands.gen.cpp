// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Commands/UnrealMCPCollisionAdvancedCommands.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeUnrealMCPCollisionAdvancedCommands() {}

// ********** Begin Cross Module References ********************************************************
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FTransform();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UBodySetup_NoRegister();
ENGINE_API UEnum* Z_Construct_UEnum_Engine_ECollisionEnabled();
ENGINE_API UEnum* Z_Construct_UEnum_Engine_ECollisionResponse();
PHYSICSCORE_API UClass* Z_Construct_UClass_UChaosPhysicalMaterial_NoRegister();
PHYSICSCORE_API UClass* Z_Construct_UClass_UPhysicalMaterial_NoRegister();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_NoRegister();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCollisionConfig();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FChaosPhysicsConfig();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FPreciseCollisionConfig();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FTriggerVolumeConfig();
UPackage* Z_Construct_UPackage__Script_UnrealMCP();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAuracronCollisionConfig ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronCollisionConfig;
class UScriptStruct* FAuracronCollisionConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCollisionConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronCollisionConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronCollisionConfig, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("AuracronCollisionConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCollisionConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron collision configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron collision configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionName_MetaData[] = {
		{ "Category", "AuracronCollisionConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerIndex_MetaData[] = {
		{ "Category", "AuracronCollisionConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionEnabled_MetaData[] = {
		{ "Category", "AuracronCollisionConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionResponse_MetaData[] = {
		{ "Category", "AuracronCollisionConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionProfileName_MetaData[] = {
		{ "Category", "AuracronCollisionConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseComplexAsSimple_MetaData[] = {
		{ "Category", "AuracronCollisionConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseChaosPhysics_MetaData[] = {
		{ "Category", "AuracronCollisionConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateOverlapEvents_MetaData[] = {
		{ "Category", "AuracronCollisionConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicalMaterial_MetaData[] = {
		{ "Category", "AuracronCollisionConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosPhysicalMaterial_MetaData[] = {
		{ "Category", "AuracronCollisionConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LayerIndex;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CollisionEnabled;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CollisionResponse;
	static const UECodeGen_Private::FNamePropertyParams NewProp_CollisionProfileName;
	static void NewProp_bUseComplexAsSimple_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseComplexAsSimple;
	static void NewProp_bUseChaosPhysics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseChaosPhysics;
	static void NewProp_bGenerateOverlapEvents_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateOverlapEvents;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_PhysicalMaterial;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ChaosPhysicalMaterial;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronCollisionConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_CollisionName = { "CollisionName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionConfig, CollisionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionName_MetaData), NewProp_CollisionName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_LayerIndex = { "LayerIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionConfig, LayerIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerIndex_MetaData), NewProp_LayerIndex_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_CollisionEnabled = { "CollisionEnabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionConfig, CollisionEnabled), Z_Construct_UEnum_Engine_ECollisionEnabled, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionEnabled_MetaData), NewProp_CollisionEnabled_MetaData) }; // 2362857466
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_CollisionResponse = { "CollisionResponse", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionConfig, CollisionResponse), Z_Construct_UEnum_Engine_ECollisionResponse, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionResponse_MetaData), NewProp_CollisionResponse_MetaData) }; // 1009580041
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_CollisionProfileName = { "CollisionProfileName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionConfig, CollisionProfileName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionProfileName_MetaData), NewProp_CollisionProfileName_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_bUseComplexAsSimple_SetBit(void* Obj)
{
	((FAuracronCollisionConfig*)Obj)->bUseComplexAsSimple = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_bUseComplexAsSimple = { "bUseComplexAsSimple", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCollisionConfig), &Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_bUseComplexAsSimple_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseComplexAsSimple_MetaData), NewProp_bUseComplexAsSimple_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_bUseChaosPhysics_SetBit(void* Obj)
{
	((FAuracronCollisionConfig*)Obj)->bUseChaosPhysics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_bUseChaosPhysics = { "bUseChaosPhysics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCollisionConfig), &Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_bUseChaosPhysics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseChaosPhysics_MetaData), NewProp_bUseChaosPhysics_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_bGenerateOverlapEvents_SetBit(void* Obj)
{
	((FAuracronCollisionConfig*)Obj)->bGenerateOverlapEvents = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_bGenerateOverlapEvents = { "bGenerateOverlapEvents", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCollisionConfig), &Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_bGenerateOverlapEvents_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateOverlapEvents_MetaData), NewProp_bGenerateOverlapEvents_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_PhysicalMaterial = { "PhysicalMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionConfig, PhysicalMaterial), Z_Construct_UClass_UPhysicalMaterial_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicalMaterial_MetaData), NewProp_PhysicalMaterial_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_ChaosPhysicalMaterial = { "ChaosPhysicalMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCollisionConfig, ChaosPhysicalMaterial), Z_Construct_UClass_UChaosPhysicalMaterial_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosPhysicalMaterial_MetaData), NewProp_ChaosPhysicalMaterial_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_CollisionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_LayerIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_CollisionEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_CollisionResponse,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_CollisionProfileName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_bUseComplexAsSimple,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_bUseChaosPhysics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_bGenerateOverlapEvents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_PhysicalMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewProp_ChaosPhysicalMaterial,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"AuracronCollisionConfig",
	Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::PropPointers),
	sizeof(FAuracronCollisionConfig),
	alignof(FAuracronCollisionConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronCollisionConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCollisionConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronCollisionConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCollisionConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronCollisionConfig ********************************************

// ********** Begin ScriptStruct FPreciseCollisionConfig *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPreciseCollisionConfig;
class UScriptStruct* FPreciseCollisionConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPreciseCollisionConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPreciseCollisionConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPreciseCollisionConfig, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("PreciseCollisionConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FPreciseCollisionConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Precise collision geometry configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Precise collision geometry configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeometryName_MetaData[] = {
		{ "Category", "PreciseCollisionConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeometryType_MetaData[] = {
		{ "Category", "PreciseCollisionConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeometrySize_MetaData[] = {
		{ "Category", "PreciseCollisionConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// box, sphere, capsule, convex, levelset\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "box, sphere, capsule, convex, levelset" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeometryTransform_MetaData[] = {
		{ "Category", "PreciseCollisionConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeometryRadius_MetaData[] = {
		{ "Category", "PreciseCollisionConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeometryHeight_MetaData[] = {
		{ "Category", "PreciseCollisionConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseExperimentalLevelSet_MetaData[] = {
		{ "Category", "PreciseCollisionConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GeometryName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GeometryType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GeometrySize;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GeometryTransform;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GeometryRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GeometryHeight;
	static void NewProp_bUseExperimentalLevelSet_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseExperimentalLevelSet;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPreciseCollisionConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::NewProp_GeometryName = { "GeometryName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPreciseCollisionConfig, GeometryName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeometryName_MetaData), NewProp_GeometryName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::NewProp_GeometryType = { "GeometryType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPreciseCollisionConfig, GeometryType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeometryType_MetaData), NewProp_GeometryType_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::NewProp_GeometrySize = { "GeometrySize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPreciseCollisionConfig, GeometrySize), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeometrySize_MetaData), NewProp_GeometrySize_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::NewProp_GeometryTransform = { "GeometryTransform", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPreciseCollisionConfig, GeometryTransform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeometryTransform_MetaData), NewProp_GeometryTransform_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::NewProp_GeometryRadius = { "GeometryRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPreciseCollisionConfig, GeometryRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeometryRadius_MetaData), NewProp_GeometryRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::NewProp_GeometryHeight = { "GeometryHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPreciseCollisionConfig, GeometryHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeometryHeight_MetaData), NewProp_GeometryHeight_MetaData) };
void Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::NewProp_bUseExperimentalLevelSet_SetBit(void* Obj)
{
	((FPreciseCollisionConfig*)Obj)->bUseExperimentalLevelSet = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::NewProp_bUseExperimentalLevelSet = { "bUseExperimentalLevelSet", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPreciseCollisionConfig), &Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::NewProp_bUseExperimentalLevelSet_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseExperimentalLevelSet_MetaData), NewProp_bUseExperimentalLevelSet_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::NewProp_GeometryName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::NewProp_GeometryType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::NewProp_GeometrySize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::NewProp_GeometryTransform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::NewProp_GeometryRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::NewProp_GeometryHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::NewProp_bUseExperimentalLevelSet,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"PreciseCollisionConfig",
	Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::PropPointers),
	sizeof(FPreciseCollisionConfig),
	alignof(FPreciseCollisionConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPreciseCollisionConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FPreciseCollisionConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPreciseCollisionConfig.InnerSingleton, Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPreciseCollisionConfig.InnerSingleton;
}
// ********** End ScriptStruct FPreciseCollisionConfig *********************************************

// ********** Begin ScriptStruct FChaosPhysicsConfig ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FChaosPhysicsConfig;
class UScriptStruct* FChaosPhysicsConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FChaosPhysicsConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FChaosPhysicsConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FChaosPhysicsConfig, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("ChaosPhysicsConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FChaosPhysicsConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Chaos physics configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Chaos physics configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicsName_MetaData[] = {
		{ "Category", "ChaosPhysicsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Mass_MetaData[] = {
		{ "Category", "ChaosPhysicsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LinearDamping_MetaData[] = {
		{ "Category", "ChaosPhysicsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AngularDamping_MetaData[] = {
		{ "Category", "ChaosPhysicsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableGravity_MetaData[] = {
		{ "Category", "ChaosPhysicsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSimulatePhysics_MetaData[] = {
		{ "Category", "ChaosPhysicsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseChaosFlesh_MetaData[] = {
		{ "Category", "ChaosPhysicsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseChaosMover_MetaData[] = {
		{ "Category", "ChaosPhysicsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PhysicsName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Mass;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LinearDamping;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AngularDamping;
	static void NewProp_bEnableGravity_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableGravity;
	static void NewProp_bSimulatePhysics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSimulatePhysics;
	static void NewProp_bUseChaosFlesh_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseChaosFlesh;
	static void NewProp_bUseChaosMover_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseChaosMover;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FChaosPhysicsConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_PhysicsName = { "PhysicsName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FChaosPhysicsConfig, PhysicsName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicsName_MetaData), NewProp_PhysicsName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_Mass = { "Mass", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FChaosPhysicsConfig, Mass), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Mass_MetaData), NewProp_Mass_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_LinearDamping = { "LinearDamping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FChaosPhysicsConfig, LinearDamping), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LinearDamping_MetaData), NewProp_LinearDamping_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_AngularDamping = { "AngularDamping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FChaosPhysicsConfig, AngularDamping), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AngularDamping_MetaData), NewProp_AngularDamping_MetaData) };
void Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_bEnableGravity_SetBit(void* Obj)
{
	((FChaosPhysicsConfig*)Obj)->bEnableGravity = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_bEnableGravity = { "bEnableGravity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FChaosPhysicsConfig), &Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_bEnableGravity_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableGravity_MetaData), NewProp_bEnableGravity_MetaData) };
void Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_bSimulatePhysics_SetBit(void* Obj)
{
	((FChaosPhysicsConfig*)Obj)->bSimulatePhysics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_bSimulatePhysics = { "bSimulatePhysics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FChaosPhysicsConfig), &Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_bSimulatePhysics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSimulatePhysics_MetaData), NewProp_bSimulatePhysics_MetaData) };
void Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_bUseChaosFlesh_SetBit(void* Obj)
{
	((FChaosPhysicsConfig*)Obj)->bUseChaosFlesh = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_bUseChaosFlesh = { "bUseChaosFlesh", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FChaosPhysicsConfig), &Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_bUseChaosFlesh_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseChaosFlesh_MetaData), NewProp_bUseChaosFlesh_MetaData) };
void Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_bUseChaosMover_SetBit(void* Obj)
{
	((FChaosPhysicsConfig*)Obj)->bUseChaosMover = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_bUseChaosMover = { "bUseChaosMover", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FChaosPhysicsConfig), &Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_bUseChaosMover_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseChaosMover_MetaData), NewProp_bUseChaosMover_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_PhysicsName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_Mass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_LinearDamping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_AngularDamping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_bEnableGravity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_bSimulatePhysics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_bUseChaosFlesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewProp_bUseChaosMover,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"ChaosPhysicsConfig",
	Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::PropPointers),
	sizeof(FChaosPhysicsConfig),
	alignof(FChaosPhysicsConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FChaosPhysicsConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FChaosPhysicsConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FChaosPhysicsConfig.InnerSingleton, Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FChaosPhysicsConfig.InnerSingleton;
}
// ********** End ScriptStruct FChaosPhysicsConfig *************************************************

// ********** Begin ScriptStruct FTriggerVolumeConfig **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FTriggerVolumeConfig;
class UScriptStruct* FTriggerVolumeConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FTriggerVolumeConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FTriggerVolumeConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FTriggerVolumeConfig, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("TriggerVolumeConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FTriggerVolumeConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Trigger volume configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Trigger volume configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TriggerName_MetaData[] = {
		{ "Category", "TriggerVolumeConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TriggerType_MetaData[] = {
		{ "Category", "TriggerVolumeConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TriggerSize_MetaData[] = {
		{ "Category", "TriggerVolumeConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// enter, exit, overlap, touch\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "enter, exit, overlap, touch" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TriggerLocation_MetaData[] = {
		{ "Category", "TriggerVolumeConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTriggerOnlyPlayers_MetaData[] = {
		{ "Category", "TriggerVolumeConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseChaosEvents_MetaData[] = {
		{ "Category", "TriggerVolumeConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TriggerName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TriggerType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TriggerSize;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TriggerLocation;
	static void NewProp_bTriggerOnlyPlayers_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTriggerOnlyPlayers;
	static void NewProp_bUseChaosEvents_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseChaosEvents;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FTriggerVolumeConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::NewProp_TriggerName = { "TriggerName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTriggerVolumeConfig, TriggerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TriggerName_MetaData), NewProp_TriggerName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::NewProp_TriggerType = { "TriggerType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTriggerVolumeConfig, TriggerType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TriggerType_MetaData), NewProp_TriggerType_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::NewProp_TriggerSize = { "TriggerSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTriggerVolumeConfig, TriggerSize), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TriggerSize_MetaData), NewProp_TriggerSize_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::NewProp_TriggerLocation = { "TriggerLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTriggerVolumeConfig, TriggerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TriggerLocation_MetaData), NewProp_TriggerLocation_MetaData) };
void Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::NewProp_bTriggerOnlyPlayers_SetBit(void* Obj)
{
	((FTriggerVolumeConfig*)Obj)->bTriggerOnlyPlayers = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::NewProp_bTriggerOnlyPlayers = { "bTriggerOnlyPlayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTriggerVolumeConfig), &Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::NewProp_bTriggerOnlyPlayers_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTriggerOnlyPlayers_MetaData), NewProp_bTriggerOnlyPlayers_MetaData) };
void Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::NewProp_bUseChaosEvents_SetBit(void* Obj)
{
	((FTriggerVolumeConfig*)Obj)->bUseChaosEvents = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::NewProp_bUseChaosEvents = { "bUseChaosEvents", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTriggerVolumeConfig), &Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::NewProp_bUseChaosEvents_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseChaosEvents_MetaData), NewProp_bUseChaosEvents_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::NewProp_TriggerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::NewProp_TriggerType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::NewProp_TriggerSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::NewProp_TriggerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::NewProp_bTriggerOnlyPlayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::NewProp_bUseChaosEvents,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"TriggerVolumeConfig",
	Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::PropPointers),
	sizeof(FTriggerVolumeConfig),
	alignof(FTriggerVolumeConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FTriggerVolumeConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FTriggerVolumeConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FTriggerVolumeConfig.InnerSingleton, Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FTriggerVolumeConfig.InnerSingleton;
}
// ********** End ScriptStruct FTriggerVolumeConfig ************************************************

// ********** Begin Class UUnrealMCPCollisionAdvancedCommands **************************************
void UUnrealMCPCollisionAdvancedCommands::StaticRegisterNativesUUnrealMCPCollisionAdvancedCommands()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UUnrealMCPCollisionAdvancedCommands;
UClass* UUnrealMCPCollisionAdvancedCommands::GetPrivateStaticClass()
{
	using TClass = UUnrealMCPCollisionAdvancedCommands;
	if (!Z_Registration_Info_UClass_UUnrealMCPCollisionAdvancedCommands.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("UnrealMCPCollisionAdvancedCommands"),
			Z_Registration_Info_UClass_UUnrealMCPCollisionAdvancedCommands.InnerSingleton,
			StaticRegisterNativesUUnrealMCPCollisionAdvancedCommands,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UUnrealMCPCollisionAdvancedCommands.InnerSingleton;
}
UClass* Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_NoRegister()
{
	return UUnrealMCPCollisionAdvancedCommands::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * UnrealMCP Collision Advanced Commands - Modern UE 5.6.1 Implementation\n * Handles advanced collision and physics configuration with Chaos Physics integration\n */" },
#endif
		{ "IncludePath", "Commands/UnrealMCPCollisionAdvancedCommands.h" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "UnrealMCP Collision Advanced Commands - Modern UE 5.6.1 Implementation\nHandles advanced collision and physics configuration with Chaos Physics integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionGeometries_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for created collision geometries\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for created collision geometries" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicsMaterials_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for physics materials\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for physics materials" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosPhysicsMaterials_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for Chaos physics materials\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for Chaos physics materials" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TriggerVolumes_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for trigger volumes\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPCollisionAdvancedCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for trigger volumes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CollisionGeometries_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollisionGeometries_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CollisionGeometries;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PhysicsMaterials_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PhysicsMaterials_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PhysicsMaterials;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ChaosPhysicsMaterials_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChaosPhysicsMaterials_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ChaosPhysicsMaterials;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TriggerVolumes_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TriggerVolumes_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_TriggerVolumes;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UUnrealMCPCollisionAdvancedCommands>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_CollisionGeometries_ValueProp = { "CollisionGeometries", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UBodySetup_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_CollisionGeometries_Key_KeyProp = { "CollisionGeometries_Key", nullptr, (EPropertyFlags)0x0100000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_CollisionGeometries = { "CollisionGeometries", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPCollisionAdvancedCommands, CollisionGeometries), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionGeometries_MetaData), NewProp_CollisionGeometries_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_PhysicsMaterials_ValueProp = { "PhysicsMaterials", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UPhysicalMaterial_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_PhysicsMaterials_Key_KeyProp = { "PhysicsMaterials_Key", nullptr, (EPropertyFlags)0x0100000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_PhysicsMaterials = { "PhysicsMaterials", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPCollisionAdvancedCommands, PhysicsMaterials), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicsMaterials_MetaData), NewProp_PhysicsMaterials_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_ChaosPhysicsMaterials_ValueProp = { "ChaosPhysicsMaterials", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UChaosPhysicalMaterial_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_ChaosPhysicsMaterials_Key_KeyProp = { "ChaosPhysicsMaterials_Key", nullptr, (EPropertyFlags)0x0100000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_ChaosPhysicsMaterials = { "ChaosPhysicsMaterials", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPCollisionAdvancedCommands, ChaosPhysicsMaterials), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosPhysicsMaterials_MetaData), NewProp_ChaosPhysicsMaterials_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_TriggerVolumes_ValueProp = { "TriggerVolumes", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_TriggerVolumes_Key_KeyProp = { "TriggerVolumes_Key", nullptr, (EPropertyFlags)0x0100000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_TriggerVolumes = { "TriggerVolumes", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPCollisionAdvancedCommands, TriggerVolumes), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TriggerVolumes_MetaData), NewProp_TriggerVolumes_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_CollisionGeometries_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_CollisionGeometries_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_CollisionGeometries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_PhysicsMaterials_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_PhysicsMaterials_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_PhysicsMaterials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_ChaosPhysicsMaterials_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_ChaosPhysicsMaterials_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_ChaosPhysicsMaterials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_TriggerVolumes_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_TriggerVolumes_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::NewProp_TriggerVolumes,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::ClassParams = {
	&UUnrealMCPCollisionAdvancedCommands::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::Class_MetaDataParams), Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands()
{
	if (!Z_Registration_Info_UClass_UUnrealMCPCollisionAdvancedCommands.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UUnrealMCPCollisionAdvancedCommands.OuterSingleton, Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UUnrealMCPCollisionAdvancedCommands.OuterSingleton;
}
UUnrealMCPCollisionAdvancedCommands::UUnrealMCPCollisionAdvancedCommands(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UUnrealMCPCollisionAdvancedCommands);
UUnrealMCPCollisionAdvancedCommands::~UUnrealMCPCollisionAdvancedCommands() {}
// ********** End Class UUnrealMCPCollisionAdvancedCommands ****************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCollisionAdvancedCommands_h__Script_UnrealMCP_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronCollisionConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics::NewStructOps, TEXT("AuracronCollisionConfig"), &Z_Registration_Info_UScriptStruct_FAuracronCollisionConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronCollisionConfig), 1869925052U) },
		{ FPreciseCollisionConfig::StaticStruct, Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics::NewStructOps, TEXT("PreciseCollisionConfig"), &Z_Registration_Info_UScriptStruct_FPreciseCollisionConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPreciseCollisionConfig), 315697968U) },
		{ FChaosPhysicsConfig::StaticStruct, Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics::NewStructOps, TEXT("ChaosPhysicsConfig"), &Z_Registration_Info_UScriptStruct_FChaosPhysicsConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FChaosPhysicsConfig), 4256658754U) },
		{ FTriggerVolumeConfig::StaticStruct, Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics::NewStructOps, TEXT("TriggerVolumeConfig"), &Z_Registration_Info_UScriptStruct_FTriggerVolumeConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FTriggerVolumeConfig), 1755993704U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands, UUnrealMCPCollisionAdvancedCommands::StaticClass, TEXT("UUnrealMCPCollisionAdvancedCommands"), &Z_Registration_Info_UClass_UUnrealMCPCollisionAdvancedCommands, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UUnrealMCPCollisionAdvancedCommands), 2657438715U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCollisionAdvancedCommands_h__Script_UnrealMCP_3381671310(TEXT("/Script/UnrealMCP"),
	Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCollisionAdvancedCommands_h__Script_UnrealMCP_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCollisionAdvancedCommands_h__Script_UnrealMCP_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCollisionAdvancedCommands_h__Script_UnrealMCP_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCollisionAdvancedCommands_h__Script_UnrealMCP_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
