/NOLOGO
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/DEF
/NAME:"UnrealEditor-UnrealMCP.dll"
/IGNORE:4221
/NODEFAULTLIB
"C:/Game/AURACRON/Intermediate/Build/Win64/x64/AURACRONEditor/Development/UnrealEd/SharedPCH.UnrealEd.Project.ValApi.ValExpApi.Cpp20.h.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/Module.UnrealMCP.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/MCPServerRunnable.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPBridge.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPModule.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPAnalyticsCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPArchitectureCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPBalanceCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPBlueprintCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPBlueprintNodeCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPCollisionAdvancedCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPCollisionCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPCommonUtils.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPEditorCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPLandscapeCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPMapCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPMaterialCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPMOBACommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPPathfindingCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPProceduralMeshCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPProjectCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPUMGCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPVisionCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPVisualEffectsCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/Default.rc2.res"
/OUT:"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealEditor-UnrealMCP.lib"