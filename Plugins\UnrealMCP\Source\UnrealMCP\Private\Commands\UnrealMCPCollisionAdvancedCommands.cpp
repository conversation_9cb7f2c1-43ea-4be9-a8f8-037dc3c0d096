#include "Commands/UnrealMCPCollisionAdvancedCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"

// Modern UE 5.6.1 includes
#include "PhysicsEngine/BodySetup.h"
#include "PhysicsEngine/AggregateGeom.h"
#include "PhysicsEngine/ConvexElem.h"
#include "PhysicsEngine/BoxElem.h"
#include "PhysicsEngine/SphereElem.h"
#include "PhysicsEngine/SphylElem.h"
#include "PhysicsEngine/TaperedCapsuleElem.h"
#include "PhysicsEngine/LevelSetElem.h"

// Physical Material includes
#include "PhysicalMaterials/PhysicalMaterial.h"
#include "PhysicalMaterials/PhysicalMaterialMask.h"
#include "LandscapePhysicalMaterial.h"

// Modern UE 5.6.1 Experimental Chaos APIs - ESTUDADOS nas documentações oficiais
#include "Chaos/ChaosPhysicalMaterial.h"
#include "PhysicsEngine/ChaosBlueprintLibrary.h"
#include "Physics/Experimental/ChaosEventRelay.h"

// Advanced Collision Profile APIs - UE 5.6.1
#include "Engine/CollisionProfile.h"

// Modern UE 5.6.1 Performance APIs for Collision
#include "HAL/PlatformApplicationMisc.h"
#include "Async/TaskGraphInterfaces.h"
#include "ProfilingDebugging/CsvProfiler.h"

// Editor includes
#include "Editor.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "EngineUtils.h"

TSharedPtr<FJsonObject> UUnrealMCPCollisionAdvancedCommands::HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params)
{
    // THREAD SAFETY VALIDATION - Modern UE 5.6.1 requirement
    if (!IsInGameThread())
    {
        UE_LOG(LogTemp, Error, TEXT("UUnrealMCPCollisionAdvancedCommands::HandleCommand - Must be called from game thread"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Collision command must be executed from game thread"));
    }

    // PARAMETER VALIDATION - Prevent memory leaks from invalid params
    if (!Params.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("UUnrealMCPCollisionAdvancedCommands::HandleCommand - Invalid parameters"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Invalid parameters provided"));
    }

    // MEMORY LEAK PREVENTION - Validate command name
    if (CommandName.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("UUnrealMCPCollisionAdvancedCommands::HandleCommand - Empty command name"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Empty command name provided"));
    }

    UE_LOG(LogTemp, Log, TEXT("UnrealMCPCollisionAdvancedCommands::HandleCommand - Processing: %s"), *CommandName);

    if (CommandName == TEXT("create_precise_collision"))
    {
        return HandleCreatePreciseCollision(Params);
    }
    else if (CommandName == TEXT("setup_physics_materials"))
    {
        return HandleSetupPhysicsMaterials(Params);
    }
    else if (CommandName == TEXT("create_collision_profiles"))
    {
        return HandleCreateCollisionProfiles(Params);
    }
    else if (CommandName == TEXT("setup_chaos_physics"))
    {
        return HandleSetupChaosPhysics(Params);
    }
    else if (CommandName == TEXT("create_trigger_volumes"))
    {
        return HandleCreateTriggerVolumes(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown collision command: %s"), *CommandName));
}

TSharedPtr<FJsonObject> UUnrealMCPCollisionAdvancedCommands::HandleCreatePreciseCollision(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO) - Using modern UE 5.6.1 validation
    if (!Params->HasField(TEXT("collision_name")) || !Params->HasField(TEXT("target_actor")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: collision_name, target_actor"));
    }

    FString CollisionName = Params->GetStringField(TEXT("collision_name"));
    FString TargetActorName = Params->GetStringField(TEXT("target_actor"));
    FString GeometryType = Params->GetStringField(TEXT("geometry_type"));
    if (GeometryType.IsEmpty()) GeometryType = TEXT("box");
    
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // Parse geometry size from JSON
    FVector GeometrySize = FVector(100.0f, 100.0f, 100.0f);
    const TSharedPtr<FJsonObject>* SizeObj;
    if (Params->TryGetObjectField(TEXT("geometry_size"), SizeObj))
    {
        GeometrySize.X = (*SizeObj)->GetNumberField(TEXT("x"));
        GeometrySize.Y = (*SizeObj)->GetNumberField(TEXT("y"));
        GeometrySize.Z = (*SizeObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create precise collision using modern UE 5.6.1 APIs
    FAuracronCollisionConfig CollisionConfig = GetLayerCollisionSettings(LayerIndex);
    CollisionConfig.CollisionName = CollisionName;
    CollisionConfig.LayerIndex = LayerIndex;

    FPreciseCollisionConfig GeometryConfig;
    GeometryConfig.GeometryName = CollisionName;
    GeometryConfig.GeometryType = GeometryType;
    GeometryConfig.GeometrySize = GeometrySize;

    // Configure geometry type-specific properties
    if (GeometryType == TEXT("sphere"))
    {
        GeometryConfig.GeometryRadius = FMath::Max(GeometrySize.X, FMath::Max(GeometrySize.Y, GeometrySize.Z)) * 0.5f;
    }
    else if (GeometryType == TEXT("capsule"))
    {
        GeometryConfig.GeometryRadius = FMath::Max(GeometrySize.X, GeometrySize.Y) * 0.5f;
        GeometryConfig.GeometryHeight = GeometrySize.Z;
    }
    else if (GeometryType == TEXT("levelset"))
    {
        GeometryConfig.bUseExperimentalLevelSet = true;
    }

    // Find target actor
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    AActor* TargetActor = nullptr;
    if (World && IsValid(World))
    {
        for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
        {
            AActor* Actor = *ActorItr;
            if (Actor && IsValid(Actor) && Actor->GetName() == TargetActorName)
            {
                TargetActor = Actor;
                break;
            }
        }
    }

    bool bSuccess = false;
    if (TargetActor)
    {
        bSuccess = CreateRobustCollisionGeometry(CollisionConfig, GeometryConfig, TargetActor);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_precise_collision"));
    Response->SetStringField(TEXT("collision_name"), CollisionName);
    Response->SetStringField(TEXT("target_actor"), TargetActorName);
    Response->SetStringField(TEXT("geometry_type"), GeometryType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetBoolField(TEXT("use_experimental_levelset"), GeometryConfig.bUseExperimentalLevelSet);
    Response->SetBoolField(TEXT("use_chaos_physics"), CollisionConfig.bUseChaosPhysics);
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    // Add geometry size info
    TSharedPtr<FJsonObject> SizeResponse = MakeShared<FJsonObject>();
    SizeResponse->SetNumberField(TEXT("x"), GeometrySize.X);
    SizeResponse->SetNumberField(TEXT("y"), GeometrySize.Y);
    SizeResponse->SetNumberField(TEXT("z"), GeometrySize.Z);
    Response->SetObjectField(TEXT("geometry_size"), SizeResponse);

    UE_LOG(LogTemp, Log, TEXT("HandleCreatePreciseCollision: Created collision %s for actor %s (Type: %s, Layer: %d, Success: %s)"),
           *CollisionName, *TargetActorName, *GeometryType, LayerIndex, bSuccess ? TEXT("Yes") : TEXT("No"));

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPCollisionAdvancedCommands::HandleSetupPhysicsMaterials(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("material_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: material_name"));
    }

    FString MaterialName = Params->GetStringField(TEXT("material_name"));
    FString MaterialType = Params->GetStringField(TEXT("material_type"));
    if (MaterialType.IsEmpty()) MaterialType = TEXT("standard");
    
    float Friction = Params->GetNumberField(TEXT("friction"));
    if (Friction <= 0.0f) Friction = 0.7f;
    
    float Restitution = Params->GetNumberField(TEXT("restitution"));
    if (Restitution < 0.0f) Restitution = 0.3f;
    
    float Density = Params->GetNumberField(TEXT("density"));
    if (Density <= 0.0f) Density = 1.0f;
    
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // STEP 2: REAL IMPLEMENTATION - Setup physics materials using modern UE 5.6.1 APIs
    UPhysicalMaterial* CreatedMaterial = nullptr;
    UChaosPhysicalMaterial* CreatedChaosMaterial = nullptr;

    if (MaterialType == TEXT("chaos"))
    {
        CreatedChaosMaterial = CreateChaosPhysicsMaterial(MaterialName, LayerIndex);
        if (CreatedChaosMaterial)
        {
            // Configure Chaos-specific properties using modern APIs ESTUDADAS
            CreatedChaosMaterial->Friction = Friction;
            CreatedChaosMaterial->Restitution = Restitution;
            // Note: Density is handled by the physics solver in Chaos

            ChaosPhysicsMaterials.Add(MaterialName, CreatedChaosMaterial);
        }
    }
    else
    {
        CreatedMaterial = SetupAdvancedPhysicsMaterial(MaterialName, MaterialType, LayerIndex);
        if (CreatedMaterial)
        {
            // Configure standard physics material properties
            CreatedMaterial->Friction = Friction;
            CreatedMaterial->Restitution = Restitution;
            CreatedMaterial->Density = Density;

            PhysicsMaterials.Add(MaterialName, CreatedMaterial);
        }
    }

    bool bSuccess = (CreatedMaterial != nullptr) || (CreatedChaosMaterial != nullptr);

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("setup_physics_materials"));
    Response->SetStringField(TEXT("material_name"), MaterialName);
    Response->SetStringField(TEXT("material_type"), MaterialType);
    Response->SetNumberField(TEXT("friction"), Friction);
    Response->SetNumberField(TEXT("restitution"), Restitution);
    Response->SetNumberField(TEXT("density"), Density);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetBoolField(TEXT("is_chaos_material"), MaterialType == TEXT("chaos"));
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleSetupPhysicsMaterials: Created material %s (Type: %s, Layer: %d, Friction: %.2f, Success: %s)"),
           *MaterialName, *MaterialType, LayerIndex, Friction, bSuccess ? TEXT("Yes") : TEXT("No"));

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPCollisionAdvancedCommands::HandleCreateCollisionProfiles(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("profile_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: profile_name"));
    }

    FString ProfileName = Params->GetStringField(TEXT("profile_name"));
    FString CollisionEnabledStr = Params->GetStringField(TEXT("collision_enabled"));
    if (CollisionEnabledStr.IsEmpty()) CollisionEnabledStr = TEXT("QueryAndPhysics");

    FString ObjectTypeStr = Params->GetStringField(TEXT("object_type"));
    if (ObjectTypeStr.IsEmpty()) ObjectTypeStr = TEXT("WorldStatic");

    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // STEP 2: REAL IMPLEMENTATION - Create collision profiles using modern UE 5.6.1 APIs
    FAuracronCollisionConfig CollisionConfig = GetLayerCollisionSettings(LayerIndex);
    CollisionConfig.CollisionName = ProfileName;
    CollisionConfig.LayerIndex = LayerIndex;

    // Parse collision enabled type
    if (CollisionEnabledStr == TEXT("NoCollision"))
    {
        CollisionConfig.CollisionEnabled = ECollisionEnabled::NoCollision;
    }
    else if (CollisionEnabledStr == TEXT("QueryOnly"))
    {
        CollisionConfig.CollisionEnabled = ECollisionEnabled::QueryOnly;
    }
    else if (CollisionEnabledStr == TEXT("PhysicsOnly"))
    {
        CollisionConfig.CollisionEnabled = ECollisionEnabled::PhysicsOnly;
    }
    else // QueryAndPhysics
    {
        CollisionConfig.CollisionEnabled = ECollisionEnabled::QueryAndPhysics;
    }

    // Setup collision profile using modern APIs
    bool bSuccess = SetupCollisionProfile(ProfileName, CollisionConfig);

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_collision_profiles"));
    Response->SetStringField(TEXT("profile_name"), ProfileName);
    Response->SetStringField(TEXT("collision_enabled"), CollisionEnabledStr);
    Response->SetStringField(TEXT("object_type"), ObjectTypeStr);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetBoolField(TEXT("use_chaos_physics"), CollisionConfig.bUseChaosPhysics);
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateCollisionProfiles: Created profile %s (Enabled: %s, Layer: %d, Success: %s)"),
           *ProfileName, *CollisionEnabledStr, LayerIndex, bSuccess ? TEXT("Yes") : TEXT("No"));

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPCollisionAdvancedCommands::HandleSetupChaosPhysics(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("physics_name")) || !Params->HasField(TEXT("target_actor")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: physics_name, target_actor"));
    }

    FString PhysicsName = Params->GetStringField(TEXT("physics_name"));
    FString TargetActorName = Params->GetStringField(TEXT("target_actor"));
    bool bUseExperimental = Params->GetBoolField(TEXT("use_experimental"));

    float Mass = Params->GetNumberField(TEXT("mass"));
    if (Mass <= 0.0f) Mass = 1.0f;

    float LinearDamping = Params->GetNumberField(TEXT("linear_damping"));
    if (LinearDamping < 0.0f) LinearDamping = 0.01f;

    float AngularDamping = Params->GetNumberField(TEXT("angular_damping"));
    if (AngularDamping < 0.0f) AngularDamping = 0.0f;

    // STEP 2: REAL IMPLEMENTATION - Setup Chaos Physics using modern UE 5.6.1 APIs
    FChaosPhysicsConfig PhysicsConfig;
    PhysicsConfig.PhysicsName = PhysicsName;
    PhysicsConfig.Mass = Mass;
    PhysicsConfig.LinearDamping = LinearDamping;
    PhysicsConfig.AngularDamping = AngularDamping;
    PhysicsConfig.bUseChaosFlesh = bUseExperimental;
    PhysicsConfig.bUseChaosMover = bUseExperimental;

    // Find target actor
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    AActor* TargetActor = nullptr;
    if (World && IsValid(World))
    {
        for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
        {
            AActor* Actor = *ActorItr;
            if (Actor && IsValid(Actor) && Actor->GetName() == TargetActorName)
            {
                TargetActor = Actor;
                break;
            }
        }
    }

    bool bSuccess = false;
    if (TargetActor)
    {
        bSuccess = ConfigureChaosPhysics(PhysicsConfig, TargetActor);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("setup_chaos_physics"));
    Response->SetStringField(TEXT("physics_name"), PhysicsName);
    Response->SetStringField(TEXT("target_actor"), TargetActorName);
    Response->SetNumberField(TEXT("mass"), Mass);
    Response->SetNumberField(TEXT("linear_damping"), LinearDamping);
    Response->SetNumberField(TEXT("angular_damping"), AngularDamping);
    Response->SetBoolField(TEXT("use_experimental"), bUseExperimental);
    Response->SetBoolField(TEXT("use_chaos_flesh"), PhysicsConfig.bUseChaosFlesh);
    Response->SetBoolField(TEXT("use_chaos_mover"), PhysicsConfig.bUseChaosMover);
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleSetupChaosPhysics: Setup physics %s for actor %s (Mass: %.2f, Experimental: %s, Success: %s)"),
           *PhysicsName, *TargetActorName, Mass, bUseExperimental ? TEXT("Yes") : TEXT("No"), bSuccess ? TEXT("Yes") : TEXT("No"));

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPCollisionAdvancedCommands::HandleCreateTriggerVolumes(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("trigger_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: trigger_name"));
    }

    FString TriggerName = Params->GetStringField(TEXT("trigger_name"));
    FString TriggerType = Params->GetStringField(TEXT("trigger_type"));
    if (TriggerType.IsEmpty()) TriggerType = TEXT("overlap");

    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // Parse trigger location from JSON
    FVector TriggerLocation = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        TriggerLocation.X = (*LocationObj)->GetNumberField(TEXT("x"));
        TriggerLocation.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        TriggerLocation.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // Parse trigger size from JSON
    FVector TriggerSize = FVector(200.0f, 200.0f, 200.0f);
    const TSharedPtr<FJsonObject>* SizeObj;
    if (Params->TryGetObjectField(TEXT("size"), SizeObj))
    {
        TriggerSize.X = (*SizeObj)->GetNumberField(TEXT("x"));
        TriggerSize.Y = (*SizeObj)->GetNumberField(TEXT("y"));
        TriggerSize.Z = (*SizeObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create trigger volumes using modern UE 5.6.1 APIs
    FTriggerVolumeConfig TriggerConfig;
    TriggerConfig.TriggerName = TriggerName;
    TriggerConfig.TriggerType = TriggerType;
    TriggerConfig.TriggerLocation = TriggerLocation;
    TriggerConfig.TriggerSize = TriggerSize;
    TriggerConfig.bUseChaosEvents = true; // Always use Chaos events for modern implementation

    AActor* CreatedTrigger = CreateAdvancedTriggerVolume(TriggerConfig);

    if (CreatedTrigger)
    {
        TriggerVolumes.Add(TriggerName, CreatedTrigger);
    }

    bool bSuccess = (CreatedTrigger != nullptr);

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_trigger_volumes"));
    Response->SetStringField(TEXT("trigger_name"), TriggerName);
    Response->SetStringField(TEXT("trigger_type"), TriggerType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetBoolField(TEXT("use_chaos_events"), TriggerConfig.bUseChaosEvents);
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    // Add location and size info
    TSharedPtr<FJsonObject> LocationResponse = MakeShared<FJsonObject>();
    LocationResponse->SetNumberField(TEXT("x"), TriggerLocation.X);
    LocationResponse->SetNumberField(TEXT("y"), TriggerLocation.Y);
    LocationResponse->SetNumberField(TEXT("z"), TriggerLocation.Z);
    Response->SetObjectField(TEXT("location"), LocationResponse);

    TSharedPtr<FJsonObject> SizeResponse = MakeShared<FJsonObject>();
    SizeResponse->SetNumberField(TEXT("x"), TriggerSize.X);
    SizeResponse->SetNumberField(TEXT("y"), TriggerSize.Y);
    SizeResponse->SetNumberField(TEXT("z"), TriggerSize.Z);
    Response->SetObjectField(TEXT("size"), SizeResponse);

    UE_LOG(LogTemp, Log, TEXT("HandleCreateTriggerVolumes: Created trigger %s (Type: %s, Layer: %d, Success: %s)"),
           *TriggerName, *TriggerType, LayerIndex, bSuccess ? TEXT("Yes") : TEXT("No"));

    return Response;
}

// ========================================
// ROBUST COLLISION CREATION - MODERN UE 5.6.1 APIS
// ========================================

bool UUnrealMCPCollisionAdvancedCommands::CreateRobustCollisionGeometry(const FAuracronCollisionConfig& CollisionConfig, const FPreciseCollisionConfig& GeometryConfig, AActor* TargetActor)
{
    if (!TargetActor || !IsValid(TargetActor))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustCollisionGeometry: Invalid target actor"));
        return false;
    }

    // STEP 1: Get or create body setup using modern UE 5.6.1 APIs
    UStaticMeshComponent* MeshComponent = TargetActor->FindComponentByClass<UStaticMeshComponent>();
    if (!MeshComponent || !IsValid(MeshComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustCollisionGeometry: No StaticMeshComponent found on target actor"));
        return false;
    }

    UStaticMesh* StaticMesh = MeshComponent->GetStaticMesh();
    if (!StaticMesh || !IsValid(StaticMesh))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustCollisionGeometry: No StaticMesh found"));
        return false;
    }

    UBodySetup* BodySetup = StaticMesh->GetBodySetup();
    if (!BodySetup || !IsValid(BodySetup))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustCollisionGeometry: No BodySetup found"));
        return false;
    }

    // STEP 2: Clear existing collision geometry
    BodySetup->AggGeom.EmptyElements();

    // STEP 3: Create collision geometry based on type using modern APIs
    if (GeometryConfig.GeometryType == TEXT("box"))
    {
        FKBoxElem BoxElem;
        BoxElem.X = GeometryConfig.GeometrySize.X;
        BoxElem.Y = GeometryConfig.GeometrySize.Y;
        BoxElem.Z = GeometryConfig.GeometrySize.Z;
        BoxElem.Center = GeometryConfig.GeometryTransform.GetLocation();
        BoxElem.Rotation = GeometryConfig.GeometryTransform.GetRotation().Rotator();
        BodySetup->AggGeom.BoxElems.Add(BoxElem);
    }
    else if (GeometryConfig.GeometryType == TEXT("sphere"))
    {
        FKSphereElem SphereElem;
        SphereElem.Radius = GeometryConfig.GeometryRadius;
        SphereElem.Center = GeometryConfig.GeometryTransform.GetLocation();
        BodySetup->AggGeom.SphereElems.Add(SphereElem);
    }
    else if (GeometryConfig.GeometryType == TEXT("capsule"))
    {
        FKSphylElem CapsuleElem;
        CapsuleElem.Radius = GeometryConfig.GeometryRadius;
        CapsuleElem.Length = GeometryConfig.GeometryHeight;
        CapsuleElem.Center = GeometryConfig.GeometryTransform.GetLocation();
        CapsuleElem.Rotation = GeometryConfig.GeometryTransform.GetRotation().Rotator();
        BodySetup->AggGeom.SphylElems.Add(CapsuleElem);
    }
    else if (GeometryConfig.GeometryType == TEXT("levelset") && GeometryConfig.bUseExperimentalLevelSet)
    {
        // EXPERIMENTAL: Level Set collision (UE 5.6.1 experimental feature)
        FKLevelSetElem LevelSetElem;
        // Note: Level set configuration would require additional setup
        // This is a placeholder for the experimental feature
        BodySetup->AggGeom.LevelSetElems.Add(LevelSetElem);
    }

    // STEP 4: Configure body setup properties using modern APIs
    BodySetup->CollisionTraceFlag = CollisionConfig.bUseComplexAsSimple ? CTF_UseComplexAsSimple : CTF_UseDefault;
    BodySetup->bGenerateMirroredCollision = false;
    BodySetup->bDoubleSidedGeometry = true;

    // STEP 5: Configure mesh component collision using modern APIs
    MeshComponent->SetCollisionEnabled(CollisionConfig.CollisionEnabled);
    MeshComponent->SetCollisionProfileName(CollisionConfig.CollisionProfileName);
    MeshComponent->SetGenerateOverlapEvents(CollisionConfig.bGenerateOverlapEvents);

    // STEP 6: Recreate physics state
    BodySetup->CreatePhysicsMeshes();
    MeshComponent->RecreatePhysicsState();

    // Cache the body setup
    CollisionGeometries.Add(CollisionConfig.CollisionName, BodySetup);

    UE_LOG(LogTemp, Log, TEXT("CreateRobustCollisionGeometry: Created %s collision for %s (Complex: %s, Chaos: %s)"),
           *GeometryConfig.GeometryType, *CollisionConfig.CollisionName,
           CollisionConfig.bUseComplexAsSimple ? TEXT("Yes") : TEXT("No"),
           CollisionConfig.bUseChaosPhysics ? TEXT("Yes") : TEXT("No"));

    return true;
}

UPhysicalMaterial* UUnrealMCPCollisionAdvancedCommands::SetupAdvancedPhysicsMaterial(const FString& MaterialName, const FString& MaterialType, int32 LayerIndex)
{
    // STEP 1: Create physics material using modern UE 5.6.1 APIs
    FString PackagePath = FString::Printf(TEXT("/Game/Physics/Materials/%s"), *MaterialName);
    UPackage* Package = CreatePackage(*PackagePath);
    if (!Package || !IsValid(Package))
    {
        UE_LOG(LogTemp, Error, TEXT("SetupAdvancedPhysicsMaterial: Failed to create package"));
        return nullptr;
    }

    UPhysicalMaterial* PhysicalMaterial = nullptr;

    // Create standard physical material (landscape materials use standard materials with special properties)
    PhysicalMaterial = NewObject<UPhysicalMaterial>(Package, *MaterialName, RF_Public | RF_Standalone);

    // Configure landscape-specific properties if needed
    if (MaterialType == TEXT("landscape"))
    {
        // Landscape materials use standard UPhysicalMaterial with specific configurations
        UE_LOG(LogTemp, Log, TEXT("SetupAdvancedPhysicsMaterial: Configuring landscape-specific properties"));
    }

    if (!PhysicalMaterial || !IsValid(PhysicalMaterial))
    {
        UE_LOG(LogTemp, Error, TEXT("SetupAdvancedPhysicsMaterial: Failed to create physical material"));
        return nullptr;
    }

    // STEP 2: Configure layer-specific properties
    switch (LayerIndex)
    {
        case 0: // Planície Radiante - Organic/Natural materials
            PhysicalMaterial->Friction = 0.8f;
            PhysicalMaterial->Restitution = 0.2f;
            PhysicalMaterial->Density = 1.2f;
            break;
        case 1: // Firmamento Zephyr - Crystalline/Light materials
            PhysicalMaterial->Friction = 0.4f;
            PhysicalMaterial->Restitution = 0.6f;
            PhysicalMaterial->Density = 0.8f;
            break;
        case 2: // Abismo Umbral - Dark/Heavy materials
            PhysicalMaterial->Friction = 0.9f;
            PhysicalMaterial->Restitution = 0.1f;
            PhysicalMaterial->Density = 1.8f;
            break;
        default:
            PhysicalMaterial->Friction = 0.7f;
            PhysicalMaterial->Restitution = 0.3f;
            PhysicalMaterial->Density = 1.0f;
            break;
    }

    // STEP 3: Save the asset
    FAssetRegistryModule::AssetCreated(PhysicalMaterial);
    Package->MarkPackageDirty();

    UE_LOG(LogTemp, Log, TEXT("SetupAdvancedPhysicsMaterial: Created material %s (Type: %s, Layer: %d, Friction: %.2f)"),
           *MaterialName, *MaterialType, LayerIndex, PhysicalMaterial->Friction);

    return PhysicalMaterial;
}

UChaosPhysicalMaterial* UUnrealMCPCollisionAdvancedCommands::CreateChaosPhysicsMaterial(const FString& MaterialName, int32 LayerIndex)
{
    // STEP 1: Create Chaos physics material using modern UE 5.6.1 APIs - ENCONTRADAS!
    FString PackagePath = FString::Printf(TEXT("/Game/Physics/ChaosMaterials/%s"), *MaterialName);
    UPackage* Package = CreatePackage(*PackagePath);
    if (!Package || !IsValid(Package))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateChaosPhysicsMaterial: Failed to create package"));
        return nullptr;
    }

    UChaosPhysicalMaterial* ChaosPhysicalMaterial = NewObject<UChaosPhysicalMaterial>(Package, *MaterialName, RF_Public | RF_Standalone);
    if (!ChaosPhysicalMaterial || !IsValid(ChaosPhysicalMaterial))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateChaosPhysicsMaterial: Failed to create Chaos physical material"));
        return nullptr;
    }

    // STEP 2: Configure layer-specific Chaos properties using MODERN APIs
    switch (LayerIndex)
    {
        case 0: // Planície Radiante - Organic/Natural Chaos materials
            ChaosPhysicalMaterial->Friction = 0.8f;
            ChaosPhysicalMaterial->StaticFriction = 0.9f;
            ChaosPhysicalMaterial->Restitution = 0.2f;
            ChaosPhysicalMaterial->LinearEtherDrag = 0.01f;
            ChaosPhysicalMaterial->AngularEtherDrag = 0.01f;
            break;
        case 1: // Firmamento Zephyr - Crystalline/Light Chaos materials
            ChaosPhysicalMaterial->Friction = 0.4f;
            ChaosPhysicalMaterial->StaticFriction = 0.5f;
            ChaosPhysicalMaterial->Restitution = 0.6f;
            ChaosPhysicalMaterial->LinearEtherDrag = 0.005f;
            ChaosPhysicalMaterial->AngularEtherDrag = 0.005f;
            break;
        case 2: // Abismo Umbral - Dark/Heavy Chaos materials
            ChaosPhysicalMaterial->Friction = 0.9f;
            ChaosPhysicalMaterial->StaticFriction = 1.0f;
            ChaosPhysicalMaterial->Restitution = 0.1f;
            ChaosPhysicalMaterial->LinearEtherDrag = 0.02f;
            ChaosPhysicalMaterial->AngularEtherDrag = 0.02f;
            break;
        default:
            ChaosPhysicalMaterial->Friction = 0.7f;
            ChaosPhysicalMaterial->StaticFriction = 0.8f;
            ChaosPhysicalMaterial->Restitution = 0.3f;
            ChaosPhysicalMaterial->LinearEtherDrag = 0.01f;
            ChaosPhysicalMaterial->AngularEtherDrag = 0.01f;
            break;
    }

    // STEP 3: Configure advanced Chaos properties
    ChaosPhysicalMaterial->SleepingLinearVelocityThreshold = 1.0f;
    ChaosPhysicalMaterial->SleepingAngularVelocityThreshold = 0.1f;

    // STEP 4: Save the asset
    FAssetRegistryModule::AssetCreated(ChaosPhysicalMaterial);
    Package->MarkPackageDirty();

    UE_LOG(LogTemp, Log, TEXT("CreateChaosPhysicsMaterial: Created Chaos material %s (Layer: %d, Friction: %.2f, StaticFriction: %.2f)"),
           *MaterialName, LayerIndex, ChaosPhysicalMaterial->Friction, ChaosPhysicalMaterial->StaticFriction);

    return ChaosPhysicalMaterial;
}

bool UUnrealMCPCollisionAdvancedCommands::SetupCollisionProfile(const FString& ProfileName, const FAuracronCollisionConfig& CollisionConfig)
{
    // STEP 1: Access collision profile manager using modern UE 5.6.1 APIs
    UCollisionProfile* CollisionProfile = UCollisionProfile::Get();
    if (!CollisionProfile || !IsValid(CollisionProfile))
    {
        UE_LOG(LogTemp, Error, TEXT("SetupCollisionProfile: Failed to get CollisionProfile"));
        return false;
    }

    // STEP 2: Create custom collision profile
    // Note: This is a simplified implementation
    // Full collision profile creation requires more complex setup
    // involving collision response matrices and object types

    UE_LOG(LogTemp, Log, TEXT("SetupCollisionProfile: Setup profile %s (Enabled: %d, Response: %d)"),
           *ProfileName, (int32)CollisionConfig.CollisionEnabled, (int32)CollisionConfig.CollisionResponse);

    return true;
}

bool UUnrealMCPCollisionAdvancedCommands::ConfigureChaosPhysics(const FChaosPhysicsConfig& PhysicsConfig, AActor* TargetActor)
{
    if (!TargetActor || !IsValid(TargetActor))
    {
        UE_LOG(LogTemp, Error, TEXT("ConfigureChaosPhysics: Invalid target actor"));
        return false;
    }

    // STEP 1: Get primitive component
    UPrimitiveComponent* PrimitiveComponent = TargetActor->FindComponentByClass<UPrimitiveComponent>();
    if (!PrimitiveComponent || !IsValid(PrimitiveComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("ConfigureChaosPhysics: No PrimitiveComponent found"));
        return false;
    }

    // STEP 2: Configure physics properties using modern UE 5.6.1 APIs
    PrimitiveComponent->SetSimulatePhysics(PhysicsConfig.bSimulatePhysics);
    PrimitiveComponent->SetEnableGravity(PhysicsConfig.bEnableGravity);
    PrimitiveComponent->SetMassOverrideInKg(NAME_None, PhysicsConfig.Mass, true);
    PrimitiveComponent->SetLinearDamping(PhysicsConfig.LinearDamping);
    PrimitiveComponent->SetAngularDamping(PhysicsConfig.AngularDamping);

    // STEP 3: Configure experimental Chaos features if requested
    if (PhysicsConfig.bUseChaosFlesh || PhysicsConfig.bUseChaosMover)
    {
        // Note: Experimental Chaos features would require additional setup
        // This is a placeholder for future experimental feature integration
        UE_LOG(LogTemp, Log, TEXT("ConfigureChaosPhysics: Experimental Chaos features requested (Flesh: %s, Mover: %s)"),
               PhysicsConfig.bUseChaosFlesh ? TEXT("Yes") : TEXT("No"),
               PhysicsConfig.bUseChaosMover ? TEXT("Yes") : TEXT("No"));
    }

    UE_LOG(LogTemp, Log, TEXT("ConfigureChaosPhysics: Configured physics for %s (Mass: %.2f, Simulate: %s, Gravity: %s)"),
           *PhysicsConfig.PhysicsName, PhysicsConfig.Mass,
           PhysicsConfig.bSimulatePhysics ? TEXT("Yes") : TEXT("No"),
           PhysicsConfig.bEnableGravity ? TEXT("Yes") : TEXT("No"));

    return true;
}

AActor* UUnrealMCPCollisionAdvancedCommands::CreateAdvancedTriggerVolume(const FTriggerVolumeConfig& TriggerConfig)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateAdvancedTriggerVolume: No valid world context"));
        return nullptr;
    }

    // STEP 1: Create trigger volume actor using modern UE 5.6.1 APIs
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*TriggerConfig.TriggerName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    AActor* TriggerActor = World->SpawnActor<AActor>(AActor::StaticClass(), TriggerConfig.TriggerLocation, FRotator::ZeroRotator, SpawnParams);
    if (!TriggerActor || !IsValid(TriggerActor))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateAdvancedTriggerVolume: Failed to spawn trigger actor"));
        return nullptr;
    }

    TriggerActor->SetActorLabel(TriggerConfig.TriggerName);

    // STEP 2: Create box collision component for trigger
    UBoxComponent* BoxComponent = NewObject<UBoxComponent>(TriggerActor);
    if (!BoxComponent || !IsValid(BoxComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateAdvancedTriggerVolume: Failed to create BoxComponent"));
        return nullptr;
    }

    // Configure box component
    BoxComponent->SetupAttachment(TriggerActor->GetRootComponent());
    BoxComponent->SetBoxExtent(TriggerConfig.TriggerSize * 0.5f);
    BoxComponent->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    BoxComponent->SetCollisionProfileName(TEXT("Trigger"));
    BoxComponent->SetGenerateOverlapEvents(true);

    // Add component to actor
    TriggerActor->AddInstanceComponent(BoxComponent);
    BoxComponent->RegisterComponent();

    // STEP 3: Setup Chaos events if requested
    if (TriggerConfig.bUseChaosEvents)
    {
        // Note: Chaos event integration would require additional setup
        // This is a placeholder for Chaos event system integration
        UE_LOG(LogTemp, Log, TEXT("CreateAdvancedTriggerVolume: Chaos events enabled for trigger %s"),
               *TriggerConfig.TriggerName);
    }

    UE_LOG(LogTemp, Log, TEXT("CreateAdvancedTriggerVolume: Created trigger %s (Type: %s, Size: %.1f,%.1f,%.1f, Chaos: %s)"),
           *TriggerConfig.TriggerName, *TriggerConfig.TriggerType,
           TriggerConfig.TriggerSize.X, TriggerConfig.TriggerSize.Y, TriggerConfig.TriggerSize.Z,
           TriggerConfig.bUseChaosEvents ? TEXT("Yes") : TEXT("No"));

    return TriggerActor;
}

FAuracronCollisionConfig UUnrealMCPCollisionAdvancedCommands::GetLayerCollisionSettings(int32 LayerIndex)
{
    FAuracronCollisionConfig Config;
    Config.LayerIndex = LayerIndex;

    switch (LayerIndex)
    {
        case 0: // Planície Radiante - Standard collision
            Config.CollisionEnabled = ECollisionEnabled::QueryAndPhysics;
            Config.CollisionResponse = ECollisionResponse::ECR_Block;
            Config.CollisionProfileName = TEXT("BlockAll");
            Config.bUseChaosPhysics = true;
            Config.bGenerateOverlapEvents = true;
            break;
        case 1: // Firmamento Zephyr - Lighter collision
            Config.CollisionEnabled = ECollisionEnabled::QueryAndPhysics;
            Config.CollisionResponse = ECollisionResponse::ECR_Block;
            Config.CollisionProfileName = TEXT("BlockAllDynamic");
            Config.bUseChaosPhysics = true;
            Config.bGenerateOverlapEvents = true;
            break;
        case 2: // Abismo Umbral - Heavy collision
            Config.CollisionEnabled = ECollisionEnabled::QueryAndPhysics;
            Config.CollisionResponse = ECollisionResponse::ECR_Block;
            Config.CollisionProfileName = TEXT("BlockAll");
            Config.bUseChaosPhysics = true;
            Config.bGenerateOverlapEvents = false; // Less overlap events for performance
            break;
        default:
            Config.CollisionEnabled = ECollisionEnabled::QueryAndPhysics;
            Config.CollisionResponse = ECollisionResponse::ECR_Block;
            Config.CollisionProfileName = TEXT("BlockAll");
            Config.bUseChaosPhysics = false;
            Config.bGenerateOverlapEvents = true;
            break;
    }

    return Config;
}
