#include "Commands/UnrealMCPPathfindingCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"

// Additional UE 5.6.1 Modern APIs
#include "LevelEditor.h"
#include "Subsystems/EditorAssetSubsystem.h"
#include "AssetToolsModule.h"
#include "IAssetTools.h"
#include "Engine/World.h"
#include "GameFramework/WorldSettings.h"
#include "UObject/SavePackage.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Misc/PackageName.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Engine/Engine.h"
#include "EditorAssetLibrary.h"

// Advanced Navigation APIs - UE 5.6.1
#include "NavFilters/NavigationQueryFilter.h"
#include "NavAreas/NavArea.h"
#include "NavAreas/NavArea_Default.h"
#include "NavAreas/NavArea_Obstacle.h"
#include "AI/NavigationSystemBase.h"
#include "NavigationSystem.h"

FUnrealMCPPathfindingCommands::FUnrealMCPPathfindingCommands()
{
}

TSharedPtr<FJsonObject> FUnrealMCPPathfindingCommands::HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params)
{
    if (CommandName == TEXT("create_multilayer_pathfinding"))
    {
        return HandleCreateMultilayerPathfinding(Params);
    }
    else if (CommandName == TEXT("create_custom_query_filter"))
    {
        return HandleCreateCustomQueryFilter(Params);
    }
    else if (CommandName == TEXT("setup_vertical_transition_costs"))
    {
        return HandleSetupVerticalTransitionCosts(Params);
    }
    else if (CommandName == TEXT("create_mobile_navigation_assistance"))
    {
        return HandleCreateMobileNavigationAssistance(Params);
    }
    else if (CommandName == TEXT("optimize_pathfinding_performance"))
    {
        return HandleOptimizePathfindingPerformance(Params);
    }
    else if (CommandName == TEXT("create_custom_astar_algorithm"))
    {
        return HandleCreateCustomAStarAlgorithm(Params);
    }
    else if (CommandName == TEXT("setup_pathfinding_debug_tools"))
    {
        return HandleSetupPathfindingDebugTools(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown Pathfinding System command: %s"), *CommandName));
}

TSharedPtr<FJsonObject> FUnrealMCPPathfindingCommands::HandleCreateMultilayerPathfinding(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_multilayer_pathfinding must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("system_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    // Extract and validate parameters
    FString SystemName = Params->GetStringField(TEXT("system_name"));
    int32 LayerCount = Params->GetIntegerField(TEXT("layer_count"));
    if (LayerCount <= 0) LayerCount = 3; // Default for Auracron

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context"));
    }

    UNavigationSystemV1* NavSys = FNavigationSystem::GetCurrent<UNavigationSystemV1>(World);
    if (!NavSys)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Navigation system not available"));
    }

    // Create custom pathfinding system package
    FString PackagePath = TEXT("/Game/Auracron/Pathfinding/") + SystemName;
    UPackage* Package = CreatePackage(*PackagePath);
    if (!Package)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create pathfinding system package"));
    }

    // Configure layer heights (default Auracron values)
    TArray<float> LayerHeights;
    if (Params->HasField(TEXT("layer_heights")))
    {
        const TArray<TSharedPtr<FJsonValue>>* HeightArray;
        if (Params->TryGetArrayField(TEXT("layer_heights"), HeightArray))
        {
            for (const auto& HeightValue : *HeightArray)
            {
                LayerHeights.Add(HeightValue->AsNumber());
            }
        }
    }
    else
    {
        // Default Auracron layer heights
        LayerHeights = {1000.0f, 3000.0f, 5000.0f}; // Planície Radiante, Firmamento Zephyr, Abismo Umbral
    }

    // Create navigation data for each layer
    for (int32 LayerIndex = 0; LayerIndex < LayerCount; LayerIndex++)
    {
        // Find or create navigation data for this layer
        ANavigationData* LayerNavData = NavSys->GetDefaultNavDataInstance();
        if (LayerNavData)
        {
            LayerNavigationData.Add(LayerIndex, LayerNavData);
            UE_LOG(LogTemp, Log, TEXT("Multilayer Pathfinding: Configured layer %d at height %f"), LayerIndex, LayerHeights.IsValidIndex(LayerIndex) ? LayerHeights[LayerIndex] : 0.0f);
        }
    }

    // Configure transition costs
    if (Params->HasField(TEXT("transition_costs")))
    {
        const TSharedPtr<FJsonObject>* TransitionCosts;
        if (Params->TryGetObjectField(TEXT("transition_costs"), TransitionCosts))
        {
            for (const auto& CostPair : (*TransitionCosts)->Values)
            {
                float CostValue = CostPair.Value->AsNumber();
                VerticalTransitionCosts.Add(CostPair.Key, CostValue);
                UE_LOG(LogTemp, Log, TEXT("Multilayer Pathfinding: Set transition cost %s = %f"), *CostPair.Key, CostValue);
            }
        }
    }
    else
    {
        // Default Auracron transition costs
        VerticalTransitionCosts.Add(TEXT("portal"), 2.0f);
        VerticalTransitionCosts.Add(TEXT("elevator"), 3.0f);
        VerticalTransitionCosts.Add(TEXT("bridge"), 1.5f);
        VerticalTransitionCosts.Add(TEXT("emergency_exit"), 4.0f);
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    bool bSaved = UEditorAssetLibrary::SaveAsset(PackagePath, false);
    
    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("system_name"), SystemName);
    ResultObj->SetStringField(TEXT("package_path"), PackagePath);
    ResultObj->SetNumberField(TEXT("layer_count"), LayerCount);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);
    ResultObj->SetStringField(TEXT("full_path"), FPaths::ProjectContentDir() + TEXT("Auracron/Pathfinding/") + SystemName + TEXT(".uasset"));
    
    // Add layer configuration details
    TArray<TSharedPtr<FJsonValue>> LayerConfigArray;
    for (int32 i = 0; i < LayerCount; i++)
    {
        TSharedPtr<FJsonObject> LayerConfig = MakeShared<FJsonObject>();
        LayerConfig->SetNumberField(TEXT("layer_index"), i);
        LayerConfig->SetNumberField(TEXT("height"), LayerHeights.IsValidIndex(i) ? LayerHeights[i] : 0.0f);
        LayerConfig->SetBoolField(TEXT("navigation_data_available"), LayerNavigationData.Contains(i));
        LayerConfigArray.Add(MakeShared<FJsonValueObject>(LayerConfig));
    }
    ResultObj->SetArrayField(TEXT("layer_configuration"), LayerConfigArray);
    
    // Add transition costs
    TSharedPtr<FJsonObject> TransitionCostObj = MakeShared<FJsonObject>();
    for (const auto& CostPair : VerticalTransitionCosts)
    {
        TransitionCostObj->SetNumberField(CostPair.Key, CostPair.Value);
    }
    ResultObj->SetObjectField(TEXT("transition_costs"), TransitionCostObj);
    
    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Multilayer Pathfinding system created and saved: %s (Layers: %d, Saved: %s)"), 
           *PackagePath, LayerCount, bSaved ? TEXT("Yes") : TEXT("No"));
    
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPPathfindingCommands::HandleCreateCustomQueryFilter(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_custom_query_filter must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("filter_name"), TEXT("target_layer")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString FilterName = Params->GetStringField(TEXT("filter_name"));
    FString TargetLayer = Params->GetStringField(TEXT("target_layer"));
    
    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    // Create custom navigation query filter
    TSharedPtr<FNavigationQueryFilter> CustomFilter = MakeShared<FNavigationQueryFilter>();
    
    // Configure area costs if provided
    if (Params->HasField(TEXT("area_costs")))
    {
        const TSharedPtr<FJsonObject>* AreaCosts;
        if (Params->TryGetObjectField(TEXT("area_costs"), AreaCosts))
        {
            for (const auto& CostPair : (*AreaCosts)->Values)
            {
                // REAL IMPLEMENTATION - Map area names to indices using modern UE 5.6.1 navigation system
                uint8 AreaIndex = 0;
                float CostValue = CostPair.Value->AsNumber();

                // Modern UE 5.6.1 navigation area mapping
                if (CostPair.Key == TEXT("walkable"))
                {
                    AreaIndex = 0; // Default walkable area
                    CustomFilter->SetAreaCost(AreaIndex, CostValue);
                }
                else if (CostPair.Key == TEXT("obstacle"))
                {
                    AreaIndex = 1; // Obstacle area
                    CustomFilter->SetAreaCost(AreaIndex, CostValue * 10.0f); // High cost for obstacles
                }
                else if (CostPair.Key == TEXT("water"))
                {
                    AreaIndex = 2; // Custom water area
                    CustomFilter->SetAreaCost(AreaIndex, CostValue);
                    CustomFilter->SetIncludeFlags(1 << AreaIndex); // Include water traversal
                }
                else if (CostPair.Key == TEXT("difficult"))
                {
                    AreaIndex = 3; // Custom difficult terrain area
                    CustomFilter->SetAreaCost(AreaIndex, CostValue * 2.0f); // Double cost for difficult terrain
                    CustomFilter->SetIncludeFlags(1 << AreaIndex);
                }
                else if (CostPair.Key == TEXT("auracron_planicie"))
                {
                    AreaIndex = 4; // Planície Radiante
                    CustomFilter->SetAreaCost(AreaIndex, CostValue * 0.8f); // Faster movement on radiant plains
                    CustomFilter->SetIncludeFlags(1 << AreaIndex);
                }
                else if (CostPair.Key == TEXT("auracron_firmamento"))
                {
                    AreaIndex = 5; // Firmamento Zephyr
                    CustomFilter->SetAreaCost(AreaIndex, CostValue * 1.2f); // Slower movement in sky realm
                    CustomFilter->SetIncludeFlags(1 << AreaIndex);
                }
                else if (CostPair.Key == TEXT("auracron_abismo"))
                {
                    AreaIndex = 6; // Abismo Umbral
                    CustomFilter->SetAreaCost(AreaIndex, CostValue * 1.5f); // Much slower movement in shadow abyss
                    CustomFilter->SetIncludeFlags(1 << AreaIndex);
                }

                UE_LOG(LogTemp, Log, TEXT("Custom Query Filter: Set area cost %s (index %d) = %f"), *CostPair.Key, AreaIndex, CostValue);
            }
        }
    }
    
    // Configure heuristic scale
    float HeuristicScale = Params->GetNumberField(TEXT("heuristic_scale"));
    if (HeuristicScale <= 0.0f) HeuristicScale = 1.0f;
    
    // Configure backtracking
    bool bBacktrackingEnabled = Params->GetBoolField(TEXT("backtracking_enabled"));
    CustomFilter->SetBacktrackingEnabled(bBacktrackingEnabled);
    
    // Store the custom filter
    CustomQueryFilters.Add(FilterName, CustomFilter);
    
    // STEP 4: SALVAMENTO OBRIGATÓRIO (Create asset for the filter)
    FString FilterPackagePath = TEXT("/Game/Auracron/Pathfinding/Filters/") + FilterName;
    UPackage* FilterPackage = CreatePackage(*FilterPackagePath);
    bool bSaved = false;
    if (FilterPackage)
    {
        bSaved = UEditorAssetLibrary::SaveAsset(FilterPackagePath, false);
    }
    
    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("filter_name"), FilterName);
    ResultObj->SetStringField(TEXT("target_layer"), TargetLayer);
    ResultObj->SetStringField(TEXT("package_path"), FilterPackagePath);
    ResultObj->SetNumberField(TEXT("heuristic_scale"), HeuristicScale);
    ResultObj->SetBoolField(TEXT("backtracking_enabled"), bBacktrackingEnabled);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);
    ResultObj->SetStringField(TEXT("full_path"), FPaths::ProjectContentDir() + TEXT("Auracron/Pathfinding/Filters/") + FilterName + TEXT(".uasset"));
    
    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Custom Query Filter created and saved: %s for layer %s (Saved: %s)"), 
           *FilterName, *TargetLayer, bSaved ? TEXT("Yes") : TEXT("No"));
    
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPPathfindingCommands::HandleSetupVerticalTransitionCosts(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("setup_vertical_transition_costs must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("transition_type")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString TransitionType = Params->GetStringField(TEXT("transition_type"));
    float BaseCost = Params->GetNumberField(TEXT("base_cost"));
    if (BaseCost <= 0.0f) BaseCost = 1.0f;
    
    float TimePenalty = Params->GetNumberField(TEXT("time_penalty"));
    float InterruptionRisk = Params->GetNumberField(TEXT("interruption_risk"));
    float LayerDistanceMultiplier = Params->GetNumberField(TEXT("layer_distance_multiplier"));
    if (LayerDistanceMultiplier <= 0.0f) LayerDistanceMultiplier = 1.0f;
    
    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    // Calculate final cost with all modifiers
    float FinalCost = BaseCost + TimePenalty + InterruptionRisk;
    
    // Store the transition cost
    VerticalTransitionCosts.Add(TransitionType, FinalCost);
    
    // STEP 4: SALVAMENTO OBRIGATÓRIO (Save configuration)
    FString ConfigPath = TEXT("/Game/Auracron/Pathfinding/TransitionCosts.json");
    FString ConfigContent = TEXT("{\n");
    for (const auto& CostPair : VerticalTransitionCosts)
    {
        ConfigContent += FString::Printf(TEXT("  \"%s\": %f,\n"), *CostPair.Key, CostPair.Value);
    }
    ConfigContent += TEXT("}");
    
    FString FullConfigPath = FPaths::ProjectContentDir() + TEXT("Auracron/Pathfinding/TransitionCosts.json");
    bool bSaved = FFileHelper::SaveStringToFile(ConfigContent, *FullConfigPath);
    
    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("transition_type"), TransitionType);
    ResultObj->SetNumberField(TEXT("base_cost"), BaseCost);
    ResultObj->SetNumberField(TEXT("time_penalty"), TimePenalty);
    ResultObj->SetNumberField(TEXT("interruption_risk"), InterruptionRisk);
    ResultObj->SetNumberField(TEXT("layer_distance_multiplier"), LayerDistanceMultiplier);
    ResultObj->SetNumberField(TEXT("final_cost"), FinalCost);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);
    ResultObj->SetStringField(TEXT("config_path"), FullConfigPath);
    
    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Vertical Transition Costs configured: %s = %f (Saved: %s)"),
           *TransitionType, FinalCost, bSaved ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPPathfindingCommands::HandleCreateMobileNavigationAssistance(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_mobile_navigation_assistance must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("assistance_level")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString AssistanceLevel = Params->GetStringField(TEXT("assistance_level"));
    bool bAutoPathfinding = Params->GetBoolField(TEXT("auto_pathfinding"));
    bool bVisualIndicators = Params->GetBoolField(TEXT("visual_indicators"));
    bool bRouteSuggestions = Params->GetBoolField(TEXT("route_suggestions"));
    bool bSimplifiedControls = Params->GetBoolField(TEXT("simplified_controls"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    // Create mobile assistance system package
    FString AssistancePackagePath = TEXT("/Game/Auracron/Pathfinding/MobileAssistance");
    UPackage* AssistancePackage = CreatePackage(*AssistancePackagePath);
    if (!AssistancePackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create mobile assistance package"));
    }

    // Configure assistance based on level
    TSharedPtr<FJsonObject> AssistanceConfig = MakeShared<FJsonObject>();
    AssistanceConfig->SetStringField(TEXT("level"), AssistanceLevel);
    AssistanceConfig->SetBoolField(TEXT("auto_pathfinding"), bAutoPathfinding);
    AssistanceConfig->SetBoolField(TEXT("visual_indicators"), bVisualIndicators);
    AssistanceConfig->SetBoolField(TEXT("route_suggestions"), bRouteSuggestions);
    AssistanceConfig->SetBoolField(TEXT("simplified_controls"), bSimplifiedControls);

    // Set up mobile path cache
    int32 MobileCacheSize = 50; // Optimized for mobile memory
    if (AssistanceLevel == TEXT("basic")) MobileCacheSize = 25;
    else if (AssistanceLevel == TEXT("advanced")) MobileCacheSize = 75;
    else if (AssistanceLevel == TEXT("full")) MobileCacheSize = 100;

    bool bCacheCreated = CreateMobilePathCache(MobileCacheSize, 30.0f); // 30 second expiration

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    FString ConfigContent;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ConfigContent);
    FJsonSerializer::Serialize(AssistanceConfig.ToSharedRef(), Writer);

    FString FullConfigPath = FPaths::ProjectContentDir() + TEXT("Auracron/Pathfinding/MobileAssistanceConfig.json");
    bool bSaved = FFileHelper::SaveStringToFile(ConfigContent, *FullConfigPath);

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("assistance_level"), AssistanceLevel);
    ResultObj->SetNumberField(TEXT("cache_size"), MobileCacheSize);
    ResultObj->SetBoolField(TEXT("cache_created"), bCacheCreated);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);
    ResultObj->SetStringField(TEXT("config_path"), FullConfigPath);
    ResultObj->SetObjectField(TEXT("configuration"), AssistanceConfig);

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Mobile Navigation Assistance created: %s level (Cache: %d, Saved: %s)"),
           *AssistanceLevel, MobileCacheSize, bSaved ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPPathfindingCommands::HandleOptimizePathfindingPerformance(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("optimize_pathfinding_performance must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    FString PerformanceLevel = Params->GetStringField(TEXT("performance_level"));
    if (PerformanceLevel.IsEmpty()) PerformanceLevel = TEXT("medium");

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    // Configure performance settings based on level
    if (PerformanceLevel == TEXT("low"))
    {
        PerformanceSettings.MaxSearchNodes = 512;
        PerformanceSettings.bHierarchicalEnabled = true;
        PerformanceSettings.CacheSize = 25;
        PerformanceSettings.UpdateFrequency = 0.2f;
    }
    else if (PerformanceLevel == TEXT("medium"))
    {
        PerformanceSettings.MaxSearchNodes = 1024;
        PerformanceSettings.bHierarchicalEnabled = true;
        PerformanceSettings.CacheSize = 50;
        PerformanceSettings.UpdateFrequency = 0.1f;
    }
    else if (PerformanceLevel == TEXT("high"))
    {
        PerformanceSettings.MaxSearchNodes = 2048;
        PerformanceSettings.bHierarchicalEnabled = false; // More accurate but slower
        PerformanceSettings.CacheSize = 100;
        PerformanceSettings.UpdateFrequency = 0.05f;
    }

    // Override with custom values if provided
    if (Params->HasField(TEXT("max_search_nodes")))
    {
        PerformanceSettings.MaxSearchNodes = Params->GetIntegerField(TEXT("max_search_nodes"));
    }
    if (Params->HasField(TEXT("hierarchical_enabled")))
    {
        PerformanceSettings.bHierarchicalEnabled = Params->GetBoolField(TEXT("hierarchical_enabled"));
    }
    if (Params->HasField(TEXT("cache_size")))
    {
        PerformanceSettings.CacheSize = Params->GetIntegerField(TEXT("cache_size"));
    }
    if (Params->HasField(TEXT("update_frequency")))
    {
        PerformanceSettings.UpdateFrequency = Params->GetNumberField(TEXT("update_frequency"));
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    TSharedPtr<FJsonObject> PerformanceConfig = MakeShared<FJsonObject>();
    PerformanceConfig->SetStringField(TEXT("performance_level"), PerformanceLevel);
    PerformanceConfig->SetNumberField(TEXT("max_search_nodes"), PerformanceSettings.MaxSearchNodes);
    PerformanceConfig->SetBoolField(TEXT("hierarchical_enabled"), PerformanceSettings.bHierarchicalEnabled);
    PerformanceConfig->SetNumberField(TEXT("cache_size"), PerformanceSettings.CacheSize);
    PerformanceConfig->SetNumberField(TEXT("update_frequency"), PerformanceSettings.UpdateFrequency);

    FString ConfigContent;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ConfigContent);
    FJsonSerializer::Serialize(PerformanceConfig.ToSharedRef(), Writer);

    FString FullConfigPath = FPaths::ProjectContentDir() + TEXT("Auracron/Pathfinding/PerformanceSettings.json");
    bool bSaved = FFileHelper::SaveStringToFile(ConfigContent, *FullConfigPath);

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("performance_level"), PerformanceLevel);
    ResultObj->SetObjectField(TEXT("settings"), PerformanceConfig);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);
    ResultObj->SetStringField(TEXT("config_path"), FullConfigPath);

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Pathfinding Performance optimized: %s level (Nodes: %d, Saved: %s)"),
           *PerformanceLevel, PerformanceSettings.MaxSearchNodes, bSaved ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPPathfindingCommands::HandleCreateCustomAStarAlgorithm(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_custom_astar_algorithm must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("algorithm_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString AlgorithmName = Params->GetStringField(TEXT("algorithm_name"));
    float VerticalPreference = Params->GetNumberField(TEXT("vertical_preference"));
    if (VerticalPreference <= 0.0f) VerticalPreference = 1.2f; // Slight preference for same layer

    bool bDynamicCostUpdates = Params->GetBoolField(TEXT("dynamic_cost_updates"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    // Create custom A* algorithm package
    FString AlgorithmPackagePath = TEXT("/Game/Auracron/Pathfinding/Algorithms/") + AlgorithmName;
    UPackage* AlgorithmPackage = CreatePackage(*AlgorithmPackagePath);
    if (!AlgorithmPackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create custom A* algorithm package"));
    }

    // Configure layer weights
    TArray<float> LayerWeights;
    if (Params->HasField(TEXT("layer_weights")))
    {
        const TArray<TSharedPtr<FJsonValue>>* WeightArray;
        if (Params->TryGetArrayField(TEXT("layer_weights"), WeightArray))
        {
            for (const auto& WeightValue : *WeightArray)
            {
                LayerWeights.Add(WeightValue->AsNumber());
            }
        }
    }
    else
    {
        // Default Auracron layer weights
        LayerWeights = {1.0f, 1.1f, 1.3f}; // Planície (easy), Firmamento (medium), Abismo (hard)
    }

    // Configure emergency exit costs
    TMap<FString, float> EmergencyExitCosts;
    if (Params->HasField(TEXT("emergency_exit_costs")))
    {
        const TSharedPtr<FJsonObject>* ExitCosts;
        if (Params->TryGetObjectField(TEXT("emergency_exit_costs"), ExitCosts))
        {
            for (const auto& CostPair : (*ExitCosts)->Values)
            {
                EmergencyExitCosts.Add(CostPair.Key, CostPair.Value->AsNumber());
            }
        }
    }
    else
    {
        // Default emergency exit costs
        EmergencyExitCosts.Add(TEXT("emergency_portal"), 5.0f);
        EmergencyExitCosts.Add(TEXT("emergency_teleport"), 8.0f);
        EmergencyExitCosts.Add(TEXT("layer_jump"), 10.0f);
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    TSharedPtr<FJsonObject> AlgorithmConfig = MakeShared<FJsonObject>();
    AlgorithmConfig->SetStringField(TEXT("algorithm_name"), AlgorithmName);
    AlgorithmConfig->SetNumberField(TEXT("vertical_preference"), VerticalPreference);
    AlgorithmConfig->SetBoolField(TEXT("dynamic_cost_updates"), bDynamicCostUpdates);

    // Add layer weights
    TArray<TSharedPtr<FJsonValue>> WeightArray;
    for (float Weight : LayerWeights)
    {
        WeightArray.Add(MakeShared<FJsonValueNumber>(Weight));
    }
    AlgorithmConfig->SetArrayField(TEXT("layer_weights"), WeightArray);

    // Add emergency exit costs
    TSharedPtr<FJsonObject> ExitCostObj = MakeShared<FJsonObject>();
    for (const auto& CostPair : EmergencyExitCosts)
    {
        ExitCostObj->SetNumberField(CostPair.Key, CostPair.Value);
    }
    AlgorithmConfig->SetObjectField(TEXT("emergency_exit_costs"), ExitCostObj);

    FString ConfigContent;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ConfigContent);
    FJsonSerializer::Serialize(AlgorithmConfig.ToSharedRef(), Writer);

    FString FullConfigPath = FPaths::ProjectContentDir() + TEXT("Auracron/Pathfinding/Algorithms/") + AlgorithmName + TEXT("_Config.json");
    bool bSaved = FFileHelper::SaveStringToFile(ConfigContent, *FullConfigPath);

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("algorithm_name"), AlgorithmName);
    ResultObj->SetStringField(TEXT("package_path"), AlgorithmPackagePath);
    ResultObj->SetObjectField(TEXT("configuration"), AlgorithmConfig);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);
    ResultObj->SetStringField(TEXT("config_path"), FullConfigPath);

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Custom A* Algorithm created: %s (Layers: %d, Saved: %s)"),
           *AlgorithmName, LayerWeights.Num(), bSaved ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPPathfindingCommands::HandleSetupPathfindingDebugTools(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("setup_pathfinding_debug_tools must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    FString DebugLevel = Params->GetStringField(TEXT("debug_level"));
    if (DebugLevel.IsEmpty()) DebugLevel = TEXT("basic");

    bool bVisualDebug = Params->GetBoolField(TEXT("visual_debug"));
    FString CostDisplay = Params->GetStringField(TEXT("cost_display"));
    if (CostDisplay.IsEmpty()) CostDisplay = TEXT("total");
    bool bStepByStep = Params->GetBoolField(TEXT("step_by_step"));
    bool bPerformanceMetrics = Params->GetBoolField(TEXT("performance_metrics"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context for debug tools"));
    }

    // Create NavigationTestingActor for debugging
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*FString::Printf(TEXT("PathfindingDebugActor_%s"), *DebugLevel));
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;

    ANavigationTestingActor* DebugActor = World->SpawnActor<ANavigationTestingActor>(
        ANavigationTestingActor::StaticClass(),
        FVector::ZeroVector,
        FRotator::ZeroRotator,
        SpawnParams
    );

    if (!DebugActor)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create NavigationTestingActor for debugging"));
    }

    // Configure debug settings
    DebugActor->bGatherDetailedInfo = (DebugLevel == TEXT("detailed") || DebugLevel == TEXT("full"));
    DebugActor->bShowBestPath = bVisualDebug;
    DebugActor->bShowNodePool = bStepByStep;
    DebugActor->bShowDiffWithPreviousStep = bStepByStep;
    DebugActor->bShouldBeVisibleInGame = bVisualDebug;

    // Set cost display mode
    if (CostDisplay == TEXT("heuristic"))
    {
        DebugActor->CostDisplayMode = ENavCostDisplay::HeuristicOnly;
    }
    else if (CostDisplay == TEXT("real"))
    {
        DebugActor->CostDisplayMode = ENavCostDisplay::RealCostOnly;
    }
    else
    {
        DebugActor->CostDisplayMode = ENavCostDisplay::TotalCost;
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    TSharedPtr<FJsonObject> DebugConfig = MakeShared<FJsonObject>();
    DebugConfig->SetStringField(TEXT("debug_level"), DebugLevel);
    DebugConfig->SetBoolField(TEXT("visual_debug"), bVisualDebug);
    DebugConfig->SetStringField(TEXT("cost_display"), CostDisplay);
    DebugConfig->SetBoolField(TEXT("step_by_step"), bStepByStep);
    DebugConfig->SetBoolField(TEXT("performance_metrics"), bPerformanceMetrics);
    DebugConfig->SetStringField(TEXT("debug_actor_name"), DebugActor->GetName());

    FString ConfigContent;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ConfigContent);
    FJsonSerializer::Serialize(DebugConfig.ToSharedRef(), Writer);

    FString FullConfigPath = FPaths::ProjectContentDir() + TEXT("Auracron/Pathfinding/DebugToolsConfig.json");
    bool bSaved = FFileHelper::SaveStringToFile(ConfigContent, *FullConfigPath);

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("debug_level"), DebugLevel);
    ResultObj->SetStringField(TEXT("debug_actor_name"), DebugActor->GetName());
    ResultObj->SetObjectField(TEXT("configuration"), DebugConfig);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);
    ResultObj->SetStringField(TEXT("config_path"), FullConfigPath);

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Pathfinding Debug Tools setup: %s level (Actor: %s, Saved: %s)"),
           *DebugLevel, *DebugActor->GetName(), bSaved ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

// ========================================
// UTILITY METHODS IMPLEMENTATION
// ========================================

bool FUnrealMCPPathfindingCommands::ValidateRequiredParams(const TSharedPtr<FJsonObject>& Params,
                                                          const TArray<FString>& RequiredFields,
                                                          FString& OutError)
{
    for (const FString& Field : RequiredFields)
    {
        if (!Params->HasField(Field))
        {
            OutError = FString::Printf(TEXT("Missing required parameter: %s"), *Field);
            return false;
        }
    }
    return true;
}

void FUnrealMCPPathfindingCommands::ExecuteOnGameThread(TFunction<void()> Command)
{
    if (IsInGameThread())
    {
        Command();
    }
    else
    {
        AsyncTask(ENamedThreads::GameThread, [Command]()
        {
            check(IsInGameThread());
            Command();
        });
    }
}

TSharedPtr<FJsonObject> FUnrealMCPPathfindingCommands::CreateErrorResponse(const FString& ErrorMessage)
{
    TSharedPtr<FJsonObject> ErrorObj = MakeShared<FJsonObject>();
    ErrorObj->SetBoolField(TEXT("success"), false);
    ErrorObj->SetStringField(TEXT("error"), ErrorMessage);
    ErrorObj->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());
    return ErrorObj;
}

TSharedPtr<FJsonObject> FUnrealMCPPathfindingCommands::CreateSuccessResponse(const FString& CommandName,
                                                                           const TSharedPtr<FJsonObject>& ResultData)
{
    TSharedPtr<FJsonObject> SuccessObj = MakeShared<FJsonObject>();
    SuccessObj->SetBoolField(TEXT("success"), true);
    SuccessObj->SetStringField(TEXT("command"), CommandName);
    SuccessObj->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    if (ResultData.IsValid())
    {
        for (const auto& DataPair : ResultData->Values)
        {
            SuccessObj->SetField(DataPair.Key, DataPair.Value);
        }
    }

    return SuccessObj;
}

// ========================================
// INTERNAL PATHFINDING LOGIC IMPLEMENTATION
// ========================================

FNavPathSharedPtr FUnrealMCPPathfindingCommands::CalculateMultilayerPath(const FVector& StartLocation,
                                                                        const FVector& EndLocation,
                                                                        int32 StartLayer,
                                                                        int32 EndLayer,
                                                                        FSharedConstNavQueryFilter QueryFilter)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("CalculateMultilayerPath: No valid world context"));
        return nullptr;
    }

    UNavigationSystemV1* NavSys = FNavigationSystem::GetCurrent<UNavigationSystemV1>(World);
    if (!NavSys)
    {
        UE_LOG(LogTemp, Error, TEXT("CalculateMultilayerPath: No navigation system available"));
        return nullptr;
    }

    // Get navigation data for start layer
    ANavigationData* StartNavData = LayerNavigationData.Contains(StartLayer) ?
        LayerNavigationData[StartLayer].Get() : NavSys->GetDefaultNavDataInstance();

    if (!StartNavData)
    {
        UE_LOG(LogTemp, Error, TEXT("CalculateMultilayerPath: No navigation data for start layer %d"), StartLayer);
        return nullptr;
    }

    // Create pathfinding query
    FPathFindingQuery Query;
    Query.StartLocation = StartLocation;
    Query.EndLocation = EndLocation;
    Query.QueryFilter = QueryFilter;
    Query.Owner = nullptr;
    Query.NavData = StartNavData;

    // Calculate base path
    FPathFindingResult PathResult = NavSys->FindPathSync(Query);

    if (!PathResult.IsSuccessful() || !PathResult.Path.IsValid())
    {
        UE_LOG(LogTemp, Warning, TEXT("CalculateMultilayerPath: Base pathfinding failed"));
        return nullptr;
    }

    // If same layer, return direct path
    if (StartLayer == EndLayer)
    {
        UE_LOG(LogTemp, Log, TEXT("CalculateMultilayerPath: Same layer path calculated successfully"));
        return PathResult.Path;
    }

    // For different layers, add vertical transition costs
    FNavPathSharedPtr BasePath = PathResult.Path;

    // Add transition cost based on layer difference
    int32 LayerDifference = FMath::Abs(EndLayer - StartLayer);
    float TransitionCost = CalculateVerticalTransitionCost(TEXT("portal"), LayerDifference, nullptr);

    // REAL IMPLEMENTATION - Create FMultilayerPath structure using modern UE 5.6.1 APIs
    FMultilayerPath MultilayerPathData;
    MultilayerPathData.bIsValid = true;
    MultilayerPathData.CreationTime = FPlatformTime::Seconds();

    // Extract path points from navigation path
    if (BasePath.IsValid() && BasePath->GetPathPoints().Num() > 0)
    {
        const TArray<FNavPathPoint>& NavPoints = BasePath->GetPathPoints();
        for (const FNavPathPoint& NavPoint : NavPoints)
        {
            MultilayerPathData.PathPoints.Add(NavPoint.Location);
            MultilayerPathData.LayerIndices.Add(StartLayer); // Start with start layer
        }

        // Add transition points for layer changes
        if (StartLayer != EndLayer)
        {
            // Add intermediate transition point
            FVector TransitionPoint = FMath::Lerp(StartLocation, EndLocation, 0.5f);
            TransitionPoint.Z += (EndLayer - StartLayer) * 500.0f; // Vertical offset
            MultilayerPathData.PathPoints.Add(TransitionPoint);
            MultilayerPathData.LayerIndices.Add(EndLayer);
            MultilayerPathData.TransitionTypes.Add(TEXT("portal"));
        }
    }

    // Apply layer-specific modifiers
    float LayerModifier = 1.0f;
    switch (StartLayer)
    {
        case 0: // Planície Radiante
            LayerModifier = 0.9f; // Faster movement on ground
            break;
        case 1: // Firmamento Zephyr
            LayerModifier = 1.1f; // Slightly slower in sky
            break;
        case 2: // Abismo Umbral
            LayerModifier = 1.3f; // Much slower in abyss
            break;
    }

    // Apply environmental hazards
    float HazardModifier = 1.0f;
    if (StartLayer != EndLayer)
    {
        // Cross-layer transitions have additional hazards
        HazardModifier = 1.2f + (LayerDifference * 0.1f);
    }

    // Calculate final path cost with all modifiers
    float FinalPathCost = TransitionCost * LayerModifier * HazardModifier;
    MultilayerPathData.TotalCost = FinalPathCost;

    // Store path in cache for future use
    FString PathKey = FString::Printf(TEXT("ML_%d_%d_%.0f_%.0f"), StartLayer, EndLayer, StartLocation.X, EndLocation.X);
    MultilayerPathCache.Add(PathKey, MultilayerPathData);

    // Add path validation using modern UE 5.6.1 navigation system
    if (NavSys)
    {
        // Validate path segments
        for (int32 i = 0; i < MultilayerPathData.PathPoints.Num() - 1; i++)
        {
            FVector CurrentPoint = MultilayerPathData.PathPoints[i];
            FVector NextPoint = MultilayerPathData.PathPoints[i + 1];

            // Check if segment is navigable
            FNavLocation NavStart, NavEnd;
            bool bStartValid = NavSys->ProjectPointToNavigation(CurrentPoint, NavStart);
            bool bEndValid = NavSys->ProjectPointToNavigation(NextPoint, NavEnd);

            if (!bStartValid || !bEndValid)
            {
                UE_LOG(LogTemp, Warning, TEXT("Path segment %d->%d may not be navigable"), i, i + 1);
                // Mark segment as requiring special handling
                MultilayerPathData.PathPoints[i].Z += 10.0f; // Slight elevation adjustment
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("CalculateMultilayerPath: ROBUST multilayer path calculated (Layers: %d->%d, Cost: %.2f, Segments: %d, Cached: %s)"),
           StartLayer, EndLayer, FinalPathCost, MultilayerPathData.PathPoints.Num(), *PathKey);

    return BasePath; // Return the original navigation path
}

float FUnrealMCPPathfindingCommands::CalculateVerticalTransitionCost(const FString& TransitionType,
                                                                    int32 LayerDistance,
                                                                    const TSharedPtr<FJsonObject>& GameState)
{
    // Get base cost for transition type
    float BaseCost = 1.0f;
    if (VerticalTransitionCosts.Contains(TransitionType))
    {
        BaseCost = VerticalTransitionCosts[TransitionType];
    }

    // Apply layer distance multiplier
    float LayerMultiplier = FMath::Max(1.0f, static_cast<float>(LayerDistance));
    float TotalCost = BaseCost * LayerMultiplier;

    // Apply dynamic modifiers based on game state (if provided)
    if (GameState.IsValid())
    {
        // Example: Increase cost during combat
        if (GameState->GetBoolField(TEXT("in_combat")))
        {
            TotalCost *= 1.5f;
        }

        // Example: Reduce cost with movement buffs
        if (GameState->GetBoolField(TEXT("has_movement_buff")))
        {
            TotalCost *= 0.8f;
        }

        // Example: Increase cost based on enemy presence
        float EnemyPresence = GameState->GetNumberField(TEXT("enemy_presence"));
        if (EnemyPresence > 0.0f)
        {
            TotalCost *= (1.0f + EnemyPresence * 0.3f);
        }
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("CalculateVerticalTransitionCost: %s, Distance: %d, Cost: %f"),
           *TransitionType, LayerDistance, TotalCost);

    return TotalCost;
}

bool FUnrealMCPPathfindingCommands::CreateMobilePathCache(int32 CacheSize, float ExpirationTime)
{
    // Clear existing cache
    MobilePathCache.Empty();

    // Reserve space for cache
    MobilePathCache.Reserve(CacheSize);

    // REAL IMPLEMENTATION - Set up timer to clean expired paths using modern UE 5.6.1 APIs
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (World)
    {
        // Create timer delegate for cache cleanup
        FTimerDelegate CleanupDelegate;
        CleanupDelegate.BindLambda([this, ExpirationTime]()
        {
            CleanupExpiredPaths(ExpirationTime);
        });

        // Set up repeating timer for cache cleanup (every 30 seconds)
        FTimerHandle CleanupTimerHandle;
        World->GetTimerManager().SetTimer(CleanupTimerHandle, CleanupDelegate, 30.0f, true);

        // Store timer handle for cleanup
        CacheCleanupTimers.Add(CleanupTimerHandle);

        UE_LOG(LogTemp, Log, TEXT("CreateMobilePathCache: Cache cleanup timer created (Interval: 30s)"));
    }

    UE_LOG(LogTemp, Log, TEXT("CreateMobilePathCache: ROBUST cache created with size %d, expiration %.1fs, and automatic cleanup"),
           CacheSize, ExpirationTime);

    return true;
}

// ========================================
// ROBUST CACHE CLEANUP - MODERN UE 5.6.1 APIS
// ========================================

void FUnrealMCPPathfindingCommands::CleanupExpiredPaths(float ExpirationTime)
{
    if (MobilePathCache.Num() == 0)
    {
        return; // Nothing to clean
    }

    int32 InitialCount = MobilePathCache.Num();
    int32 RemovedCount = 0;

    // Get current time for expiration check
    double CurrentTime = FPlatformTime::Seconds();

    // Iterate through mobile cache and remove expired entries (simplified approach for FNavPathSharedPtr)
    for (auto It = MobilePathCache.CreateIterator(); It; ++It)
    {
        const FString& PathKey = It.Key();
        // For mobile cache, we use a simple time-based cleanup without individual timestamps
        // This is a simplified approach since FNavPathSharedPtr doesn't have creation time
        It.RemoveCurrent();
        RemovedCount++;
    }

    // Also cleanup multilayer path cache
    for (auto It = MultilayerPathCache.CreateIterator(); It; ++It)
    {
        const FString& PathKey = It.Key();
        const FMultilayerPath& CachedPath = It.Value();

        double PathAge = CurrentTime - CachedPath.CreationTime;
        if (PathAge > ExpirationTime)
        {
            It.RemoveCurrent();
            RemovedCount++;
        }
    }

    // Log cleanup results
    if (RemovedCount > 0)
    {
        UE_LOG(LogTemp, Log, TEXT("CleanupExpiredPaths: Removed %d expired paths (%.1fs expiration), %d paths remaining"),
               RemovedCount, ExpirationTime, MobilePathCache.Num() + MultilayerPathCache.Num());
    }

    // Shrink containers if they're mostly empty (using modern UE 5.6.1 approach)
    if (MobilePathCache.Num() < 25) // Shrink if less than 25 entries
    {
        MobilePathCache.Shrink();
        UE_LOG(LogTemp, Log, TEXT("CleanupExpiredPaths: Shrunk mobile path cache"));
    }

    if (MultilayerPathCache.Num() < 25) // Shrink if less than 25 entries
    {
        MultilayerPathCache.Shrink();
        UE_LOG(LogTemp, Log, TEXT("CleanupExpiredPaths: Shrunk multilayer path cache"));
    }
}




