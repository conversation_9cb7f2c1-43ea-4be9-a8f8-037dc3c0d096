"""
    pygments.lexers.ncl
    ~~~~~~~~~~~~~~~~~~~

    Lexers for NCAR Command Language.

    :copyright: Copyright 2006-2025 by the Pygments team, see AUTHORS.
    :license: BSD, see LICENSE for details.
"""

import re

from pygments.lexer import RegexLexer, include, words
from pygments.token import Text, Comment, Operator, Keyword, Name, String, \
    Number, Punctuation

__all__ = ['NCLLexer']


class NCLLexer(RegexLexer):
    """
    Lexer for NCL code.
    """
    name = 'NCL'
    aliases = ['ncl']
    filenames = ['*.ncl']
    mimetypes = ['text/ncl']
    url = 'https://www.ncl.ucar.edu'
    version_added = '2.2'

    flags = re.MULTILINE

    tokens = {
        'root': [
            (r';.*\n', Comment),
            include('strings'),
            include('core'),
            (r'[a-zA-Z_]\w*', Name),
            include('nums'),
            (r'[\s]+', Text),
        ],
        'core': [
            # Statements
            (words((
                'begin', 'break', 'continue', 'create', 'defaultapp', 'do',
                'else', 'end', 'external', 'exit', 'True', 'False', 'file', 'function',
                'getvalues', 'graphic', 'group', 'if', 'list', 'load', 'local',
                'new', '_Missing', 'Missing', 'noparent', 'procedure',
                'quit', 'QUIT', 'Quit', 'record', 'return', 'setvalues', 'stop',
                'then', 'while'), prefix=r'\b', suffix=r'\s*\b'),
             Keyword),

            # Data Types
            (words((
                'ubyte', 'uint', 'uint64', 'ulong', 'string', 'byte',
                'character', 'double', 'float', 'integer', 'int64', 'logical',
                'long', 'short', 'ushort', 'enumeric', 'numeric', 'snumeric'),
                prefix=r'\b', suffix=r'\s*\b'),
             Keyword.Type),

            # Operators
            (r'[\%^*+\-/<>]', Operator),

            # punctuation:
            (r'[\[\]():@$!&|.,\\{}]', Punctuation),
            (r'[=:]', Punctuation),

            # Intrinsics
            (words((
                'abs', 'acos', 'addfile', 'addfiles', 'all', 'angmom_atm', 'any',
                'area_conserve_remap', 'area_hi2lores', 'area_poly_sphere',
                'asciiread', 'asciiwrite', 'asin', 'atan', 'atan2', 'attsetvalues',
                'avg', 'betainc', 'bin_avg', 'bin_sum', 'bw_bandpass_filter',
                'cancor', 'cbinread', 'cbinwrite', 'cd_calendar', 'cd_inv_calendar',
                'cdfbin_p', 'cdfbin_pr', 'cdfbin_s', 'cdfbin_xn', 'cdfchi_p',
                'cdfchi_x', 'cdfgam_p', 'cdfgam_x', 'cdfnor_p', 'cdfnor_x',
                'cdft_p', 'cdft_t', 'ceil', 'center_finite_diff',
                'center_finite_diff_n', 'cfftb', 'cfftf', 'cfftf_frq_reorder',
                'charactertodouble', 'charactertofloat', 'charactertointeger',
                'charactertolong', 'charactertoshort', 'charactertostring',
                'chartodouble', 'chartofloat', 'chartoint', 'chartointeger',
                'chartolong', 'chartoshort', 'chartostring', 'chiinv', 'clear',
                'color_index_to_rgba', 'conform', 'conform_dims', 'cos', 'cosh',
                'count_unique_values', 'covcorm', 'covcorm_xy', 'craybinnumrec',
                'craybinrecread', 'create_graphic', 'csa1', 'csa1d', 'csa1s',
                'csa1x', 'csa1xd', 'csa1xs', 'csa2', 'csa2d', 'csa2l', 'csa2ld',
                'csa2ls', 'csa2lx', 'csa2lxd', 'csa2lxs', 'csa2s', 'csa2x',
                'csa2xd', 'csa2xs', 'csa3', 'csa3d', 'csa3l', 'csa3ld', 'csa3ls',
                'csa3lx', 'csa3lxd', 'csa3lxs', 'csa3s', 'csa3x', 'csa3xd',
                'csa3xs', 'csc2s', 'csgetp', 'css2c', 'cssetp', 'cssgrid', 'csstri',
                'csvoro', 'cumsum', 'cz2ccm', 'datatondc', 'day_of_week',
                'day_of_year', 'days_in_month', 'default_fillvalue', 'delete',
                'depth_to_pres', 'destroy', 'determinant', 'dewtemp_trh',
                'dgeevx_lapack', 'dim_acumrun_n', 'dim_avg', 'dim_avg_n',
                'dim_avg_wgt', 'dim_avg_wgt_n', 'dim_cumsum', 'dim_cumsum_n',
                'dim_gamfit_n', 'dim_gbits', 'dim_max', 'dim_max_n', 'dim_median',
                'dim_median_n', 'dim_min', 'dim_min_n', 'dim_num', 'dim_num_n',
                'dim_numrun_n', 'dim_pqsort', 'dim_pqsort_n', 'dim_product',
                'dim_product_n', 'dim_rmsd', 'dim_rmsd_n', 'dim_rmvmean',
                'dim_rmvmean_n', 'dim_rmvmed', 'dim_rmvmed_n', 'dim_spi_n',
                'dim_standardize', 'dim_standardize_n', 'dim_stat4', 'dim_stat4_n',
                'dim_stddev', 'dim_stddev_n', 'dim_sum', 'dim_sum_n', 'dim_sum_wgt',
                'dim_sum_wgt_n', 'dim_variance', 'dim_variance_n', 'dimsizes',
                'doubletobyte', 'doubletochar', 'doubletocharacter',
                'doubletofloat', 'doubletoint', 'doubletointeger', 'doubletolong',
                'doubletoshort', 'dpres_hybrid_ccm', 'dpres_plevel', 'draw',
                'draw_color_palette', 'dsgetp', 'dsgrid2', 'dsgrid2d', 'dsgrid2s',
                'dsgrid3', 'dsgrid3d', 'dsgrid3s', 'dspnt2', 'dspnt2d', 'dspnt2s',
                'dspnt3', 'dspnt3d', 'dspnt3s', 'dssetp', 'dtrend', 'dtrend_msg',
                'dtrend_msg_n', 'dtrend_n', 'dtrend_quadratic',
                'dtrend_quadratic_msg_n', 'dv2uvf', 'dv2uvg', 'dz_height',
                'echo_off', 'echo_on', 'eof2data', 'eof_varimax', 'eofcor',
                'eofcor_pcmsg', 'eofcor_ts', 'eofcov', 'eofcov_pcmsg', 'eofcov_ts',
                'eofunc', 'eofunc_ts', 'eofunc_varimax', 'equiv_sample_size', 'erf',
                'erfc', 'esacr', 'esacv', 'esccr', 'esccv', 'escorc', 'escorc_n',
                'escovc', 'exit', 'exp', 'exp_tapersh', 'exp_tapersh_wgts',
                'exp_tapershC', 'ezfftb', 'ezfftb_n', 'ezfftf', 'ezfftf_n',
                'f2fosh', 'f2foshv', 'f2fsh', 'f2fshv', 'f2gsh', 'f2gshv', 'fabs',
                'fbindirread', 'fbindirwrite', 'fbinnumrec', 'fbinread',
                'fbinrecread', 'fbinrecwrite', 'fbinwrite', 'fft2db', 'fft2df',
                'fftshift', 'fileattdef', 'filechunkdimdef', 'filedimdef',
                'fileexists', 'filegrpdef', 'filevarattdef', 'filevarchunkdef',
                'filevarcompressleveldef', 'filevardef', 'filevardimsizes',
                'filwgts_lancos', 'filwgts_lanczos', 'filwgts_normal',
                'floattobyte', 'floattochar', 'floattocharacter', 'floattoint',
                'floattointeger', 'floattolong', 'floattoshort', 'floor',
                'fluxEddy', 'fo2fsh', 'fo2fshv', 'fourier_info', 'frame', 'fspan',
                'ftcurv', 'ftcurvd', 'ftcurvi', 'ftcurvp', 'ftcurvpi', 'ftcurvps',
                'ftcurvs', 'ftest', 'ftgetp', 'ftkurv', 'ftkurvd', 'ftkurvp',
                'ftkurvpd', 'ftsetp', 'ftsurf', 'g2fsh', 'g2fshv', 'g2gsh',
                'g2gshv', 'gamma', 'gammainc', 'gaus', 'gaus_lobat',
                'gaus_lobat_wgt', 'gc_aangle', 'gc_clkwise', 'gc_dangle',
                'gc_inout', 'gc_latlon', 'gc_onarc', 'gc_pnt2gc', 'gc_qarea',
                'gc_tarea', 'generate_2d_array', 'get_color_index',
                'get_color_rgba', 'get_cpu_time', 'get_isolines', 'get_ncl_version',
                'get_script_name', 'get_script_prefix_name', 'get_sphere_radius',
                'get_unique_values', 'getbitsone', 'getenv', 'getfiledimsizes',
                'getfilegrpnames', 'getfilepath', 'getfilevaratts',
                'getfilevarchunkdimsizes', 'getfilevardims', 'getfilevardimsizes',
                'getfilevarnames', 'getfilevartypes', 'getvaratts', 'getvardims',
                'gradsf', 'gradsg', 'greg2jul', 'grid2triple', 'hlsrgb', 'hsvrgb',
                'hydro', 'hyi2hyo', 'idsfft', 'igradsf', 'igradsg', 'ilapsf',
                'ilapsg', 'ilapvf', 'ilapvg', 'ind', 'ind_resolve', 'int2p',
                'int2p_n', 'integertobyte', 'integertochar', 'integertocharacter',
                'integertoshort', 'inttobyte', 'inttochar', 'inttoshort',
                'inverse_matrix', 'isatt', 'isbigendian', 'isbyte', 'ischar',
                'iscoord', 'isdefined', 'isdim', 'isdimnamed', 'isdouble',
                'isenumeric', 'isfile', 'isfilepresent', 'isfilevar',
                'isfilevaratt', 'isfilevarcoord', 'isfilevardim', 'isfloat',
                'isfunc', 'isgraphic', 'isint', 'isint64', 'isinteger',
                'isleapyear', 'islogical', 'islong', 'ismissing', 'isnan_ieee',
                'isnumeric', 'ispan', 'isproc', 'isshort', 'issnumeric', 'isstring',
                'isubyte', 'isuint', 'isuint64', 'isulong', 'isunlimited',
                'isunsigned', 'isushort', 'isvar', 'jul2greg', 'kmeans_as136',
                'kolsm2_n', 'kron_product', 'lapsf', 'lapsg', 'lapvf', 'lapvg',
                'latlon2utm', 'lclvl', 'lderuvf', 'lderuvg', 'linint1', 'linint1_n',
                'linint2', 'linint2_points', 'linmsg', 'linmsg_n', 'linrood_latwgt',
                'linrood_wgt', 'list_files', 'list_filevars', 'list_hlus',
                'list_procfuncs', 'list_vars', 'ListAppend', 'ListCount',
                'ListGetType', 'ListIndex', 'ListIndexFromName', 'ListPop',
                'ListPush', 'ListSetType', 'loadscript', 'local_max', 'local_min',
                'log', 'log10', 'longtobyte', 'longtochar', 'longtocharacter',
                'longtoint', 'longtointeger', 'longtoshort', 'lspoly', 'lspoly_n',
                'mask', 'max', 'maxind', 'min', 'minind', 'mixed_layer_depth',
                'mixhum_ptd', 'mixhum_ptrh', 'mjo_cross_coh2pha',
                'mjo_cross_segment', 'moc_globe_atl', 'monthday', 'natgrid',
                'natgridd', 'natgrids', 'ncargpath', 'ncargversion', 'ndctodata',
                'ndtooned', 'new', 'NewList', 'ngezlogo', 'nggcog', 'nggetp',
                'nglogo', 'ngsetp', 'NhlAddAnnotation', 'NhlAddData',
                'NhlAddOverlay', 'NhlAddPrimitive', 'NhlAppGetDefaultParentId',
                'NhlChangeWorkstation', 'NhlClassName', 'NhlClearWorkstation',
                'NhlDataPolygon', 'NhlDataPolyline', 'NhlDataPolymarker',
                'NhlDataToNDC', 'NhlDestroy', 'NhlDraw', 'NhlFrame', 'NhlFreeColor',
                'NhlGetBB', 'NhlGetClassResources', 'NhlGetErrorObjectId',
                'NhlGetNamedColorIndex', 'NhlGetParentId',
                'NhlGetParentWorkstation', 'NhlGetWorkspaceObjectId',
                'NhlIsAllocatedColor', 'NhlIsApp', 'NhlIsDataComm', 'NhlIsDataItem',
                'NhlIsDataSpec', 'NhlIsTransform', 'NhlIsView', 'NhlIsWorkstation',
                'NhlName', 'NhlNDCPolygon', 'NhlNDCPolyline', 'NhlNDCPolymarker',
                'NhlNDCToData', 'NhlNewColor', 'NhlNewDashPattern', 'NhlNewMarker',
                'NhlPalGetDefined', 'NhlRemoveAnnotation', 'NhlRemoveData',
                'NhlRemoveOverlay', 'NhlRemovePrimitive', 'NhlSetColor',
                'NhlSetDashPattern', 'NhlSetMarker', 'NhlUpdateData',
                'NhlUpdateWorkstation', 'nice_mnmxintvl', 'nngetaspectd',
                'nngetaspects', 'nngetp', 'nngetsloped', 'nngetslopes', 'nngetwts',
                'nngetwtsd', 'nnpnt', 'nnpntd', 'nnpntend', 'nnpntendd',
                'nnpntinit', 'nnpntinitd', 'nnpntinits', 'nnpnts', 'nnsetp', 'num',
                'obj_anal_ic', 'omega_ccm', 'onedtond', 'overlay', 'paleo_outline',
                'pdfxy_bin', 'poisson_grid_fill', 'pop_remap', 'potmp_insitu_ocn',
                'prcwater_dp', 'pres2hybrid', 'pres_hybrid_ccm', 'pres_sigma',
                'print', 'print_table', 'printFileVarSummary', 'printVarSummary',
                'product', 'pslec', 'pslhor', 'pslhyp', 'qsort', 'rand',
                'random_chi', 'random_gamma', 'random_normal', 'random_setallseed',
                'random_uniform', 'rcm2points', 'rcm2rgrid', 'rdsstoi',
                'read_colormap_file', 'reg_multlin', 'regcoef', 'regCoef_n',
                'regline', 'relhum', 'replace_ieeenan', 'reshape', 'reshape_ind',
                'rgba_to_color_index', 'rgbhls', 'rgbhsv', 'rgbyiq', 'rgrid2rcm',
                'rhomb_trunc', 'rip_cape_2d', 'rip_cape_3d', 'round', 'rtest',
                'runave', 'runave_n', 'set_default_fillvalue', 'set_sphere_radius',
                'setfileoption', 'sfvp2uvf', 'sfvp2uvg', 'shaec', 'shagc',
                'shgetnp', 'shgetp', 'shgrid', 'shorttobyte', 'shorttochar',
                'shorttocharacter', 'show_ascii', 'shsec', 'shsetp', 'shsgc',
                'shsgc_R42', 'sigma2hybrid', 'simpeq', 'simpne', 'sin',
                'sindex_yrmo', 'sinh', 'sizeof', 'sleep', 'smth9', 'snindex_yrmo',
                'solve_linsys', 'span_color_indexes', 'span_color_rgba',
                'sparse_matrix_mult', 'spcorr', 'spcorr_n', 'specx_anal',
                'specxy_anal', 'spei', 'sprintf', 'sprinti', 'sqrt', 'sqsort',
                'srand', 'stat2', 'stat4', 'stat_medrng', 'stat_trim',
                'status_exit', 'stdatmus_p2tdz', 'stdatmus_z2tdp', 'stddev',
                'str_capital', 'str_concat', 'str_fields_count', 'str_get_cols',
                'str_get_dq', 'str_get_field', 'str_get_nl', 'str_get_sq',
                'str_get_tab', 'str_index_of_substr', 'str_insert', 'str_is_blank',
                'str_join', 'str_left_strip', 'str_lower', 'str_match',
                'str_match_ic', 'str_match_ic_regex', 'str_match_ind',
                'str_match_ind_ic', 'str_match_ind_ic_regex', 'str_match_ind_regex',
                'str_match_regex', 'str_right_strip', 'str_split',
                'str_split_by_length', 'str_split_csv', 'str_squeeze', 'str_strip',
                'str_sub_str', 'str_switch', 'str_upper', 'stringtochar',
                'stringtocharacter', 'stringtodouble', 'stringtofloat',
                'stringtoint', 'stringtointeger', 'stringtolong', 'stringtoshort',
                'strlen', 'student_t', 'sum', 'svd_lapack', 'svdcov', 'svdcov_sv',
                'svdstd', 'svdstd_sv', 'system', 'systemfunc', 'tan', 'tanh',
                'taper', 'taper_n', 'tdclrs', 'tdctri', 'tdcudp', 'tdcurv',
                'tddtri', 'tdez2d', 'tdez3d', 'tdgetp', 'tdgrds', 'tdgrid',
                'tdgtrs', 'tdinit', 'tditri', 'tdlbla', 'tdlblp', 'tdlbls',
                'tdline', 'tdlndp', 'tdlnpa', 'tdlpdp', 'tdmtri', 'tdotri',
                'tdpara', 'tdplch', 'tdprpa', 'tdprpi', 'tdprpt', 'tdsetp',
                'tdsort', 'tdstri', 'tdstrs', 'tdttri', 'thornthwaite', 'tobyte',
                'tochar', 'todouble', 'tofloat', 'toint', 'toint64', 'tointeger',
                'tolong', 'toshort', 'tosigned', 'tostring', 'tostring_with_format',
                'totype', 'toubyte', 'touint', 'touint64', 'toulong', 'tounsigned',
                'toushort', 'trend_manken', 'tri_trunc', 'triple2grid',
                'triple2grid2d', 'trop_wmo', 'ttest', 'typeof', 'undef',
                'unique_string', 'update', 'ushorttoint', 'ut_calendar',
                'ut_inv_calendar', 'utm2latlon', 'uv2dv_cfd', 'uv2dvf', 'uv2dvg',
                'uv2sfvpf', 'uv2sfvpg', 'uv2vr_cfd', 'uv2vrdvf', 'uv2vrdvg',
                'uv2vrf', 'uv2vrg', 'v5d_close', 'v5d_create', 'v5d_setLowLev',
                'v5d_setUnits', 'v5d_write', 'v5d_write_var', 'variance', 'vhaec',
                'vhagc', 'vhsec', 'vhsgc', 'vibeta', 'vinth2p', 'vinth2p_ecmwf',
                'vinth2p_ecmwf_nodes', 'vinth2p_nodes', 'vintp2p_ecmwf', 'vr2uvf',
                'vr2uvg', 'vrdv2uvf', 'vrdv2uvg', 'wavelet', 'wavelet_default',
                'weibull', 'wgt_area_smooth', 'wgt_areaave', 'wgt_areaave2',
                'wgt_arearmse', 'wgt_arearmse2', 'wgt_areasum2', 'wgt_runave',
                'wgt_runave_n', 'wgt_vert_avg_beta', 'wgt_volave', 'wgt_volave_ccm',
                'wgt_volrmse', 'wgt_volrmse_ccm', 'where', 'wk_smooth121', 'wmbarb',
                'wmbarbmap', 'wmdrft', 'wmgetp', 'wmlabs', 'wmsetp', 'wmstnm',
                'wmvect', 'wmvectmap', 'wmvlbl', 'wrf_avo', 'wrf_cape_2d',
                'wrf_cape_3d', 'wrf_dbz', 'wrf_eth', 'wrf_helicity', 'wrf_ij_to_ll',
                'wrf_interp_1d', 'wrf_interp_2d_xy', 'wrf_interp_3d_z',
                'wrf_latlon_to_ij', 'wrf_ll_to_ij', 'wrf_omega', 'wrf_pvo',
                'wrf_rh', 'wrf_slp', 'wrf_smooth_2d', 'wrf_td', 'wrf_tk',
                'wrf_updraft_helicity', 'wrf_uvmet', 'wrf_virtual_temp',
                'wrf_wetbulb', 'wrf_wps_close_int', 'wrf_wps_open_int',
                'wrf_wps_rddata_int', 'wrf_wps_rdhead_int', 'wrf_wps_read_int',
                'wrf_wps_write_int', 'write_matrix', 'write_table', 'yiqrgb',
                'z2geouv', 'zonal_mpsi', 'addfiles_GetVar', 'advect_variable',
                'area_conserve_remap_Wrap', 'area_hi2lores_Wrap',
                'array_append_record', 'assignFillValue', 'byte2flt',
                'byte2flt_hdf', 'calcDayAnomTLL', 'calcMonAnomLLLT',
                'calcMonAnomLLT', 'calcMonAnomTLL', 'calcMonAnomTLLL',
                'calculate_monthly_values', 'cd_convert', 'changeCase',
                'changeCaseChar', 'clmDayTLL', 'clmDayTLLL', 'clmMon2clmDay',
                'clmMonLLLT', 'clmMonLLT', 'clmMonTLL', 'clmMonTLLL', 'closest_val',
                'copy_VarAtts', 'copy_VarCoords', 'copy_VarCoords_1',
                'copy_VarCoords_2', 'copy_VarMeta', 'copyatt', 'crossp3',
                'cshstringtolist', 'cssgrid_Wrap', 'dble2flt', 'decimalPlaces',
                'delete_VarAtts', 'dim_avg_n_Wrap', 'dim_avg_wgt_n_Wrap',
                'dim_avg_wgt_Wrap', 'dim_avg_Wrap', 'dim_cumsum_n_Wrap',
                'dim_cumsum_Wrap', 'dim_max_n_Wrap', 'dim_min_n_Wrap',
                'dim_rmsd_n_Wrap', 'dim_rmsd_Wrap', 'dim_rmvmean_n_Wrap',
                'dim_rmvmean_Wrap', 'dim_rmvmed_n_Wrap', 'dim_rmvmed_Wrap',
                'dim_standardize_n_Wrap', 'dim_standardize_Wrap',
                'dim_stddev_n_Wrap', 'dim_stddev_Wrap', 'dim_sum_n_Wrap',
                'dim_sum_wgt_n_Wrap', 'dim_sum_wgt_Wrap', 'dim_sum_Wrap',
                'dim_variance_n_Wrap', 'dim_variance_Wrap', 'dpres_plevel_Wrap',
                'dtrend_leftdim', 'dv2uvF_Wrap', 'dv2uvG_Wrap', 'eof_north',
                'eofcor_Wrap', 'eofcov_Wrap', 'eofunc_north', 'eofunc_ts_Wrap',
                'eofunc_varimax_reorder', 'eofunc_varimax_Wrap', 'eofunc_Wrap',
                'epsZero', 'f2fosh_Wrap', 'f2foshv_Wrap', 'f2fsh_Wrap',
                'f2fshv_Wrap', 'f2gsh_Wrap', 'f2gshv_Wrap', 'fbindirSwap',
                'fbinseqSwap1', 'fbinseqSwap2', 'flt2dble', 'flt2string',
                'fo2fsh_Wrap', 'fo2fshv_Wrap', 'g2fsh_Wrap', 'g2fshv_Wrap',
                'g2gsh_Wrap', 'g2gshv_Wrap', 'generate_resample_indices',
                'generate_sample_indices', 'generate_unique_indices',
                'genNormalDist', 'get1Dindex', 'get1Dindex_Collapse',
                'get1Dindex_Exclude', 'get_file_suffix', 'GetFillColor',
                'GetFillColorIndex', 'getFillValue', 'getind_latlon2d',
                'getVarDimNames', 'getVarFillValue', 'grib_stime2itime',
                'hyi2hyo_Wrap', 'ilapsF_Wrap', 'ilapsG_Wrap', 'ind_nearest_coord',
                'indStrSubset', 'int2dble', 'int2flt', 'int2p_n_Wrap', 'int2p_Wrap',
                'isMonotonic', 'isStrSubset', 'latGau', 'latGauWgt', 'latGlobeF',
                'latGlobeFo', 'latRegWgt', 'linint1_n_Wrap', 'linint1_Wrap',
                'linint2_points_Wrap', 'linint2_Wrap', 'local_max_1d',
                'local_min_1d', 'lonFlip', 'lonGlobeF', 'lonGlobeFo', 'lonPivot',
                'merge_levels_sfc', 'mod', 'month_to_annual',
                'month_to_annual_weighted', 'month_to_season', 'month_to_season12',
                'month_to_seasonN', 'monthly_total_to_daily_mean', 'nameDim',
                'natgrid_Wrap', 'NewCosWeight', 'niceLatLon2D', 'NormCosWgtGlobe',
                'numAsciiCol', 'numAsciiRow', 'numeric2int',
                'obj_anal_ic_deprecated', 'obj_anal_ic_Wrap', 'omega_ccm_driver',
                'omega_to_w', 'oneDtostring', 'pack_values', 'pattern_cor', 'pdfx',
                'pdfxy', 'pdfxy_conform', 'pot_temp', 'pot_vort_hybrid',
                'pot_vort_isobaric', 'pres2hybrid_Wrap', 'print_clock',
                'printMinMax', 'quadroots', 'rcm2points_Wrap', 'rcm2rgrid_Wrap',
                'readAsciiHead', 'readAsciiTable', 'reg_multlin_stats',
                'region_ind', 'regline_stats', 'relhum_ttd', 'replaceSingleChar',
                'RGBtoCmap', 'rgrid2rcm_Wrap', 'rho_mwjf', 'rm_single_dims',
                'rmAnnCycle1D', 'rmInsufData', 'rmMonAnnCycLLLT', 'rmMonAnnCycLLT',
                'rmMonAnnCycTLL', 'runave_n_Wrap', 'runave_Wrap', 'short2flt',
                'short2flt_hdf', 'shsgc_R42_Wrap', 'sign_f90', 'sign_matlab',
                'smth9_Wrap', 'smthClmDayTLL', 'smthClmDayTLLL', 'SqrtCosWeight',
                'stat_dispersion', 'static_stability', 'stdMonLLLT', 'stdMonLLT',
                'stdMonTLL', 'stdMonTLLL', 'symMinMaxPlt', 'table_attach_columns',
                'table_attach_rows', 'time_to_newtime', 'transpose',
                'triple2grid_Wrap', 'ut_convert', 'uv2dvF_Wrap', 'uv2dvG_Wrap',
                'uv2vrF_Wrap', 'uv2vrG_Wrap', 'vr2uvF_Wrap', 'vr2uvG_Wrap',
                'w_to_omega', 'wallClockElapseTime', 'wave_number_spc',
                'wgt_areaave_Wrap', 'wgt_runave_leftdim', 'wgt_runave_n_Wrap',
                'wgt_runave_Wrap', 'wgt_vertical_n', 'wind_component',
                'wind_direction', 'yyyyddd_to_yyyymmdd', 'yyyymm_time',
                'yyyymm_to_yyyyfrac', 'yyyymmdd_time', 'yyyymmdd_to_yyyyddd',
                'yyyymmdd_to_yyyyfrac', 'yyyymmddhh_time', 'yyyymmddhh_to_yyyyfrac',
                'zonal_mpsi_Wrap', 'zonalAve', 'calendar_decode2', 'cd_string',
                'kf_filter', 'run_cor', 'time_axis_labels', 'ut_string',
                'wrf_contour', 'wrf_map', 'wrf_map_overlay', 'wrf_map_overlays',
                'wrf_map_resources', 'wrf_map_zoom', 'wrf_overlay', 'wrf_overlays',
                'wrf_user_getvar', 'wrf_user_ij_to_ll', 'wrf_user_intrp2d',
                'wrf_user_intrp3d', 'wrf_user_latlon_to_ij', 'wrf_user_list_times',
                'wrf_user_ll_to_ij', 'wrf_user_unstagger', 'wrf_user_vert_interp',
                'wrf_vector', 'gsn_add_annotation', 'gsn_add_polygon',
                'gsn_add_polyline', 'gsn_add_polymarker',
                'gsn_add_shapefile_polygons', 'gsn_add_shapefile_polylines',
                'gsn_add_shapefile_polymarkers', 'gsn_add_text', 'gsn_attach_plots',
                'gsn_blank_plot', 'gsn_contour', 'gsn_contour_map',
                'gsn_contour_shade', 'gsn_coordinates', 'gsn_create_labelbar',
                'gsn_create_legend', 'gsn_create_text',
                'gsn_csm_attach_zonal_means', 'gsn_csm_blank_plot',
                'gsn_csm_contour', 'gsn_csm_contour_map', 'gsn_csm_contour_map_ce',
                'gsn_csm_contour_map_overlay', 'gsn_csm_contour_map_polar',
                'gsn_csm_hov', 'gsn_csm_lat_time', 'gsn_csm_map', 'gsn_csm_map_ce',
                'gsn_csm_map_polar', 'gsn_csm_pres_hgt',
                'gsn_csm_pres_hgt_streamline', 'gsn_csm_pres_hgt_vector',
                'gsn_csm_streamline', 'gsn_csm_streamline_contour_map',
                'gsn_csm_streamline_contour_map_ce',
                'gsn_csm_streamline_contour_map_polar', 'gsn_csm_streamline_map',
                'gsn_csm_streamline_map_ce', 'gsn_csm_streamline_map_polar',
                'gsn_csm_streamline_scalar', 'gsn_csm_streamline_scalar_map',
                'gsn_csm_streamline_scalar_map_ce',
                'gsn_csm_streamline_scalar_map_polar', 'gsn_csm_time_lat',
                'gsn_csm_vector', 'gsn_csm_vector_map', 'gsn_csm_vector_map_ce',
                'gsn_csm_vector_map_polar', 'gsn_csm_vector_scalar',
                'gsn_csm_vector_scalar_map', 'gsn_csm_vector_scalar_map_ce',
                'gsn_csm_vector_scalar_map_polar', 'gsn_csm_x2y', 'gsn_csm_x2y2',
                'gsn_csm_xy', 'gsn_csm_xy2', 'gsn_csm_xy3', 'gsn_csm_y',
                'gsn_define_colormap', 'gsn_draw_colormap', 'gsn_draw_named_colors',
                'gsn_histogram', 'gsn_labelbar_ndc', 'gsn_legend_ndc', 'gsn_map',
                'gsn_merge_colormaps', 'gsn_open_wks', 'gsn_panel', 'gsn_polygon',
                'gsn_polygon_ndc', 'gsn_polyline', 'gsn_polyline_ndc',
                'gsn_polymarker', 'gsn_polymarker_ndc', 'gsn_retrieve_colormap',
                'gsn_reverse_colormap', 'gsn_streamline', 'gsn_streamline_map',
                'gsn_streamline_scalar', 'gsn_streamline_scalar_map', 'gsn_table',
                'gsn_text', 'gsn_text_ndc', 'gsn_vector', 'gsn_vector_map',
                'gsn_vector_scalar', 'gsn_vector_scalar_map', 'gsn_xy', 'gsn_y',
                'hsv2rgb', 'maximize_output', 'namedcolor2rgb', 'namedcolor2rgba',
                'reset_device_coordinates', 'span_named_colors'), prefix=r'\b'),
             Name.Builtin),

            # Resources
            (words((
                'amDataXF', 'amDataYF', 'amJust', 'amOn', 'amOrthogonalPosF',
                'amParallelPosF', 'amResizeNotify', 'amSide', 'amTrackData',
                'amViewId', 'amZone', 'appDefaultParent', 'appFileSuffix',
                'appResources', 'appSysDir', 'appUsrDir', 'caCopyArrays',
                'caXArray', 'caXCast', 'caXMaxV', 'caXMinV', 'caXMissingV',
                'caYArray', 'caYCast', 'caYMaxV', 'caYMinV', 'caYMissingV',
                'cnCellFillEdgeColor', 'cnCellFillMissingValEdgeColor',
                'cnConpackParams', 'cnConstFEnableFill', 'cnConstFLabelAngleF',
                'cnConstFLabelBackgroundColor', 'cnConstFLabelConstantSpacingF',
                'cnConstFLabelFont', 'cnConstFLabelFontAspectF',
                'cnConstFLabelFontColor', 'cnConstFLabelFontHeightF',
                'cnConstFLabelFontQuality', 'cnConstFLabelFontThicknessF',
                'cnConstFLabelFormat', 'cnConstFLabelFuncCode', 'cnConstFLabelJust',
                'cnConstFLabelOn', 'cnConstFLabelOrthogonalPosF',
                'cnConstFLabelParallelPosF', 'cnConstFLabelPerimColor',
                'cnConstFLabelPerimOn', 'cnConstFLabelPerimSpaceF',
                'cnConstFLabelPerimThicknessF', 'cnConstFLabelSide',
                'cnConstFLabelString', 'cnConstFLabelTextDirection',
                'cnConstFLabelZone', 'cnConstFUseInfoLabelRes',
                'cnExplicitLabelBarLabelsOn', 'cnExplicitLegendLabelsOn',
                'cnExplicitLineLabelsOn', 'cnFillBackgroundColor', 'cnFillColor',
                'cnFillColors', 'cnFillDotSizeF', 'cnFillDrawOrder', 'cnFillMode',
                'cnFillOn', 'cnFillOpacityF', 'cnFillPalette', 'cnFillPattern',
                'cnFillPatterns', 'cnFillScaleF', 'cnFillScales', 'cnFixFillBleed',
                'cnGridBoundFillColor', 'cnGridBoundFillPattern',
                'cnGridBoundFillScaleF', 'cnGridBoundPerimColor',
                'cnGridBoundPerimDashPattern', 'cnGridBoundPerimOn',
                'cnGridBoundPerimThicknessF', 'cnHighLabelAngleF',
                'cnHighLabelBackgroundColor', 'cnHighLabelConstantSpacingF',
                'cnHighLabelCount', 'cnHighLabelFont', 'cnHighLabelFontAspectF',
                'cnHighLabelFontColor', 'cnHighLabelFontHeightF',
                'cnHighLabelFontQuality', 'cnHighLabelFontThicknessF',
                'cnHighLabelFormat', 'cnHighLabelFuncCode', 'cnHighLabelPerimColor',
                'cnHighLabelPerimOn', 'cnHighLabelPerimSpaceF',
                'cnHighLabelPerimThicknessF', 'cnHighLabelString', 'cnHighLabelsOn',
                'cnHighLowLabelOverlapMode', 'cnHighUseLineLabelRes',
                'cnInfoLabelAngleF', 'cnInfoLabelBackgroundColor',
                'cnInfoLabelConstantSpacingF', 'cnInfoLabelFont',
                'cnInfoLabelFontAspectF', 'cnInfoLabelFontColor',
                'cnInfoLabelFontHeightF', 'cnInfoLabelFontQuality',
                'cnInfoLabelFontThicknessF', 'cnInfoLabelFormat',
                'cnInfoLabelFuncCode', 'cnInfoLabelJust', 'cnInfoLabelOn',
                'cnInfoLabelOrthogonalPosF', 'cnInfoLabelParallelPosF',
                'cnInfoLabelPerimColor', 'cnInfoLabelPerimOn',
                'cnInfoLabelPerimSpaceF', 'cnInfoLabelPerimThicknessF',
                'cnInfoLabelSide', 'cnInfoLabelString', 'cnInfoLabelTextDirection',
                'cnInfoLabelZone', 'cnLabelBarEndLabelsOn', 'cnLabelBarEndStyle',
                'cnLabelDrawOrder', 'cnLabelMasking', 'cnLabelScaleFactorF',
                'cnLabelScaleValueF', 'cnLabelScalingMode', 'cnLegendLevelFlags',
                'cnLevelCount', 'cnLevelFlag', 'cnLevelFlags', 'cnLevelSelectionMode',
                'cnLevelSpacingF', 'cnLevels', 'cnLineColor', 'cnLineColors',
                'cnLineDashPattern', 'cnLineDashPatterns', 'cnLineDashSegLenF',
                'cnLineDrawOrder', 'cnLineLabelAngleF', 'cnLineLabelBackgroundColor',
                'cnLineLabelConstantSpacingF', 'cnLineLabelCount',
                'cnLineLabelDensityF', 'cnLineLabelFont', 'cnLineLabelFontAspectF',
                'cnLineLabelFontColor', 'cnLineLabelFontColors',
                'cnLineLabelFontHeightF', 'cnLineLabelFontQuality',
                'cnLineLabelFontThicknessF', 'cnLineLabelFormat',
                'cnLineLabelFuncCode', 'cnLineLabelInterval', 'cnLineLabelPerimColor',
                'cnLineLabelPerimOn', 'cnLineLabelPerimSpaceF',
                'cnLineLabelPerimThicknessF', 'cnLineLabelPlacementMode',
                'cnLineLabelStrings', 'cnLineLabelsOn', 'cnLinePalette',
                'cnLineThicknessF', 'cnLineThicknesses', 'cnLinesOn',
                'cnLowLabelAngleF', 'cnLowLabelBackgroundColor',
                'cnLowLabelConstantSpacingF', 'cnLowLabelCount', 'cnLowLabelFont',
                'cnLowLabelFontAspectF', 'cnLowLabelFontColor',
                'cnLowLabelFontHeightF', 'cnLowLabelFontQuality',
                'cnLowLabelFontThicknessF', 'cnLowLabelFormat', 'cnLowLabelFuncCode',
                'cnLowLabelPerimColor', 'cnLowLabelPerimOn', 'cnLowLabelPerimSpaceF',
                'cnLowLabelPerimThicknessF', 'cnLowLabelString', 'cnLowLabelsOn',
                'cnLowUseHighLabelRes', 'cnMaxDataValueFormat', 'cnMaxLevelCount',
                'cnMaxLevelValF', 'cnMaxPointDistanceF', 'cnMinLevelValF',
                'cnMissingValFillColor', 'cnMissingValFillPattern',
                'cnMissingValFillScaleF', 'cnMissingValPerimColor',
                'cnMissingValPerimDashPattern', 'cnMissingValPerimGridBoundOn',
                'cnMissingValPerimOn', 'cnMissingValPerimThicknessF',
                'cnMonoFillColor', 'cnMonoFillPattern', 'cnMonoFillScale',
                'cnMonoLevelFlag', 'cnMonoLineColor', 'cnMonoLineDashPattern',
                'cnMonoLineLabelFontColor', 'cnMonoLineThickness', 'cnNoDataLabelOn',
                'cnNoDataLabelString', 'cnOutOfRangeFillColor',
                'cnOutOfRangeFillPattern', 'cnOutOfRangeFillScaleF',
                'cnOutOfRangePerimColor', 'cnOutOfRangePerimDashPattern',
                'cnOutOfRangePerimOn', 'cnOutOfRangePerimThicknessF',
                'cnRasterCellSizeF', 'cnRasterMinCellSizeF', 'cnRasterModeOn',
                'cnRasterSampleFactorF', 'cnRasterSmoothingOn', 'cnScalarFieldData',
                'cnSmoothingDistanceF', 'cnSmoothingOn', 'cnSmoothingTensionF',
                'cnSpanFillPalette', 'cnSpanLinePalette', 'ctCopyTables',
                'ctXElementSize', 'ctXMaxV', 'ctXMinV', 'ctXMissingV', 'ctXTable',
                'ctXTableLengths', 'ctXTableType', 'ctYElementSize', 'ctYMaxV',
                'ctYMinV', 'ctYMissingV', 'ctYTable', 'ctYTableLengths',
                'ctYTableType', 'dcDelayCompute', 'errBuffer',
                'errFileName', 'errFilePtr', 'errLevel', 'errPrint', 'errUnitNumber',
                'gsClipOn', 'gsColors', 'gsEdgeColor', 'gsEdgeDashPattern',
                'gsEdgeDashSegLenF', 'gsEdgeThicknessF', 'gsEdgesOn',
                'gsFillBackgroundColor', 'gsFillColor', 'gsFillDotSizeF',
                'gsFillIndex', 'gsFillLineThicknessF', 'gsFillOpacityF',
                'gsFillScaleF', 'gsFont', 'gsFontAspectF', 'gsFontColor',
                'gsFontHeightF', 'gsFontOpacityF', 'gsFontQuality',
                'gsFontThicknessF', 'gsLineColor', 'gsLineDashPattern',
                'gsLineDashSegLenF', 'gsLineLabelConstantSpacingF', 'gsLineLabelFont',
                'gsLineLabelFontAspectF', 'gsLineLabelFontColor',
                'gsLineLabelFontHeightF', 'gsLineLabelFontQuality',
                'gsLineLabelFontThicknessF', 'gsLineLabelFuncCode',
                'gsLineLabelString', 'gsLineOpacityF', 'gsLineThicknessF',
                'gsMarkerColor', 'gsMarkerIndex', 'gsMarkerOpacityF', 'gsMarkerSizeF',
                'gsMarkerThicknessF', 'gsSegments', 'gsTextAngleF',
                'gsTextConstantSpacingF', 'gsTextDirection', 'gsTextFuncCode',
                'gsTextJustification', 'gsnAboveYRefLineBarColors',
                'gsnAboveYRefLineBarFillScales', 'gsnAboveYRefLineBarPatterns',
                'gsnAboveYRefLineColor', 'gsnAddCyclic', 'gsnAttachBorderOn',
                'gsnAttachPlotsXAxis', 'gsnBelowYRefLineBarColors',
                'gsnBelowYRefLineBarFillScales', 'gsnBelowYRefLineBarPatterns',
                'gsnBelowYRefLineColor', 'gsnBoxMargin', 'gsnCenterString',
                'gsnCenterStringFontColor', 'gsnCenterStringFontHeightF',
                'gsnCenterStringFuncCode', 'gsnCenterStringOrthogonalPosF',
                'gsnCenterStringParallelPosF', 'gsnContourLineThicknessesScale',
                'gsnContourNegLineDashPattern', 'gsnContourPosLineDashPattern',
                'gsnContourZeroLineThicknessF', 'gsnDebugWriteFileName', 'gsnDraw',
                'gsnFrame', 'gsnHistogramBarWidthPercent', 'gsnHistogramBinIntervals',
                'gsnHistogramBinMissing', 'gsnHistogramBinWidth',
                'gsnHistogramClassIntervals', 'gsnHistogramCompare',
                'gsnHistogramComputePercentages',
                'gsnHistogramComputePercentagesNoMissing',
                'gsnHistogramDiscreteBinValues', 'gsnHistogramDiscreteClassValues',
                'gsnHistogramHorizontal', 'gsnHistogramMinMaxBinsOn',
                'gsnHistogramNumberOfBins', 'gsnHistogramPercentSign',
                'gsnHistogramSelectNiceIntervals', 'gsnLeftString',
                'gsnLeftStringFontColor', 'gsnLeftStringFontHeightF',
                'gsnLeftStringFuncCode', 'gsnLeftStringOrthogonalPosF',
                'gsnLeftStringParallelPosF', 'gsnMajorLatSpacing',
                'gsnMajorLonSpacing', 'gsnMaskLambertConformal',
                'gsnMaskLambertConformalOutlineOn', 'gsnMaximize',
                'gsnMinorLatSpacing', 'gsnMinorLonSpacing', 'gsnPanelBottom',
                'gsnPanelCenter', 'gsnPanelDebug', 'gsnPanelFigureStrings',
                'gsnPanelFigureStringsBackgroundFillColor',
                'gsnPanelFigureStringsFontHeightF', 'gsnPanelFigureStringsJust',
                'gsnPanelFigureStringsPerimOn', 'gsnPanelLabelBar', 'gsnPanelLeft',
                'gsnPanelMainFont', 'gsnPanelMainFontColor',
                'gsnPanelMainFontHeightF', 'gsnPanelMainString', 'gsnPanelRight',
                'gsnPanelRowSpec', 'gsnPanelScalePlotIndex', 'gsnPanelTop',
                'gsnPanelXF', 'gsnPanelXWhiteSpacePercent', 'gsnPanelYF',
                'gsnPanelYWhiteSpacePercent', 'gsnPaperHeight', 'gsnPaperMargin',
                'gsnPaperOrientation', 'gsnPaperWidth', 'gsnPolar',
                'gsnPolarLabelDistance', 'gsnPolarLabelFont',
                'gsnPolarLabelFontHeightF', 'gsnPolarLabelSpacing', 'gsnPolarTime',
                'gsnPolarUT', 'gsnRightString', 'gsnRightStringFontColor',
                'gsnRightStringFontHeightF', 'gsnRightStringFuncCode',
                'gsnRightStringOrthogonalPosF', 'gsnRightStringParallelPosF',
                'gsnScalarContour', 'gsnScale', 'gsnShape', 'gsnSpreadColorEnd',
                'gsnSpreadColorStart', 'gsnSpreadColors', 'gsnStringFont',
                'gsnStringFontColor', 'gsnStringFontHeightF', 'gsnStringFuncCode',
                'gsnTickMarksOn', 'gsnXAxisIrregular2Linear', 'gsnXAxisIrregular2Log',
                'gsnXRefLine', 'gsnXRefLineColor', 'gsnXRefLineDashPattern',
                'gsnXRefLineThicknessF', 'gsnXYAboveFillColors', 'gsnXYBarChart',
                'gsnXYBarChartBarWidth', 'gsnXYBarChartColors',
                'gsnXYBarChartColors2', 'gsnXYBarChartFillDotSizeF',
                'gsnXYBarChartFillLineThicknessF', 'gsnXYBarChartFillOpacityF',
                'gsnXYBarChartFillScaleF', 'gsnXYBarChartOutlineOnly',
                'gsnXYBarChartOutlineThicknessF', 'gsnXYBarChartPatterns',
                'gsnXYBarChartPatterns2', 'gsnXYBelowFillColors', 'gsnXYFillColors',
                'gsnXYFillOpacities', 'gsnXYLeftFillColors', 'gsnXYRightFillColors',
                'gsnYAxisIrregular2Linear', 'gsnYAxisIrregular2Log', 'gsnYRefLine',
                'gsnYRefLineColor', 'gsnYRefLineColors', 'gsnYRefLineDashPattern',
                'gsnYRefLineDashPatterns', 'gsnYRefLineThicknessF',
                'gsnYRefLineThicknesses', 'gsnZonalMean', 'gsnZonalMeanXMaxF',
                'gsnZonalMeanXMinF', 'gsnZonalMeanYRefLine', 'lbAutoManage',
                'lbBottomMarginF', 'lbBoxCount', 'lbBoxEndCapStyle', 'lbBoxFractions',
                'lbBoxLineColor', 'lbBoxLineDashPattern', 'lbBoxLineDashSegLenF',
                'lbBoxLineThicknessF', 'lbBoxLinesOn', 'lbBoxMajorExtentF',
                'lbBoxMinorExtentF', 'lbBoxSeparatorLinesOn', 'lbBoxSizing',
                'lbFillBackground', 'lbFillColor', 'lbFillColors', 'lbFillDotSizeF',
                'lbFillLineThicknessF', 'lbFillPattern', 'lbFillPatterns',
                'lbFillScaleF', 'lbFillScales', 'lbJustification', 'lbLabelAlignment',
                'lbLabelAngleF', 'lbLabelAutoStride', 'lbLabelBarOn',
                'lbLabelConstantSpacingF', 'lbLabelDirection', 'lbLabelFont',
                'lbLabelFontAspectF', 'lbLabelFontColor', 'lbLabelFontHeightF',
                'lbLabelFontQuality', 'lbLabelFontThicknessF', 'lbLabelFuncCode',
                'lbLabelJust', 'lbLabelOffsetF', 'lbLabelPosition', 'lbLabelStride',
                'lbLabelStrings', 'lbLabelsOn', 'lbLeftMarginF', 'lbMaxLabelLenF',
                'lbMinLabelSpacingF', 'lbMonoFillColor', 'lbMonoFillPattern',
                'lbMonoFillScale', 'lbOrientation', 'lbPerimColor',
                'lbPerimDashPattern', 'lbPerimDashSegLenF', 'lbPerimFill',
                'lbPerimFillColor', 'lbPerimOn', 'lbPerimThicknessF',
                'lbRasterFillOn', 'lbRightMarginF', 'lbTitleAngleF',
                'lbTitleConstantSpacingF', 'lbTitleDirection', 'lbTitleExtentF',
                'lbTitleFont', 'lbTitleFontAspectF', 'lbTitleFontColor',
                'lbTitleFontHeightF', 'lbTitleFontQuality', 'lbTitleFontThicknessF',
                'lbTitleFuncCode', 'lbTitleJust', 'lbTitleOffsetF', 'lbTitleOn',
                'lbTitlePosition', 'lbTitleString', 'lbTopMarginF', 'lgAutoManage',
                'lgBottomMarginF', 'lgBoxBackground', 'lgBoxLineColor',
                'lgBoxLineDashPattern', 'lgBoxLineDashSegLenF', 'lgBoxLineThicknessF',
                'lgBoxLinesOn', 'lgBoxMajorExtentF', 'lgBoxMinorExtentF',
                'lgDashIndex', 'lgDashIndexes', 'lgItemCount', 'lgItemOrder',
                'lgItemPlacement', 'lgItemPositions', 'lgItemType', 'lgItemTypes',
                'lgJustification', 'lgLabelAlignment', 'lgLabelAngleF',
                'lgLabelAutoStride', 'lgLabelConstantSpacingF', 'lgLabelDirection',
                'lgLabelFont', 'lgLabelFontAspectF', 'lgLabelFontColor',
                'lgLabelFontHeightF', 'lgLabelFontQuality', 'lgLabelFontThicknessF',
                'lgLabelFuncCode', 'lgLabelJust', 'lgLabelOffsetF', 'lgLabelPosition',
                'lgLabelStride', 'lgLabelStrings', 'lgLabelsOn', 'lgLeftMarginF',
                'lgLegendOn', 'lgLineColor', 'lgLineColors', 'lgLineDashSegLenF',
                'lgLineDashSegLens', 'lgLineLabelConstantSpacingF', 'lgLineLabelFont',
                'lgLineLabelFontAspectF', 'lgLineLabelFontColor',
                'lgLineLabelFontColors', 'lgLineLabelFontHeightF',
                'lgLineLabelFontHeights', 'lgLineLabelFontQuality',
                'lgLineLabelFontThicknessF', 'lgLineLabelFuncCode',
                'lgLineLabelStrings', 'lgLineLabelsOn', 'lgLineThicknessF',
                'lgLineThicknesses', 'lgMarkerColor', 'lgMarkerColors',
                'lgMarkerIndex', 'lgMarkerIndexes', 'lgMarkerSizeF', 'lgMarkerSizes',
                'lgMarkerThicknessF', 'lgMarkerThicknesses', 'lgMonoDashIndex',
                'lgMonoItemType', 'lgMonoLineColor', 'lgMonoLineDashSegLen',
                'lgMonoLineLabelFontColor', 'lgMonoLineLabelFontHeight',
                'lgMonoLineThickness', 'lgMonoMarkerColor', 'lgMonoMarkerIndex',
                'lgMonoMarkerSize', 'lgMonoMarkerThickness', 'lgOrientation',
                'lgPerimColor', 'lgPerimDashPattern', 'lgPerimDashSegLenF',
                'lgPerimFill', 'lgPerimFillColor', 'lgPerimOn', 'lgPerimThicknessF',
                'lgRightMarginF', 'lgTitleAngleF', 'lgTitleConstantSpacingF',
                'lgTitleDirection', 'lgTitleExtentF', 'lgTitleFont',
                'lgTitleFontAspectF', 'lgTitleFontColor', 'lgTitleFontHeightF',
                'lgTitleFontQuality', 'lgTitleFontThicknessF', 'lgTitleFuncCode',
                'lgTitleJust', 'lgTitleOffsetF', 'lgTitleOn', 'lgTitlePosition',
                'lgTitleString', 'lgTopMarginF', 'mpAreaGroupCount',
                'mpAreaMaskingOn', 'mpAreaNames', 'mpAreaTypes', 'mpBottomAngleF',
                'mpBottomMapPosF', 'mpBottomNDCF', 'mpBottomNPCF',
                'mpBottomPointLatF', 'mpBottomPointLonF', 'mpBottomWindowF',
                'mpCenterLatF', 'mpCenterLonF', 'mpCenterRotF', 'mpCountyLineColor',
                'mpCountyLineDashPattern', 'mpCountyLineDashSegLenF',
                'mpCountyLineThicknessF', 'mpDataBaseVersion', 'mpDataResolution',
                'mpDataSetName', 'mpDefaultFillColor', 'mpDefaultFillPattern',
                'mpDefaultFillScaleF', 'mpDynamicAreaGroups', 'mpEllipticalBoundary',
                'mpFillAreaSpecifiers', 'mpFillBoundarySets', 'mpFillColor',
                'mpFillColors', 'mpFillColors-default', 'mpFillDotSizeF',
                'mpFillDrawOrder', 'mpFillOn', 'mpFillPatternBackground',
                'mpFillPattern', 'mpFillPatterns', 'mpFillPatterns-default',
                'mpFillScaleF', 'mpFillScales', 'mpFillScales-default',
                'mpFixedAreaGroups', 'mpGeophysicalLineColor',
                'mpGeophysicalLineDashPattern', 'mpGeophysicalLineDashSegLenF',
                'mpGeophysicalLineThicknessF', 'mpGreatCircleLinesOn',
                'mpGridAndLimbDrawOrder', 'mpGridAndLimbOn', 'mpGridLatSpacingF',
                'mpGridLineColor', 'mpGridLineDashPattern', 'mpGridLineDashSegLenF',
                'mpGridLineThicknessF', 'mpGridLonSpacingF', 'mpGridMaskMode',
                'mpGridMaxLatF', 'mpGridPolarLonSpacingF', 'mpGridSpacingF',
                'mpInlandWaterFillColor', 'mpInlandWaterFillPattern',
                'mpInlandWaterFillScaleF', 'mpLabelDrawOrder', 'mpLabelFontColor',
                'mpLabelFontHeightF', 'mpLabelsOn', 'mpLambertMeridianF',
                'mpLambertParallel1F', 'mpLambertParallel2F', 'mpLandFillColor',
                'mpLandFillPattern', 'mpLandFillScaleF', 'mpLeftAngleF',
                'mpLeftCornerLatF', 'mpLeftCornerLonF', 'mpLeftMapPosF',
                'mpLeftNDCF', 'mpLeftNPCF', 'mpLeftPointLatF',
                'mpLeftPointLonF', 'mpLeftWindowF', 'mpLimbLineColor',
                'mpLimbLineDashPattern', 'mpLimbLineDashSegLenF',
                'mpLimbLineThicknessF', 'mpLimitMode', 'mpMaskAreaSpecifiers',
                'mpMaskOutlineSpecifiers', 'mpMaxLatF', 'mpMaxLonF',
                'mpMinLatF', 'mpMinLonF', 'mpMonoFillColor', 'mpMonoFillPattern',
                'mpMonoFillScale', 'mpNationalLineColor', 'mpNationalLineDashPattern',
                'mpNationalLineThicknessF', 'mpOceanFillColor', 'mpOceanFillPattern',
                'mpOceanFillScaleF', 'mpOutlineBoundarySets', 'mpOutlineDrawOrder',
                'mpOutlineMaskingOn', 'mpOutlineOn', 'mpOutlineSpecifiers',
                'mpPerimDrawOrder', 'mpPerimLineColor', 'mpPerimLineDashPattern',
                'mpPerimLineDashSegLenF', 'mpPerimLineThicknessF', 'mpPerimOn',
                'mpPolyMode', 'mpProjection', 'mpProvincialLineColor',
                'mpProvincialLineDashPattern', 'mpProvincialLineDashSegLenF',
                'mpProvincialLineThicknessF', 'mpRelativeCenterLat',
                'mpRelativeCenterLon', 'mpRightAngleF', 'mpRightCornerLatF',
                'mpRightCornerLonF', 'mpRightMapPosF', 'mpRightNDCF',
                'mpRightNPCF', 'mpRightPointLatF', 'mpRightPointLonF',
                'mpRightWindowF', 'mpSatelliteAngle1F', 'mpSatelliteAngle2F',
                'mpSatelliteDistF', 'mpShapeMode', 'mpSpecifiedFillColors',
                'mpSpecifiedFillDirectIndexing', 'mpSpecifiedFillPatterns',
                'mpSpecifiedFillPriority', 'mpSpecifiedFillScales',
                'mpTopAngleF', 'mpTopMapPosF', 'mpTopNDCF', 'mpTopNPCF',
                'mpTopPointLatF', 'mpTopPointLonF', 'mpTopWindowF',
                'mpUSStateLineColor', 'mpUSStateLineDashPattern',
                'mpUSStateLineDashSegLenF', 'mpUSStateLineThicknessF',
                'pmAnnoManagers', 'pmAnnoViews', 'pmLabelBarDisplayMode',
                'pmLabelBarHeightF', 'pmLabelBarKeepAspect', 'pmLabelBarOrthogonalPosF',
                'pmLabelBarParallelPosF', 'pmLabelBarSide', 'pmLabelBarWidthF',
                'pmLabelBarZone', 'pmLegendDisplayMode', 'pmLegendHeightF',
                'pmLegendKeepAspect', 'pmLegendOrthogonalPosF',
                'pmLegendParallelPosF', 'pmLegendSide', 'pmLegendWidthF',
                'pmLegendZone', 'pmOverlaySequenceIds', 'pmTickMarkDisplayMode',
                'pmTickMarkZone', 'pmTitleDisplayMode', 'pmTitleZone',
                'prGraphicStyle', 'prPolyType', 'prXArray', 'prYArray',
                'sfCopyData', 'sfDataArray', 'sfDataMaxV', 'sfDataMinV',
                'sfElementNodes', 'sfExchangeDimensions', 'sfFirstNodeIndex',
                'sfMissingValueV', 'sfXArray', 'sfXCActualEndF', 'sfXCActualStartF',
                'sfXCEndIndex', 'sfXCEndSubsetV', 'sfXCEndV', 'sfXCStartIndex',
                'sfXCStartSubsetV', 'sfXCStartV', 'sfXCStride', 'sfXCellBounds',
                'sfYArray', 'sfYCActualEndF', 'sfYCActualStartF', 'sfYCEndIndex',
                'sfYCEndSubsetV', 'sfYCEndV', 'sfYCStartIndex', 'sfYCStartSubsetV',
                'sfYCStartV', 'sfYCStride', 'sfYCellBounds', 'stArrowLengthF',
                'stArrowStride', 'stCrossoverCheckCount',
                'stExplicitLabelBarLabelsOn', 'stLabelBarEndLabelsOn',
                'stLabelFormat', 'stLengthCheckCount', 'stLevelColors',
                'stLevelCount', 'stLevelPalette', 'stLevelSelectionMode',
                'stLevelSpacingF', 'stLevels', 'stLineColor', 'stLineOpacityF',
                'stLineStartStride', 'stLineThicknessF', 'stMapDirection',
                'stMaxLevelCount', 'stMaxLevelValF', 'stMinArrowSpacingF',
                'stMinDistanceF', 'stMinLevelValF', 'stMinLineSpacingF',
                'stMinStepFactorF', 'stMonoLineColor', 'stNoDataLabelOn',
                'stNoDataLabelString', 'stScalarFieldData', 'stScalarMissingValColor',
                'stSpanLevelPalette', 'stStepSizeF', 'stStreamlineDrawOrder',
                'stUseScalarArray', 'stVectorFieldData', 'stZeroFLabelAngleF',
                'stZeroFLabelBackgroundColor', 'stZeroFLabelConstantSpacingF',
                'stZeroFLabelFont', 'stZeroFLabelFontAspectF',
                'stZeroFLabelFontColor', 'stZeroFLabelFontHeightF',
                'stZeroFLabelFontQuality', 'stZeroFLabelFontThicknessF',
                'stZeroFLabelFuncCode', 'stZeroFLabelJust', 'stZeroFLabelOn',
                'stZeroFLabelOrthogonalPosF', 'stZeroFLabelParallelPosF',
                'stZeroFLabelPerimColor', 'stZeroFLabelPerimOn',
                'stZeroFLabelPerimSpaceF', 'stZeroFLabelPerimThicknessF',
                'stZeroFLabelSide', 'stZeroFLabelString', 'stZeroFLabelTextDirection',
                'stZeroFLabelZone', 'tfDoNDCOverlay', 'tfPlotManagerOn',
                'tfPolyDrawList', 'tfPolyDrawOrder', 'tiDeltaF', 'tiMainAngleF',
                'tiMainConstantSpacingF', 'tiMainDirection', 'tiMainFont',
                'tiMainFontAspectF', 'tiMainFontColor', 'tiMainFontHeightF',
                'tiMainFontQuality', 'tiMainFontThicknessF', 'tiMainFuncCode',
                'tiMainJust', 'tiMainOffsetXF', 'tiMainOffsetYF', 'tiMainOn',
                'tiMainPosition', 'tiMainSide', 'tiMainString', 'tiUseMainAttributes',
                'tiXAxisAngleF', 'tiXAxisConstantSpacingF', 'tiXAxisDirection',
                'tiXAxisFont', 'tiXAxisFontAspectF', 'tiXAxisFontColor',
                'tiXAxisFontHeightF', 'tiXAxisFontQuality', 'tiXAxisFontThicknessF',
                'tiXAxisFuncCode', 'tiXAxisJust', 'tiXAxisOffsetXF',
                'tiXAxisOffsetYF', 'tiXAxisOn', 'tiXAxisPosition', 'tiXAxisSide',
                'tiXAxisString', 'tiYAxisAngleF', 'tiYAxisConstantSpacingF',
                'tiYAxisDirection', 'tiYAxisFont', 'tiYAxisFontAspectF',
                'tiYAxisFontColor', 'tiYAxisFontHeightF', 'tiYAxisFontQuality',
                'tiYAxisFontThicknessF', 'tiYAxisFuncCode', 'tiYAxisJust',
                'tiYAxisOffsetXF', 'tiYAxisOffsetYF', 'tiYAxisOn', 'tiYAxisPosition',
                'tiYAxisSide', 'tiYAxisString', 'tmBorderLineColor',
                'tmBorderThicknessF', 'tmEqualizeXYSizes', 'tmLabelAutoStride',
                'tmSciNoteCutoff', 'tmXBAutoPrecision', 'tmXBBorderOn',
                'tmXBDataLeftF', 'tmXBDataRightF', 'tmXBFormat', 'tmXBIrrTensionF',
                'tmXBIrregularPoints', 'tmXBLabelAngleF', 'tmXBLabelConstantSpacingF',
                'tmXBLabelDeltaF', 'tmXBLabelDirection', 'tmXBLabelFont',
                'tmXBLabelFontAspectF', 'tmXBLabelFontColor', 'tmXBLabelFontHeightF',
                'tmXBLabelFontQuality', 'tmXBLabelFontThicknessF',
                'tmXBLabelFuncCode', 'tmXBLabelJust', 'tmXBLabelStride', 'tmXBLabels',
                'tmXBLabelsOn', 'tmXBMajorLengthF', 'tmXBMajorLineColor',
                'tmXBMajorOutwardLengthF', 'tmXBMajorThicknessF', 'tmXBMaxLabelLenF',
                'tmXBMaxTicks', 'tmXBMinLabelSpacingF', 'tmXBMinorLengthF',
                'tmXBMinorLineColor', 'tmXBMinorOn', 'tmXBMinorOutwardLengthF',
                'tmXBMinorPerMajor', 'tmXBMinorThicknessF', 'tmXBMinorValues',
                'tmXBMode', 'tmXBOn', 'tmXBPrecision', 'tmXBStyle', 'tmXBTickEndF',
                'tmXBTickSpacingF', 'tmXBTickStartF', 'tmXBValues', 'tmXMajorGrid',
                'tmXMajorGridLineColor', 'tmXMajorGridLineDashPattern',
                'tmXMajorGridThicknessF', 'tmXMinorGrid', 'tmXMinorGridLineColor',
                'tmXMinorGridLineDashPattern', 'tmXMinorGridThicknessF',
                'tmXTAutoPrecision', 'tmXTBorderOn', 'tmXTDataLeftF',
                'tmXTDataRightF', 'tmXTFormat', 'tmXTIrrTensionF',
                'tmXTIrregularPoints', 'tmXTLabelAngleF', 'tmXTLabelConstantSpacingF',
                'tmXTLabelDeltaF', 'tmXTLabelDirection', 'tmXTLabelFont',
                'tmXTLabelFontAspectF', 'tmXTLabelFontColor', 'tmXTLabelFontHeightF',
                'tmXTLabelFontQuality', 'tmXTLabelFontThicknessF',
                'tmXTLabelFuncCode', 'tmXTLabelJust', 'tmXTLabelStride', 'tmXTLabels',
                'tmXTLabelsOn', 'tmXTMajorLengthF', 'tmXTMajorLineColor',
                'tmXTMajorOutwardLengthF', 'tmXTMajorThicknessF', 'tmXTMaxLabelLenF',
                'tmXTMaxTicks', 'tmXTMinLabelSpacingF', 'tmXTMinorLengthF',
                'tmXTMinorLineColor', 'tmXTMinorOn', 'tmXTMinorOutwardLengthF',
                'tmXTMinorPerMajor', 'tmXTMinorThicknessF', 'tmXTMinorValues',
                'tmXTMode', 'tmXTOn', 'tmXTPrecision', 'tmXTStyle', 'tmXTTickEndF',
                'tmXTTickSpacingF', 'tmXTTickStartF', 'tmXTValues', 'tmXUseBottom',
                'tmYLAutoPrecision', 'tmYLBorderOn', 'tmYLDataBottomF',
                'tmYLDataTopF', 'tmYLFormat', 'tmYLIrrTensionF',
                'tmYLIrregularPoints', 'tmYLLabelAngleF', 'tmYLLabelConstantSpacingF',
                'tmYLLabelDeltaF', 'tmYLLabelDirection', 'tmYLLabelFont',
                'tmYLLabelFontAspectF', 'tmYLLabelFontColor', 'tmYLLabelFontHeightF',
                'tmYLLabelFontQuality', 'tmYLLabelFontThicknessF',
                'tmYLLabelFuncCode', 'tmYLLabelJust', 'tmYLLabelStride', 'tmYLLabels',
                'tmYLLabelsOn', 'tmYLMajorLengthF', 'tmYLMajorLineColor',
                'tmYLMajorOutwardLengthF', 'tmYLMajorThicknessF', 'tmYLMaxLabelLenF',
                'tmYLMaxTicks', 'tmYLMinLabelSpacingF', 'tmYLMinorLengthF',
                'tmYLMinorLineColor', 'tmYLMinorOn', 'tmYLMinorOutwardLengthF',
                'tmYLMinorPerMajor', 'tmYLMinorThicknessF', 'tmYLMinorValues',
                'tmYLMode', 'tmYLOn', 'tmYLPrecision', 'tmYLStyle', 'tmYLTickEndF',
                'tmYLTickSpacingF', 'tmYLTickStartF', 'tmYLValues', 'tmYMajorGrid',
                'tmYMajorGridLineColor', 'tmYMajorGridLineDashPattern',
                'tmYMajorGridThicknessF', 'tmYMinorGrid', 'tmYMinorGridLineColor',
                'tmYMinorGridLineDashPattern', 'tmYMinorGridThicknessF',
                'tmYRAutoPrecision', 'tmYRBorderOn', 'tmYRDataBottomF',
                'tmYRDataTopF', 'tmYRFormat', 'tmYRIrrTensionF',
                'tmYRIrregularPoints', 'tmYRLabelAngleF', 'tmYRLabelConstantSpacingF',
                'tmYRLabelDeltaF', 'tmYRLabelDirection', 'tmYRLabelFont',
                'tmYRLabelFontAspectF', 'tmYRLabelFontColor', 'tmYRLabelFontHeightF',
                'tmYRLabelFontQuality', 'tmYRLabelFontThicknessF',
                'tmYRLabelFuncCode', 'tmYRLabelJust', 'tmYRLabelStride', 'tmYRLabels',
                'tmYRLabelsOn', 'tmYRMajorLengthF', 'tmYRMajorLineColor',
                'tmYRMajorOutwardLengthF', 'tmYRMajorThicknessF', 'tmYRMaxLabelLenF',
                'tmYRMaxTicks', 'tmYRMinLabelSpacingF', 'tmYRMinorLengthF',
                'tmYRMinorLineColor', 'tmYRMinorOn', 'tmYRMinorOutwardLengthF',
                'tmYRMinorPerMajor', 'tmYRMinorThicknessF', 'tmYRMinorValues',
                'tmYRMode', 'tmYROn', 'tmYRPrecision', 'tmYRStyle', 'tmYRTickEndF',
                'tmYRTickSpacingF', 'tmYRTickStartF', 'tmYRValues', 'tmYUseLeft',
                'trGridType', 'trLineInterpolationOn',
                'trXAxisType', 'trXCoordPoints', 'trXInterPoints', 'trXLog',
                'trXMaxF', 'trXMinF', 'trXReverse', 'trXSamples', 'trXTensionF',
                'trYAxisType', 'trYCoordPoints', 'trYInterPoints', 'trYLog',
                'trYMaxF', 'trYMinF', 'trYReverse', 'trYSamples', 'trYTensionF',
                'txAngleF', 'txBackgroundFillColor', 'txConstantSpacingF', 'txDirection',
                'txFont', 'HLU-Fonts', 'txFontAspectF', 'txFontColor',
                'txFontHeightF', 'txFontOpacityF', 'txFontQuality',
                'txFontThicknessF', 'txFuncCode', 'txJust', 'txPerimColor',
                'txPerimDashLengthF', 'txPerimDashPattern', 'txPerimOn',
                'txPerimSpaceF', 'txPerimThicknessF', 'txPosXF', 'txPosYF',
                'txString', 'vcExplicitLabelBarLabelsOn', 'vcFillArrowEdgeColor',
                'vcFillArrowEdgeThicknessF', 'vcFillArrowFillColor',
                'vcFillArrowHeadInteriorXF', 'vcFillArrowHeadMinFracXF',
                'vcFillArrowHeadMinFracYF', 'vcFillArrowHeadXF', 'vcFillArrowHeadYF',
                'vcFillArrowMinFracWidthF', 'vcFillArrowWidthF', 'vcFillArrowsOn',
                'vcFillOverEdge', 'vcGlyphOpacityF', 'vcGlyphStyle',
                'vcLabelBarEndLabelsOn', 'vcLabelFontColor', 'vcLabelFontHeightF',
                'vcLabelsOn', 'vcLabelsUseVectorColor', 'vcLevelColors',
                'vcLevelCount', 'vcLevelPalette', 'vcLevelSelectionMode',
                'vcLevelSpacingF', 'vcLevels', 'vcLineArrowColor',
                'vcLineArrowHeadMaxSizeF', 'vcLineArrowHeadMinSizeF',
                'vcLineArrowThicknessF', 'vcMagnitudeFormat',
                'vcMagnitudeScaleFactorF', 'vcMagnitudeScaleValueF',
                'vcMagnitudeScalingMode', 'vcMapDirection', 'vcMaxLevelCount',
                'vcMaxLevelValF', 'vcMaxMagnitudeF', 'vcMinAnnoAngleF',
                'vcMinAnnoArrowAngleF', 'vcMinAnnoArrowEdgeColor',
                'vcMinAnnoArrowFillColor', 'vcMinAnnoArrowLineColor',
                'vcMinAnnoArrowMinOffsetF', 'vcMinAnnoArrowSpaceF',
                'vcMinAnnoArrowUseVecColor', 'vcMinAnnoBackgroundColor',
                'vcMinAnnoConstantSpacingF', 'vcMinAnnoExplicitMagnitudeF',
                'vcMinAnnoFont', 'vcMinAnnoFontAspectF', 'vcMinAnnoFontColor',
                'vcMinAnnoFontHeightF', 'vcMinAnnoFontQuality',
                'vcMinAnnoFontThicknessF', 'vcMinAnnoFuncCode', 'vcMinAnnoJust',
                'vcMinAnnoOn', 'vcMinAnnoOrientation', 'vcMinAnnoOrthogonalPosF',
                'vcMinAnnoParallelPosF', 'vcMinAnnoPerimColor', 'vcMinAnnoPerimOn',
                'vcMinAnnoPerimSpaceF', 'vcMinAnnoPerimThicknessF', 'vcMinAnnoSide',
                'vcMinAnnoString1', 'vcMinAnnoString1On', 'vcMinAnnoString2',
                'vcMinAnnoString2On', 'vcMinAnnoTextDirection', 'vcMinAnnoZone',
                'vcMinDistanceF', 'vcMinFracLengthF', 'vcMinLevelValF',
                'vcMinMagnitudeF', 'vcMonoFillArrowEdgeColor',
                'vcMonoFillArrowFillColor', 'vcMonoLineArrowColor',
                'vcMonoWindBarbColor', 'vcNoDataLabelOn', 'vcNoDataLabelString',
                'vcPositionMode', 'vcRefAnnoAngleF', 'vcRefAnnoArrowAngleF',
                'vcRefAnnoArrowEdgeColor', 'vcRefAnnoArrowFillColor',
                'vcRefAnnoArrowLineColor', 'vcRefAnnoArrowMinOffsetF',
                'vcRefAnnoArrowSpaceF', 'vcRefAnnoArrowUseVecColor',
                'vcRefAnnoBackgroundColor', 'vcRefAnnoConstantSpacingF',
                'vcRefAnnoExplicitMagnitudeF', 'vcRefAnnoFont',
                'vcRefAnnoFontAspectF', 'vcRefAnnoFontColor', 'vcRefAnnoFontHeightF',
                'vcRefAnnoFontQuality', 'vcRefAnnoFontThicknessF',
                'vcRefAnnoFuncCode', 'vcRefAnnoJust', 'vcRefAnnoOn',
                'vcRefAnnoOrientation', 'vcRefAnnoOrthogonalPosF',
                'vcRefAnnoParallelPosF', 'vcRefAnnoPerimColor', 'vcRefAnnoPerimOn',
                'vcRefAnnoPerimSpaceF', 'vcRefAnnoPerimThicknessF', 'vcRefAnnoSide',
                'vcRefAnnoString1', 'vcRefAnnoString1On', 'vcRefAnnoString2',
                'vcRefAnnoString2On', 'vcRefAnnoTextDirection', 'vcRefAnnoZone',
                'vcRefLengthF', 'vcRefMagnitudeF', 'vcScalarFieldData',
                'vcScalarMissingValColor', 'vcScalarValueFormat',
                'vcScalarValueScaleFactorF', 'vcScalarValueScaleValueF',
                'vcScalarValueScalingMode', 'vcSpanLevelPalette', 'vcUseRefAnnoRes',
                'vcUseScalarArray', 'vcVectorDrawOrder', 'vcVectorFieldData',
                'vcWindBarbCalmCircleSizeF', 'vcWindBarbColor',
                'vcWindBarbLineThicknessF', 'vcWindBarbScaleFactorF',
                'vcWindBarbTickAngleF', 'vcWindBarbTickLengthF',
                'vcWindBarbTickSpacingF', 'vcZeroFLabelAngleF',
                'vcZeroFLabelBackgroundColor', 'vcZeroFLabelConstantSpacingF',
                'vcZeroFLabelFont', 'vcZeroFLabelFontAspectF',
                'vcZeroFLabelFontColor', 'vcZeroFLabelFontHeightF',
                'vcZeroFLabelFontQuality', 'vcZeroFLabelFontThicknessF',
                'vcZeroFLabelFuncCode', 'vcZeroFLabelJust', 'vcZeroFLabelOn',
                'vcZeroFLabelOrthogonalPosF', 'vcZeroFLabelParallelPosF',
                'vcZeroFLabelPerimColor', 'vcZeroFLabelPerimOn',
                'vcZeroFLabelPerimSpaceF', 'vcZeroFLabelPerimThicknessF',
                'vcZeroFLabelSide', 'vcZeroFLabelString', 'vcZeroFLabelTextDirection',
                'vcZeroFLabelZone', 'vfCopyData', 'vfDataArray',
                'vfExchangeDimensions', 'vfExchangeUVData', 'vfMagMaxV', 'vfMagMinV',
                'vfMissingUValueV', 'vfMissingVValueV', 'vfPolarData',
                'vfSingleMissingValue', 'vfUDataArray', 'vfUMaxV', 'vfUMinV',
                'vfVDataArray', 'vfVMaxV', 'vfVMinV', 'vfXArray', 'vfXCActualEndF',
                'vfXCActualStartF', 'vfXCEndIndex', 'vfXCEndSubsetV', 'vfXCEndV',
                'vfXCStartIndex', 'vfXCStartSubsetV', 'vfXCStartV', 'vfXCStride',
                'vfYArray', 'vfYCActualEndF', 'vfYCActualStartF', 'vfYCEndIndex',
                'vfYCEndSubsetV', 'vfYCEndV', 'vfYCStartIndex', 'vfYCStartSubsetV',
                'vfYCStartV', 'vfYCStride', 'vpAnnoManagerId', 'vpClipOn',
                'vpHeightF', 'vpKeepAspect', 'vpOn', 'vpUseSegments', 'vpWidthF',
                'vpXF', 'vpYF', 'wkAntiAlias', 'wkBackgroundColor', 'wkBackgroundOpacityF',
                'wkColorMapLen', 'wkColorMap', 'wkColorModel', 'wkDashTableLength',
                'wkDefGraphicStyleId', 'wkDeviceLowerX', 'wkDeviceLowerY',
                'wkDeviceUpperX', 'wkDeviceUpperY', 'wkFileName', 'wkFillTableLength',
                'wkForegroundColor', 'wkFormat', 'wkFullBackground', 'wkGksWorkId',
                'wkHeight', 'wkMarkerTableLength', 'wkMetaName', 'wkOrientation',
                'wkPDFFileName', 'wkPDFFormat', 'wkPDFResolution', 'wkPSFileName',
                'wkPSFormat', 'wkPSResolution', 'wkPaperHeightF', 'wkPaperSize',
                'wkPaperWidthF', 'wkPause', 'wkTopLevelViews', 'wkViews',
                'wkVisualType', 'wkWidth', 'wkWindowId', 'wkXColorMode', 'wsCurrentSize',
                'wsMaximumSize', 'wsThresholdSize', 'xyComputeXMax',
                'xyComputeXMin', 'xyComputeYMax', 'xyComputeYMin', 'xyCoordData',
                'xyCoordDataSpec', 'xyCurveDrawOrder', 'xyDashPattern',
                'xyDashPatterns', 'xyExplicitLabels', 'xyExplicitLegendLabels',
                'xyLabelMode', 'xyLineColor', 'xyLineColors', 'xyLineDashSegLenF',
                'xyLineLabelConstantSpacingF', 'xyLineLabelFont',
                'xyLineLabelFontAspectF', 'xyLineLabelFontColor',
                'xyLineLabelFontColors', 'xyLineLabelFontHeightF',
                'xyLineLabelFontQuality', 'xyLineLabelFontThicknessF',
                'xyLineLabelFuncCode', 'xyLineThicknessF', 'xyLineThicknesses',
                'xyMarkLineMode', 'xyMarkLineModes', 'xyMarker', 'xyMarkerColor',
                'xyMarkerColors', 'xyMarkerSizeF', 'xyMarkerSizes',
                'xyMarkerThicknessF', 'xyMarkerThicknesses', 'xyMarkers',
                'xyMonoDashPattern', 'xyMonoLineColor', 'xyMonoLineLabelFontColor',
                'xyMonoLineThickness', 'xyMonoMarkLineMode', 'xyMonoMarker',
                'xyMonoMarkerColor', 'xyMonoMarkerSize', 'xyMonoMarkerThickness',
                'xyXIrrTensionF', 'xyXIrregularPoints', 'xyXStyle', 'xyYIrrTensionF',
                'xyYIrregularPoints', 'xyYStyle'), prefix=r'\b'),
             Name.Builtin),

            # Booleans
            (r'\.(True|False)\.', Name.Builtin),
            # Comparing Operators
            (r'\.(eq|ne|lt|le|gt|ge|not|and|or|xor)\.', Operator.Word),
        ],

        'strings': [
            (r'(?s)"(\\\\|\\[0-7]+|\\.|[^"\\])*"', String.Double),
        ],

        'nums': [
            (r'\d+(?![.e])(_[a-z]\w+)?', Number.Integer),
            (r'[+-]?\d*\.\d+(e[-+]?\d+)?(_[a-z]\w+)?', Number.Float),
            (r'[+-]?\d+\.\d*(e[-+]?\d+)?(_[a-z]\w+)?', Number.Float),
        ],
    }
