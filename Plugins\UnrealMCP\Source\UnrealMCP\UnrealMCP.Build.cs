// Copyright Epic Games, Inc. All Rights Reserved.

using UnrealBuildTool;

public class UnrealMCP : ModuleRules
{
	public UnrealMCP(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
		// Use IWYUSupport instead of the deprecated bEnforceIWYU in UE5.5
		IWYUSupport = IWYUSupport.Full;

		PublicIncludePaths.AddRange(
			new string[] {
				// ... add public include paths required here ...
			}
		);
		
		PrivateIncludePaths.AddRange(
			new string[] {
				// ... add other private include paths required here ...
			}
		);
		
		PublicDependencyModuleNames.AddRange(
			new string[]
			{
				"Core",
				"CoreUObject",
				"Engine",
				"InputCore",
				"Networking",
				"Sockets",
				"HTTP",
				"Json",
				"JsonUtilities",
				"DeveloperSettings",
				"ImageCore",
				"ImageWrapper",
				"RenderCore",
				"RHI",
				// Map System Dependencies - UE 5.6 Modern APIs
				"Landscape",
				"LevelEditor",
				"NavigationSystem",
				"AIModule",
				// PCG Framework Dependencies - UE 5.6.1 Experimental
				"PCG",
				"ComputeFramework",
				"Foliage",
				"PhysicsCore",
				// Landscape Experimental Dependencies - UE 5.6.1
				"LandscapePatch",
				// GeometryFlow Experimental Dependencies - UE 5.6.1
				"GeometryFlowCore",
				"GeometryFlowMeshProcessing",
				// DynamicMesh Dependencies - UE 5.6.1
				"GeometryCore",
				"GeometryFramework",
				"DynamicMesh",
				"MeshDescription",
				// Material Dependencies - UE 5.6.1
				"MaterialEditor",
				"UnrealEd",
				"ToolMenus",
				"EditorStyle",
				"EditorWidgets",
				// Analytics Dependencies - UE 5.6.1 Modern APIs
				"Analytics",
				"AnalyticsET",
				"TelemetryUtils",
				// Architecture Dependencies - UE 5.6.1 Modern APIs
				"PCG",
				// Balance Dependencies - UE 5.6.1 Modern APIs
				"MLAdapter",
				// Blueprint Dependencies - UE 5.6.1 Modern APIs
				"BlueprintGraph",
				"KismetCompiler",
				// Collision Dependencies - UE 5.6.1 Modern APIs
				"Chaos",
				"ChaosCore",
				"ChaosSolverEngine"
			}
		);
		
		PrivateDependencyModuleNames.AddRange(
			new string[]
			{
				"UnrealEd",
				"EditorScriptingUtilities",
				"EditorSubsystem",
				"Slate",
				"SlateCore",
				"UMG",
				"Kismet",
				"KismetCompiler",
				"BlueprintGraph",
				"Projects",
				"AssetRegistry",
				// Map System Private Dependencies
				"Renderer",
				"PhysicsCore",
				"Chaos",
				"LevelSequence",
				"MovieScene",
				"AssetTools",
				// PCG Framework Private Dependencies - UE 5.6.1 Experimental
				"PCGCompute",
				"Voronoi",
				// GeometryFlow Private Dependencies - UE 5.6.1 Experimental
				"MeshModelingTools",
				"MeshModelingToolsExp",
				"ModelingOperators",
				"ModelingOperatorsEditorOnly"
			}
		);
		
		if (Target.bBuildEditor == true)
		{
			PrivateDependencyModuleNames.AddRange(
				new string[]
				{
					"PropertyEditor",      // For widget property editing
					"ToolMenus",           // For editor UI
					"BlueprintEditorLibrary", // For Blueprint utilities
					"UMGEditor",          // For WidgetBlueprint.h and other UMG editor functionality
					// Map System Editor Dependencies - UE 5.6 Experimental
					"LandscapeEditor",
					"WorldPartitionEditor"
				}
			);
		}
		
		DynamicallyLoadedModuleNames.AddRange(
			new string[]
			{
				// ... add any modules that your module loads dynamically here ...
			}
		);
	}
} 