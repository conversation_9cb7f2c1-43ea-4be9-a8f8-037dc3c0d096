Stats thread started at 0.326066
Metadata set : systemresolution.resx="1280"
Metadata set : systemresolution.resy="720"
ICU TimeZone Detection - Raw Offset: -3:00, Platform Override: ''
Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-8389789443C933D62A169082CAEF8DA3
         Session CrashGUID >====================================================
No local boot hotfix file found at: [../../../../../../Game/AURACRON/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
Pre-Initializing Audio Device Manager...
AudioInfo: 'OPUS' Registered
Lib vorbis DLL was dynamically loaded.
AudioInfo: 'OGG' Registered
AudioInfo: 'ADPCM' Registered
AudioInfo: 'PCM' Registered
AudioInfo: 'BINKA' Registered
AudioInfo: 'RADA' Registered
Audio Device Manager Pre-Initialized
Looking for build plugins target receipt
Found matching target receipt: ../../../../../../Game/AURACRON/Binaries/Win64/AURACRONEditor.target
Looking for enabled plugins target receipt
Loading VulkanPC ini files took 0.07 seconds
Found matching target receipt: ../../../../../../Game/AURACRON/Binaries/Win64/AURACRONEditor.target
Loading Android ini files took 0.07 seconds
Loading Mac ini files took 0.08 seconds
Loading IOS ini files took 0.08 seconds
Loading TVOS ini files took 0.08 seconds
Loading Unix ini files took 0.09 seconds
Loading Windows ini files took 0.09 seconds
Loading Linux ini files took 0.10 seconds
Mounting Engine plugin ChaosCloth
Mounting Engine plugin ChaosInsights
Mounting Engine plugin ChaosVD
Mounting Engine plugin CmdLinkServer
Mounting Engine plugin EnhancedInput
Mounting Engine plugin FastBuildController
Mounting Engine plugin IoStoreInsights
Mounting Engine plugin MassInsights
Mounting Engine plugin MeshPainting
Mounting Engine plugin PCG
Loading VisionOS ini files took 0.04 seconds
Asset registry cache read as 66.4 MiB from ../../../../../../Game/AURACRON/Intermediate/CachedAssetRegistry_0.bin.
Mounting Engine plugin RenderGraphInsights
Mounting Engine plugin UbaController
Mounting Engine plugin TraceUtilities
Mounting Engine plugin WorldMetrics
Mounting Engine plugin XGEController
Mounting Engine plugin MetaHumanSDK
Mounting Engine plugin InterchangeTests
Mounting Engine plugin CameraCalibrationCore
Mounting Engine plugin Takes
Mounting Engine plugin Paper2D
Mounting Engine plugin EnvironmentQueryEditor
Mounting Engine plugin AISupport
Mounting Engine plugin ACLPlugin
Mounting Engine plugin AnimationData
Mounting Engine plugin AnimationModifierLibrary
Mounting Engine plugin BlendSpaceMotionAnalysis
Mounting Engine plugin ControlRigModules
Mounting Engine plugin ControlRigSpline
Mounting Engine plugin ControlRig
Mounting Engine plugin GameplayInsights
Mounting Engine plugin DeformerGraph
Mounting Engine plugin IKRig
Mounting Engine plugin RigLogic
Mounting Engine plugin TweeningUtils
Mounting Engine plugin SkeletalMeshModelingTools
Mounting Engine plugin GameplayCameras
Mounting Engine plugin EngineCameras
Mounting Engine plugin CameraShakePreviewer
Mounting Engine plugin AnimationSharing
Mounting Engine plugin CLionSourceCodeAccess
Mounting Engine plugin OodleNetwork
Mounting Engine plugin CodeLiteSourceCodeAccess
Mounting Engine plugin DumpGPUServices
Mounting Engine plugin KDevelopSourceCodeAccess
Mounting Engine plugin N10XSourceCodeAccess
Mounting Engine plugin GitSourceControl
Mounting Engine plugin NamingTokens
Mounting Engine plugin NullSourceCodeAccess
Mounting Engine plugin PerforceSourceControl
Mounting Engine plugin PropertyAccessNode
Mounting Engine plugin PixWinPlugin
Mounting Engine plugin RenderDocPlugin
Mounting Engine plugin ProjectLauncher
Mounting Engine plugin PlasticSourceControl
Mounting Engine plugin RiderSourceCodeAccess
Mounting Engine plugin SubversionSourceControl
Mounting Engine plugin UObjectPlugin
Mounting Engine plugin TextureFormatOodle
Mounting Engine plugin VisualStudioCodeSourceCodeAccess
Mounting Engine plugin PluginUtils
Mounting Engine plugin AssetManagerEditor
Mounting Engine plugin VisualStudioSourceCodeAccess
Mounting Engine plugin XCodeSourceCodeAccess
Mounting Engine plugin ChangelistReview
Mounting Engine plugin BlueprintHeaderView
Mounting Engine plugin ColorGrading
Mounting Engine plugin CryptoKeys
Mounting Engine plugin DataValidation
Mounting Engine plugin CurveEditorTools
Mounting Engine plugin EditorDebugTools
Mounting Engine plugin EditorScriptingUtilities
Mounting Engine plugin FacialAnimation
Mounting Engine plugin MacGraphicsSwitching
Mounting Engine plugin EngineAssetDefinitions
Mounting Engine plugin GeometryMode
Mounting Engine plugin GameplayTagsEditor
Mounting Engine plugin MaterialAnalyzer
Mounting Engine plugin MobileLauncherProfileWizard
Mounting Engine plugin MeshLODToolset
Mounting Engine plugin ModelingToolsEditorMode
Mounting Engine plugin PluginBrowser
Mounting Engine plugin SpeedTreeImporter
Mounting Engine plugin ProxyLODPlugin
Mounting Engine plugin SequencerAnimTools
Mounting Engine plugin StylusInput
Mounting Engine plugin UMGWidgetPreview
Mounting Engine plugin UVEditor
Mounting Engine plugin WorldPartitionHLODUtilities
Mounting Engine plugin DatasmithContent
Mounting Engine plugin GLTFExporter
Mounting Engine plugin VariantManagerContent
Mounting Engine plugin VariantManager
Mounting Engine plugin AdvancedRenamer
Mounting Engine plugin AutomationUtils
Mounting Engine plugin BackChannel
Mounting Engine plugin ChaosCaching
Mounting Engine plugin ChaosEditor
Mounting Engine plugin ChaosSolverPlugin
Mounting Engine plugin ChaosUserDataPT
Mounting Engine plugin CharacterAI
Mounting Engine plugin ChaosNiagara
Mounting Engine plugin Dataflow
Mounting Engine plugin EditorPerformance
Mounting Engine plugin EditorDataStorage
Mounting Engine plugin EditorTelemetry
Mounting Engine plugin EditorDataStorageFeatures
Mounting Engine plugin Fracture
Mounting Engine plugin FullBodyIK
Mounting Engine plugin GeometryCollectionPlugin
Mounting Engine plugin GeometryDataflow
Mounting Engine plugin GeometryFlow
Mounting Engine plugin LevelSequenceNavigatorBridge
Mounting Engine plugin MeshModelingToolsetExp
Mounting Engine plugin LowLevelNetTrace
Mounting Engine plugin LocalizableMessage
Mounting Engine plugin NFORDenoise
Mounting Engine plugin PlanarCut
Mounting Engine plugin PlatformCrypto
Mounting Engine plugin PythonScriptPlugin
Mounting Engine plugin RuntimeTelemetry
Mounting Engine plugin SequenceNavigator
Mounting Engine plugin SkeletalReduction
Mounting Engine plugin ToolPresets
Mounting Engine plugin Cascade
Mounting Engine plugin NiagaraSimCaching
Mounting Engine plugin Niagara
Mounting Engine plugin InterchangeAssets
Mounting Engine plugin AlembicImporter
Mounting Engine plugin AndroidMedia
Mounting Engine plugin InterchangeEditor
Mounting Engine plugin Interchange
Mounting Engine plugin ImgMedia
Mounting Engine plugin MediaCompositing
Mounting Engine plugin MediaPlate
Mounting Engine plugin MediaPlayerEditor
Mounting Engine plugin UdpMessaging
Mounting Engine plugin WebMMedia
Mounting Engine plugin TcpMessaging
Mounting Engine plugin AvfMedia
Mounting Engine plugin ActorSequence
Mounting Engine plugin WmfMedia
Mounting Engine plugin LevelSequenceEditor
Mounting Engine plugin TemplateSequence
Mounting Engine plugin NNERuntimeORT
Mounting Engine plugin SequencerScripting
Mounting Engine plugin NNEDenoiser
Mounting Engine plugin EOSShared
Mounting Engine plugin OnlineBase
Mounting Engine plugin OnlineServices
Mounting Engine plugin OnlineSubsystem
Mounting Engine plugin OnlineSubsystemNull
Mounting Engine plugin OnlineSubsystemUtils
Mounting Engine plugin LauncherChunkInstaller
Mounting Engine plugin ActorLayerUtilities
Mounting Engine plugin AndroidFileServer
Mounting Engine plugin AndroidDeviceProfileSelector
Mounting Engine plugin AppleImageUtils
Mounting Engine plugin AndroidMoviePlayer
Mounting Engine plugin AppleMoviePlayer
Mounting Engine plugin AndroidPermission
Mounting Engine plugin AudioCapture
Mounting Engine plugin ArchVisCharacter
Mounting Engine plugin AssetTags
Mounting Engine plugin AudioWidgets
Mounting Engine plugin AudioSynesthesia
Mounting Engine plugin CableComponent
Mounting Engine plugin ChunkDownloader
Mounting Engine plugin CustomMeshComponent
Mounting Engine plugin ExampleDeviceProfileSelector
Mounting Engine plugin ComputeFramework
Mounting Engine plugin GeometryProcessing
Mounting Engine plugin GeometryCache
Mounting Engine plugin HairStrands
Mounting Engine plugin GoogleCloudMessaging
Mounting Engine plugin GooglePAD
Mounting Engine plugin InputDebugging
Mounting Engine plugin IOSDeviceProfileSelector
Mounting Engine plugin LinuxDeviceProfileSelector
Mounting Engine plugin LocationServicesBPLibrary
Mounting Engine plugin MeshModelingToolset
Mounting Engine plugin Metasound
Mounting Engine plugin MobilePatchingUtils
Mounting Engine plugin MsQuic
Mounting Engine plugin PropertyAccessEditor
Mounting Engine plugin PropertyBindingUtils
Mounting Engine plugin ProceduralMeshComponent
Mounting Engine plugin ResonanceAudio
Mounting Engine plugin RigVM
Mounting Engine plugin SignificanceManager
Mounting Engine plugin SoundFields
Mounting Engine plugin StateTree
Mounting Engine plugin Synthesis
Mounting Engine plugin WindowsMoviePlayer
Mounting Engine plugin WaveTable
Mounting Engine plugin WindowsDeviceProfileSelector
Mounting Engine plugin WebMMoviePlayer
Mounting Engine plugin LightMixer
Mounting Engine plugin ObjectMixer
Mounting Engine plugin PortableObjectFileDataSource
Mounting Engine plugin CompositeCore
Mounting Engine plugin ContentBrowserAssetDataSource
Mounting Engine plugin ContentBrowserClassDataSource
Mounting Engine plugin ContentBrowserFileDataSource
Mounting Engine plugin BaseCharacterFXEditor
Mounting Engine plugin OnlineSubsystemGooglePlay
Mounting Engine plugin OnlineSubsystemIOS
Mounting Engine plugin XInputDevice
Mounting Project plugin UnrealMCP
Revision control is disabled
Revision control is disabled
Revision control is disabled
Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
File 'WinPixGpuCapturer.dll' does not exist
PIX capture plugin failed to initialize! Check that the process is launched from PIX.
Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
Started StudioTelemetry Session
NFORDenoise function starting up
Loaded "C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/EOSSDK-Win64-Shipping.dll"
FEOSSDKManager::Initialize Initializing EOSSDK Version:1.17.0-41373641
Using libcurl 8.12.1
 - built for Windows
 - supports SSL with OpenSSL/1.1.1t
 - supports HTTP deflate (compression) using libz 1.3
 - other features:
     CURL_VERSION_SSL
     CURL_VERSION_LIBZ
     CURL_VERSION_IPV6
     CURL_VERSION_ASYNCHDNS
     CURL_VERSION_LARGEFILE
     CURL_VERSION_TLSAUTH_SRP
     CURL_VERSION_HTTP2
 CurlRequestOptions (configurable via config and command line):
 - bVerifyPeer = true  - Libcurl will verify peer certificate
 - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
 - bDontReuseConnections = false  - Libcurl will reuse connections
 - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
 - LocalHostAddr = Default
 - BufferSize = 65536
CreateHttpThread using FCurlMultiPollEventLoopHttpThread
Creating http thread with maximum 256 concurrent requests
WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
OSS: Created online subsystem instance for: NULL
OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
Applying CVar settings from Section [/Script/CompositeCore.CompositeCorePluginSettings] File [Engine]
ExecutableName: UnrealEditor.exe
Build: ++UE5+Release-5.6-CL-44394996
Platform=WindowsEditor
MachineId=8bb1964343e8298f803f869f44351803
DeviceId=
Engine Version: 5.6.1-44394996+++UE5+Release-5.6
Compatible Engine Version: 5.6.0-43139311+++UE5+Release-5.6
Net CL: 43139311
OS: Windows 11 (24H2) [10.0.26100.4946] (), CPU: 13th Gen Intel(R) Core(TM) i5-1345U, GPU: Intel(R) Iris(R) Xe Graphics
Compiled (64-bit): Jul 28 2025 20:53:34
Architecture: x64
Compiled with Visual C++: 19.38.33130.00
Build Configuration: Development
Branch Name: ++UE5+Release-5.6
Command Line: -AUTH_LOGIN=unused -AUTH_PASSWORD=796af167ed144a639240610d25f35db7 -AUTH_TYPE=exchangecode -epicapp=UE_5.6 -epicenv=Prod -EpicPortal -epicusername=Jukinhaum -epicuserid=1de6ee944444461fafe09fadb52795be -epiclocale=pt-BR -epicsandboxid=ue
Base Directory: C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/
Allocator: Mimalloc
Installed Engine Build: 1
This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
Number of dev versions registered: 37
  Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
  Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
  Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
  Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
  Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
  Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
  Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
  Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
  Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
  Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
  Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
  Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
  Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
  Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
  Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
  Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
  Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
  Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
  Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
  FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 207
  FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
  FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
  FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 17
  Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 11
  Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
  Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
  Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
  Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
  Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
  Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
  UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 121
  UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 56
  UE5-SpecialProject (59DA5D52-1232-4948-B878-597870B8E98B): 9
  Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
  Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
  LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
  Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
Presizing for max 25165824 objects, including 0 objects not considered by GC.
Object subsystem initialized
Set CVar [[con.DebugEarlyDefault:1]]
CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
Set CVar [[r.setres:1280x720]]
CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
CVar [[QualityLevelMapping:high]] deferred - dummy variable created
CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
Set CVar [[r.PSOPrecache.GlobalShaders:1]]
Set CVar [[r.VRS.EnableSoftware:1]]
Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
Set CVar [[r.VSync:0]]
Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
Set CVar [[r.GPUCrashDebugging:0]]
CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
Set CVar [[r.AllowStaticLighting:0]]
Set CVar [[r.GenerateMeshDistanceFields:1]]
Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
Set CVar [[r.ReflectionMethod:1]]
Set CVar [[r.SkinCache.CompileShaders:1]]
Set CVar [[r.RayTracing:1]]
Set CVar [[r.RayTracing.RayTracingProxies.ProjectEnabled:1]]
Set CVar [[r.Shadow.Virtual.Enable:1]]
Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
Set CVar [[s.AsyncLoadingThreadEnabled:1]]
Set CVar [[s.EventDrivenLoaderEnabled:1]]
Set CVar [[s.WarnIfTimeLimitExceeded:0]]
Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
Set CVar [[s.TimeLimitExceededMinTime:0.005]]
Set CVar [[s.UseBackgroundLevelStreaming:1]]
Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
Set CVar [[s.FlushStreamingOnExit:1]]
CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
Set CVar [[gc.FlushStreamingOnGC:0]]
Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
Set CVar [[gc.AllowParallelGC:1]]
Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
Set CVar [[gc.MaxObjectsInEditor:25165824]]
Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
Set CVar [[gc.CreateGCClusters:1]]
Set CVar [[gc.MinGCClusterSize:5]]
Set CVar [[gc.AssetClustreringEnabled:0]]
Set CVar [[gc.ActorClusteringEnabled:0]]
Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
Set CVar [[gc.GarbageEliminationEnabled:1]]
Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
Metadata set : systemresolution.resx="1536"
Metadata set : systemresolution.resy="864"
Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
Set CVar [[r.SkeletalMeshLODBias:0]]
Set CVar [[r.ViewDistanceScale:1.0]]
Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
Set CVar [[r.FXAA.Quality:4]]
Set CVar [[r.TemporalAA.Quality:2]]
Set CVar [[r.TSR.History.R11G11B10:1]]
Set CVar [[r.TSR.History.ScreenPercentage:200]]
Set CVar [[r.TSR.History.UpdateQuality:3]]
Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
Set CVar [[r.TSR.ReprojectionField:1]]
Set CVar [[r.TSR.Resurrection:1]]
Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
Set CVar [[r.LightFunctionQuality:1]]
Set CVar [[r.ShadowQuality:5]]
Set CVar [[r.Shadow.CSM.MaxCascades:10]]
Set CVar [[r.Shadow.MaxResolution:2048]]
Set CVar [[r.Shadow.MaxCSMResolution:2048]]
Set CVar [[r.Shadow.RadiusThreshold:0.01]]
Set CVar [[r.Shadow.DistanceScale:1.0]]
Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
Set CVar [[r.DistanceFieldShadowing:1]]
Set CVar [[r.VolumetricFog:1]]
Set CVar [[r.VolumetricFog.GridPixelSize:8]]
Set CVar [[r.VolumetricFog.GridSizeZ:128]]
Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
Set CVar [[r.LightMaxDrawDistanceScale:1]]
Set CVar [[r.CapsuleShadows:1]]
Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
Set CVar [[r.DistanceFieldAO:1]]
Set CVar [[r.SkylightIntensityMultiplier:1.0]]
Set CVar [[r.AOQuality:2]]
Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
Set CVar [[r.LumenScene.DirectLighting.MaxLightsPerTile:8]]
Set CVar [[r.LumenScene.DirectLighting.UpdateFactor:32]]
Set CVar [[r.LumenScene.Radiosity.UpdateFactor:64]]
Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:100]]
Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
Set CVar [[r.Lumen.ScreenProbeGather.NumAdaptiveProbes:8]]
Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.BentNormal:1]]
Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:70]]
Set CVar [[r.RayTracing.Scene.BuildMode:1]]
Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
Set CVar [[r.SSR.Quality:3]]
Set CVar [[r.SSR.HalfResSceneColor:0]]
Set CVar [[r.Lumen.Reflections.Allow:1]]
Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.NumSamples:5]]
Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
Set CVar [[r.MotionBlurQuality:4]]
Set CVar [[r.MotionBlur.HalfResGather:0]]
Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
Set CVar [[r.AmbientOcclusionMaxQuality:100]]
Set CVar [[r.AmbientOcclusionLevels:-1]]
Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
Set CVar [[r.DepthOfFieldQuality:2]]
Set CVar [[r.RenderTargetPoolMin:400]]
Set CVar [[r.LensFlareQuality:2]]
Set CVar [[r.SceneColorFringeQuality:1]]
Set CVar [[r.EyeAdaptationQuality:2]]
Set CVar [[r.BloomQuality:5]]
Set CVar [[r.Bloom.ScreenPercentage:50.000]]
Set CVar [[r.FastBlurThreshold:100]]
Set CVar [[r.Upscale.Quality:3]]
Set CVar [[r.LightShaftQuality:1]]
Set CVar [[r.Filter.SizeScale:1]]
Set CVar [[r.Tonemapper.Quality:5]]
Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
Applying CVar settings from Section [TextureQuality@3] File [Scalability]
Set CVar [[r.Streaming.MipBias:0]]
Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
Set CVar [[r.Streaming.Boost:1]]
Set CVar [[r.MaxAnisotropy:8]]
Set CVar [[r.VT.MaxAnisotropy:8]]
Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
Set CVar [[r.Streaming.PoolSize:1000]]
Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
Set CVar [[r.TranslucencyLightingVolumeDim:64]]
Set CVar [[r.RefractionQuality:2]]
Set CVar [[r.SceneColorFormat:4]]
Set CVar [[r.DetailMode:3]]
Set CVar [[r.TranslucencyVolumeBlur:1]]
Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
Set CVar [[r.SSS.Scale:1]]
Set CVar [[r.SSS.SampleSet:2]]
Set CVar [[r.SSS.Quality:1]]
Set CVar [[r.SSS.HalfRes:0]]
Set CVar [[r.SSGI.Quality:3]]
Set CVar [[r.EmitterSpawnRateScale:1.0]]
Set CVar [[r.ParticleLightQuality:2]]
Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
Set CVar [[fx.Niagara.QualityLevel:3]]
Set CVar [[r.Refraction.OffsetQuality:1]]
Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:2]]
Set CVar [[r.HeterogeneousVolumes.MaxStepCount:256]]
Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:256]]
Set CVar [[r.HeterogeneousVolumes.Shadows.MaxSampleCount:8]]
Set CVar [[r.HeterogeneousVolumes.UseExistenceMask:0]]
Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
Set CVar [[foliage.DensityScale:1.0]]
Set CVar [[grass.DensityScale:1.0]]
Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
Set CVar [[r.AnisotropicMaterials:1]]
Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
Using Default RHI: D3D12
Using Highest Feature Level of D3D12: SM6
Loading RHI module D3D12RHI
Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
Intel Extensions Framework not supported by driver. Please check if a driver update is available.
Found D3D12 adapter 0: Intel(R) Iris(R) Xe Graphics (VendorId: 8086, DeviceId: a7a1, SubSysId: c001028, Revision: 0004
  Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 unsupported
  Adapter has 128MB of dedicated video memory, 0MB of dedicated system memory, and 16226MB of shared system memory, 1 output[s], UMA:true
  Driver Version: 32.0.101.6556 (internal:32.0.101.6556, unified:101.6556)
     Driver Date: 1-23-2025
Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
  Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
  Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 16226MB of shared system memory, 0 output[s], UMA:true
DirectX Agility SDK runtime found.
Chosen D3D12 Adapter Id = 0
Adapter only supports up to Feature Level 'SM5', requested Feature Level was 'SM6'
RHI D3D12 with Feature Level SM6 is not supported on your system, attempting to fall back to RHI D3D11 with Feature Level SM5
Loading RHI module D3D11RHI
Checking if RHI D3D11 with Feature Level SM5 is supported by your system.
D3D11 min allowed feature level: 11_0
D3D11 max allowed feature level: 11_1
D3D11 adapters:
Testing D3D11 Adapter 0:
    Description : Intel(R) Iris(R) Xe Graphics
    VendorId    : 8086
    DeviceId    : a7a1
    SubSysId    : c001028
    Revision    : 0004
    DedicatedVideoMemory : 134217728 bytes
    DedicatedSystemMemory : 0 bytes
    SharedSystemMemory : 17014562816 bytes
    AdapterLuid : 0 85693
   0. 'Intel(R) Iris(R) Xe Graphics' (Feature Level 11_1)
      128/0/16226 MB DedicatedVideo/DedicatedSystem/SharedSystem, Outputs:1, VendorId:0x8086 UMA:true
Testing D3D11 Adapter 1:
    Description : Microsoft Basic Render Driver
    VendorId    : 1414
    DeviceId    : 008c
    SubSysId    : 0000
    Revision    : 0000
    DedicatedVideoMemory : 0 bytes
    DedicatedSystemMemory : 0 bytes
    SharedSystemMemory : 17014562816 bytes
    AdapterLuid : 0 86560
   1. 'Microsoft Basic Render Driver' (Feature Level 11_1)
      0/0/16226 MB DedicatedVideo/DedicatedSystem/SharedSystem, Outputs:0, VendorId:0x1414 UMA:true
Chosen D3D11 Adapter:
    Description : Intel(R) Iris(R) Xe Graphics
    VendorId    : 8086
    DeviceId    : a7a1
    SubSysId    : c001028
    Revision    : 0004
    DedicatedVideoMemory : 134217728 bytes
    DedicatedSystemMemory : 0 bytes
    SharedSystemMemory : 17014562816 bytes
    AdapterLuid : 0 85693
Integrated GPU (iGPU): true
RHI D3D11 with Feature Level SM5 is supported and will be used.
Selected Device Profile: [WindowsEditor]
Platform has ~ 32 GB [34029125632 / 34359738368 / 32], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
Going up to parent DeviceProfile [Windows]
Going up to parent DeviceProfile []
Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
Set CVar [[r.DumpShaderDebugInfo:2]]
Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
Applying CVar settings from Section [ConsoleVariables] File [Engine]
Applying CVar settings from Section [ConsoleVariables] File [C:/Game/AURACRON/Saved/Config/WindowsEditor/Editor.ini]
Computer: TKT
User: tktca
CPU Page size=4096, Cores=10
High frequency timer resolution =10.000000 MHz
Process is running as part of a Windows Job with separate resource limits
Memory total: Physical=31.7GB (32GB approx) Virtual=37.8GB
Platform Memory Stats for WindowsEditor
Process Physical Memory: 638.79 MB used, 701.80 MB peak
Process Virtual Memory: 641.98 MB used, 684.71 MB peak
Physical Memory: 19078.85 MB used,  13373.85 MB free, 32452.70 MB total
Virtual Memory: 22770.88 MB used,  15976.17 MB free, 38747.05 MB total
Metadata set : extradevelopmentmemorymb="0"
WindowsPlatformFeatures enabled
Chaos Debug Draw Startup
Physics initialised using underlying interface: Chaos
Using OS detected language (pt-BR).
Using OS detected locale (pt-BR).
Setting process to per monitor DPI aware
LocRes '../../../Engine/Content/Localization/Editor/pt/Editor.locres' could not be opened for reading!
LocRes '../../../Engine/Content/Localization/EditorTutorials/pt/EditorTutorials.locres' could not be opened for reading!
LocRes '../../../Engine/Content/Localization/Keywords/pt/Keywords.locres' could not be opened for reading!
LocRes '../../../Engine/Content/Localization/Category/pt/Category.locres' could not be opened for reading!
LocRes '../../../Engine/Content/Localization/ToolTips/pt/ToolTips.locres' could not be opened for reading!
LocRes '../../../Engine/Content/Localization/PropertyNames/pt/PropertyNames.locres' could not be opened for reading!
LocRes '../../../Engine/Content/Localization/Engine/pt/Engine.locres' could not be opened for reading!
LocRes '../../../Engine/Plugins/MetaHuman/MetaHumanSDK/Content/Localization/MetaHumanSDK/pt/MetaHumanSDK.locres' could not be opened for reading!
LocRes '../../../Engine/Plugins/Online/OnlineSubsystem/Content/Localization/OnlineSubsystem/pt/OnlineSubsystem.locres' could not be opened for reading!
LocRes '../../../Engine/Plugins/Online/OnlineSubsystemUtils/Content/Localization/OnlineSubsystemUtils/pt/OnlineSubsystemUtils.locres' could not be opened for reading!
LocRes '../../../Engine/Plugins/Online/Android/OnlineSubsystemGooglePlay/Content/Localization/OnlineSubsystemGooglePlay/pt/OnlineSubsystemGooglePlay.locres' could not be opened for reading!
LocRes '../../../Engine/Plugins/Online/IOS/OnlineSubsystemIOS/Content/Localization/OnlineSubsystemIOS/pt/OnlineSubsystemIOS.locres' could not be opened for reading!
Available input methods:
  - Português (Brasil) - (Keyboard).
  - Português (Portugal) - (Keyboard).
Activated input method: Português (Brasil) - (Keyboard).
CacheForceMaxTouchpadSensitivityMode SetMaxTouchpadSensitivity
Attempting to load config data for Default__SlateThemeManager before the Class has been constructed/registered/linked (likely during module loading or early startup). This will result in the load silently failing and should be fixed.
New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
Slate User Registered.  User Index 0, Is Virtual User: 0
Using Default RHI: D3D12
Using Highest Feature Level of D3D12: SM6
Loading RHI module D3D12RHI
Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
Adapter only supports up to Feature Level 'SM5', requested Feature Level was 'SM6'
RHI D3D12 with Feature Level SM6 is not supported on your system, attempting to fall back to RHI D3D11 with Feature Level SM5
Loading RHI module D3D11RHI
Checking if RHI D3D11 with Feature Level SM5 is supported by your system.
RHI D3D11 with Feature Level SM5 is supported and will be used.
Attached monitors:
    resolution: 1920x1080, work area: (0, 0) -> (1920, 1020), device: '\\.\DISPLAY1' [PRIMARY]
Found 1 attached monitors.
Gathering driver information using Windows Setup API
RHI Adapter Info:
            Name: Intel(R) Iris(R) Xe Graphics
  Driver Version: 32.0.101.6556 (internal:32.0.101.6556, unified:101.6556)
     Driver Date: 1-23-2025
Creating new Direct3DDevice
    GPU DeviceId: 0xa7a1 (for the marketing name, search the web for "GPU Device Id")
Texture pool is 1523 MB (70% of 2176 MB)
Nvidia Aftermath is disabled in D3D11 due to instability issues.
Creating D3DDevice using adapter:
    Description : Intel(R) Iris(R) Xe Graphics
    VendorId    : 8086
    DeviceId    : a7a1
    SubSysId    : c001028
    Revision    : 0004
    DedicatedVideoMemory : 134217728 bytes
    DedicatedSystemMemory : 0 bytes
    SharedSystemMemory : 17014562816 bytes
    AdapterLuid : 0 85693
Aftermath is not loaded.
Intel Extensions loaded requested version for UAVOverlap: 1.1.0
Intel Extensions loaded requested version Atomics Version: 3.4.1
Intel Extensions Framework enabled
RHI has support for 64 bit atomics
Async texture creation enabled
D3D11_MAP_WRITE_NO_OVERWRITE for dynamic buffer SRVs is supported
Array index from any shader is supported
Current RHI does not support Variable Rate Shading
Metadata set : verbatimrhiname="D3D11"
Metadata set : rhiname="D3D11"
Metadata set : rhifeaturelevel="SM5"
Metadata set : shaderplatform="PCD3D_SM5"
Initializing FReadOnlyCVARCache
Running Turnkey SDK detection: ' -ScriptsForProject="C:/Game/AURACRON/AURACRON.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/AURACRON/Intermediate/TurnkeyReport_0.log" -log="C:/Game/AURACRON/Intermediate/TurnkeyLog_0.log" -project="C:/Game/AURACRON/AURACRON.uproject"  -platform=all'
Running Serialized UAT: [ cmd.exe /c ""C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="C:/Game/AURACRON/AURACRON.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/AURACRON/Intermediate/TurnkeyReport_0.log" -log="C:/Game/AURACRON/Intermediate/TurnkeyLog_0.log" -project="C:/Game/AURACRON/AURACRON.uproject"  -platform=all" ]
ASTCEnc version 5.0.1 library loaded
Loaded Base TextureFormat: TextureFormatASTC
Loaded Base TextureFormat: TextureFormatDXT
Loaded Base TextureFormat: TextureFormatETC2
Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
Loaded Base TextureFormat: TextureFormatUncompressed
Oodle Texture TFO init; latest sdk version = 2.9.13
Oodle Texture loading DLL: oo2tex_win64_2.9.13.dll
Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
Loaded Base TextureFormat: TextureFormatOodle
Loaded TargetPlatform 'Android'
Loaded TargetPlatform 'Android_ASTC'
Loaded TargetPlatform 'Android_DXT'
Loaded TargetPlatform 'Android_ETC2'
Loaded TargetPlatform 'Android_OpenXR'
Loaded TargetPlatform 'AndroidClient'
Loaded TargetPlatform 'Android_ASTCClient'
Loaded TargetPlatform 'Android_DXTClient'
Loaded TargetPlatform 'Android_ETC2Client'
Loaded TargetPlatform 'Android_OpenXRClient'
Loaded TargetPlatform 'Android_Multi'
Loaded TargetPlatform 'Android_MultiClient'
Loaded TargetPlatform 'IOS'
Loaded TargetPlatform 'IOSClient'
Loaded TargetPlatform 'Linux'
Loaded TargetPlatform 'LinuxEditor'
Loaded TargetPlatform 'LinuxServer'
Loaded TargetPlatform 'LinuxClient'
Loaded TargetPlatform 'Mac'
Loaded TargetPlatform 'MacEditor'
Loaded TargetPlatform 'MacServer'
Loaded TargetPlatform 'MacClient'
Loaded TargetPlatform 'TVOS'
Loaded TargetPlatform 'TVOSClient'
Loaded TargetPlatform 'Windows'
Loaded TargetPlatform 'WindowsEditor'
Loaded TargetPlatform 'WindowsServer'
Loaded TargetPlatform 'WindowsClient'
Building Assets For WindowsEditor
Unable to find shader format SF_METAL_ES3_1_IOS from hinted modules, loading all potential format modules to find it
Loaded format module MetalShaderFormat
  SF_METAL_ES3_1_IOS
  SF_METAL_SM5_IOS
  SF_METAL_ES3_1_TVOS
  SF_METAL_SM5_TVOS
  SF_METAL_SM5
  SF_METAL_SM6
  SF_METAL_SIM
  SF_METAL_ES3_1
Loaded format module ShaderFormatD3D
  PCD3D_SM6
  PCD3D_SM5
  PCD3D_ES31
Loaded format module ShaderFormatOpenGL
  GLSL_150_ES31
  GLSL_ES3_1_ANDROID
Loaded format module ShaderFormatVectorVM
  VVM_1_0
Loaded format module VulkanShaderFormat
  SF_VULKAN_SM5
  SF_VULKAN_ES31_ANDROID
  SF_VULKAN_ES31
  SF_VULKAN_SM5_ANDROID
  SF_VULKAN_SM6
Ray tracing is disabled. Reason: not supported by current RHI.
Memory: Max Cache Size: -1 MB
FDerivedDataBackendGraph: Pak pak cache file ../../../../../../Game/AURACRON/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
Unable to find inner node Pak for hierarchy Hierarchy.
FDerivedDataBackendGraph: CompressedPak pak cache file ../../../../../../Game/AURACRON/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
Unable to find inner node CompressedPak for hierarchy Hierarchy.
../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1350 MiB)
FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
Unable to find inner node EnterprisePak for hierarchy Hierarchy.
Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
InTree version at 'C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
Found existing instance running on port 8558 matching our settings, no actions needed
Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
Local ZenServer AutoLaunch initialization completed in 0.205 seconds
ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.02ms. RandomReadSpeed=1350.50MBs, RandomWriteSpeed=104.37MBs. Assigned SpeedClass 'Local'
Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
ZenShared: Disabled because Host is set to 'None'
Unable to find inner node ZenShared for hierarchy Hierarchy.
Shared: Disabled because no path is configured.
Unable to find inner node Shared for hierarchy Hierarchy.
Cloud: Disabled because Host is set to 'None'
Unable to find inner node Cloud for hierarchy Hierarchy.
Guid format shader working directory is 15 characters bigger than the processId version (../../../../../../Game/AURACRON/Intermediate/Shaders/WorkingDirectory/5332/).
Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/29B3C3FC41F21C8388CC6B901E9E701F/'.
Cannot use XGE Controller as Incredibuild is not installed on this machine.
UBA/Horde Configuration [Uba.Provider.Horde]: Not Enabled
Using 9 local workers for shader compilation
Compiling shader autogen file: ../../../../../../Game/AURACRON/Intermediate/ShaderAutogen/PCD3D_SM5/AutogenShaderHeaders.ush
Autogen file is unchanged, skipping write.
Completed SDK detection: ExitCode = 0
Using FreeType 2.10.0
SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
FAssetRegistry took 0.0029 seconds to start up
EditorDomain is Disabled
AssetDataGatherer spent 0.000s loading caches ../../../../../../Game/AURACRON/Intermediate/CachedAssetRegistry_*.bin.
Deviceprofile LinuxArm64Editor not found.
Deviceprofile LinuxArm64 not found.
Active device profile: [000001F1C68F1E00][000001F1C1F10000 66] WindowsEditor
Metadata set : deviceprofile="WindowsEditor"
FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
Texture Encode Speed: FinalIfAvailable (editor).
Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
Shared linear texture encoding: Disabled
Turnkey Platform: Android: (Status=Invalid, MinAllowed_Sdk=r25b, MaxAllowed_Sdk=r29, Current_Sdk=, Allowed_AutoSdk=r27c, Current_AutoSdk=, Flags="Platform_InvalidHostPrerequisites, Support_FullSdk", Error="Android Studio is not installed correctly.")
Turnkey Platform: IOS: (Status=Invalid, MinAllowed_Sdk=1100.0.0.0, MaxAllowed_Sdk=8999.0, Current_Sdk=, Allowed_AutoSdk=15.2, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.26100.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists")
Running Turnkey device detection: ' -ScriptsForProject="C:/Game/AURACRON/AURACRON.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/AURACRON/Intermediate/TurnkeyReport_1.log" -log="C:/Game/AURACRON/Intermediate/TurnkeyLog_1.log" -project="C:/Game/AURACRON/AURACRON.uproject"  -Device=Win64@TKT'
Running Serialized UAT: [ cmd.exe /c ""C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="C:/Game/AURACRON/AURACRON.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/AURACRON/Intermediate/TurnkeyReport_1.log" -log="C:/Game/AURACRON/Intermediate/TurnkeyLog_1.log" -project="C:/Game/AURACRON/AURACRON.uproject"  -Device=Win64@TKT" -nocompile -nocompileuat ]
Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness because of a recursive sync load
Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec because of a recursive sync load
Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_M because of a recursive sync load
Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_N because of a recursive sync load
Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Opacity/CameraDepthFade because of a recursive sync load
Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDiffuse because of a recursive sync load
Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components because of a recursive sync load
Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultPostProcessMaterial because of a recursive sync load
Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultLightFunctionMaterial because of a recursive sync load
Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/EngineMaterials/WorldGridMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDeferredDecalMaterial because of a recursive sync load
Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/WorldGridMaterial because of a recursive sync load
Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Using QuadricMeshReduction for automatic static mesh reduction
Using SkeletalMeshReduction for automatic skeletal mesh reduction
Using ProxyLODMeshReduction for automatic mesh merging
No distributed automatic mesh merging module available
No distributed automatic mesh merging module available
Applying CVar settings from Section [/Script/CQTest.CQTestSettings] File [Engine]
VirtualizationSystem name found in ini file: None
FNullVirtualizationSystem mounted, virtualization will be disabled
FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetActorFactory name: NetActorFactory id: 0
FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetSubObjectFactory name: NetSubObjectFactory id: 1
Starting LiveCoding
LiveCodingConsole Arguments: UnrealEditor Win64 Development
First instance in process group "UE_AURACRON_0xa5ca6502", spawning console
Waiting for server
Border
BreadcrumbButton
Brushes.Title
ColorPicker.ColorThemes
Default
Icons.Save
Icons.Toolbar.Settings
ListView
SoftwareCursor_CardinalCross
SoftwareCursor_Grab
TableView.DarkRow
TableView.Row
TreeView
FWorldPartitionClassDescRegistry::Initialize started...
FWorldPartitionClassDescRegistry::Initialize took 2.424 ms
XR: Instanced Stereo Rendering is Disabled
XR: MultiViewport is Disabled
XR: Mobile Multiview is Disabled
UGameplayTagsManager::InitializeManager -  0.000 s
Niagara Debugger Client Initialized | Session: 02D1BF087F514AE58000000000004A00 | Instance: 6E277A8E40A87DBF1EF11FA55CEC952F (TKT-5332).
Work queue size set to 1024.
Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
Unicast socket bound to '0.0.0.0:55416'.
Added local interface '192.168.0.35' to multicast group '230.0.0.1:6666'
Added local interface '172.17.96.1' to multicast group '230.0.0.1:6666'
Using asynchronous task graph for message deserialization.
Initializing TcpMessaging bridge
Available graphics and compute adapters:
Selecting NPU adapter: Intel(R) Iris(R) Xe Graphics
Completed device detection: Code = 0
MakeRuntimeORTDml:
  DirectML:  yes
  RHI D3D12: no
  D3D12:     yes
  NPU:       yes
Interface availability:
  GPU: yes
  RDG: no
  NPU: yes
Available graphics and compute adapters:
Selecting NPU adapter: Intel(R) Iris(R) Xe Graphics
MetaSound Page Target Initialized to 'Default'
Registering Engine Module Parameter Interfaces...
MetaSound Engine Initialized
[FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDGenericDebugDrawExtension] ...
[FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDAccelerationStructuresExtension] ...
Initialize
OnSessionChanged
Initialize
OnSessionChanged
Initialize
OnSessionChanged
Initialize
OnSessionChanged
Successfully initialized, removing startup thread
Applying CVar settings from Section [/Script/PCG.PCGEngineSettings] File [Engine]
Controle de revisão desabilitado
Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
Controle de revisão desabilitado
Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
UAndroidPermissionCallbackProxy::GetInstance
No Audio Capture implementations found. Audio input will be silent.
No Audio Capture implementations found. Audio input will be silent.
Loaded 0 collections in 0.001179 seconds
Scanning file cache for directory 'C:/Game/AURACRON/Saved/Collections/' took 0.00s
Scanning file cache for directory 'C:/Game/AURACRON/Content/Developers/tktca/Collections/' took 0.00s
Scanning file cache for directory 'C:/Game/AURACRON/Content/Collections/' took 0.00s
appError called: Fatal error: [File:D:\build\++UE5\Sync\Engine\Source\Runtime\CoreUObject\Private\UObject\UObjectGlobals.cpp] [Line: 4987] 
NewObject with empty name can't be used to create default subobjects (inside of UObject derived class constructor) as it produces inconsistent object names. Use ObjectInitializer.CreateDefaultSubobject<> instead.



Windows GetLastError: A operação foi concluída com êxito. (0)
