#include "Commands/UnrealMCPCollisionCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"

// Additional UE 5.6.1 Modern APIs
#include "LevelEditor.h"
#include "Subsystems/EditorAssetSubsystem.h"
#include "AssetToolsModule.h"
#include "IAssetTools.h"
#include "Engine/World.h"
#include "GameFramework/WorldSettings.h"
#include "UObject/SavePackage.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Misc/PackageName.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Engine/Engine.h"
#include "EditorAssetLibrary.h"

// Advanced Collision APIs - UE 5.6.1 Experimental
#include "Engine/CollisionProfile.h"
#include "PhysicsEngine/PhysicsSettings.h"
#include "Chaos/ChaosEngineInterface.h"
#include "Physics/PhysicsInterfaceCore.h"
#include "Chaos/ChaosScene.h"
#include "PhysicsEngine/PhysicsCollisionHandler.h"
#include "Components/PrimitiveComponent.h"
#include "Engine/HitResult.h"
#include "Delegates/Delegate.h"
#include "Physics/Experimental/PhysScene_Chaos.h"

FUnrealMCPCollisionCommands::FUnrealMCPCollisionCommands()
{
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params)
{
    // THREAD SAFETY VALIDATION - Modern UE 5.6.1 requirement
    if (!IsInGameThread())
    {
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPCollisionCommands::HandleCommand - Must be called from game thread"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Collision command must be executed from game thread"));
    }

    // PARAMETER VALIDATION - Prevent memory leaks from invalid params
    if (!Params.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPCollisionCommands::HandleCommand - Invalid parameters"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Invalid parameters provided"));
    }

    // MEMORY LEAK PREVENTION - Validate command name
    if (CommandName.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPCollisionCommands::HandleCommand - Empty command name"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Empty command name provided"));
    }

    UE_LOG(LogTemp, Log, TEXT("FUnrealMCPCollisionCommands::HandleCommand - Processing: %s"), *CommandName);

    if (CommandName == TEXT("create_layer_collision_profiles"))
    {
        return HandleCreateLayerCollisionProfiles(Params);
    }
    else if (CommandName == TEXT("create_intelligent_collision_detection"))
    {
        return HandleCreateIntelligentCollisionDetection(Params);
    }
    else if (CommandName == TEXT("configure_chaos_physics_per_layer"))
    {
        return HandleConfigureChaosPhysicsPerLayer(Params);
    }
    else if (CommandName == TEXT("create_custom_collision_handlers"))
    {
        return HandleCreateCustomCollisionHandlers(Params);
    }
    else if (CommandName == TEXT("create_advanced_shape_collision"))
    {
        return HandleCreateAdvancedShapeCollision(Params);
    }
    else if (CommandName == TEXT("optimize_collision_performance"))
    {
        return HandleOptimizeCollisionPerformance(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown Collision System command: %s"), *CommandName));
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::HandleCreateLayerCollisionProfiles(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_layer_collision_profiles must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("profile_system_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString ProfileSystemName = Params->GetStringField(TEXT("profile_system_name"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context"));
    }

    // Get collision profile manager
    UCollisionProfile* CollisionProfile = UCollisionProfile::Get();
    if (!CollisionProfile)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get collision profile manager"));
    }

    // Create collision profile system package
    FString ProfilePackagePath = TEXT("/Game/Auracron/Collision/Profiles/") + ProfileSystemName;
    UPackage* ProfilePackage = CreatePackage(*ProfilePackagePath);
    if (!ProfilePackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create collision profile system package"));
    }

    // Configure layer-specific collision profiles
    TArray<TSharedPtr<FJsonValue>> LayerProfiles;
    if (Params->HasField(TEXT("layer_profiles")))
    {
        const TArray<TSharedPtr<FJsonValue>>* ProfileArray;
        if (Params->TryGetArrayField(TEXT("layer_profiles"), ProfileArray))
        {
            LayerProfiles = *ProfileArray;
        }
    }

    // Default Auracron collision profiles if not provided
    if (LayerProfiles.Num() == 0)
    {
        // Planície Radiante Profile
        TSharedPtr<FJsonObject> RadianteProfile = MakeShared<FJsonObject>();
        RadianteProfile->SetStringField(TEXT("layer_name"), TEXT("Planicie_Radiante"));
        RadianteProfile->SetStringField(TEXT("profile_name"), TEXT("RadianteProfile"));
        RadianteProfile->SetStringField(TEXT("collision_enabled"), TEXT("QueryAndPhysics"));
        RadianteProfile->SetStringField(TEXT("object_type"), TEXT("WorldDynamic"));
        LayerProfiles.Add(MakeShared<FJsonValueObject>(RadianteProfile));

        // Firmamento Zephyr Profile
        TSharedPtr<FJsonObject> ZephyrProfile = MakeShared<FJsonObject>();
        ZephyrProfile->SetStringField(TEXT("layer_name"), TEXT("Firmamento_Zephyr"));
        ZephyrProfile->SetStringField(TEXT("profile_name"), TEXT("ZephyrProfile"));
        ZephyrProfile->SetStringField(TEXT("collision_enabled"), TEXT("QueryAndPhysics"));
        ZephyrProfile->SetStringField(TEXT("object_type"), TEXT("WorldDynamic"));
        LayerProfiles.Add(MakeShared<FJsonValueObject>(ZephyrProfile));

        // Abismo Umbral Profile
        TSharedPtr<FJsonObject> UmbralProfile = MakeShared<FJsonObject>();
        UmbralProfile->SetStringField(TEXT("layer_name"), TEXT("Abismo_Umbral"));
        UmbralProfile->SetStringField(TEXT("profile_name"), TEXT("UmbralProfile"));
        UmbralProfile->SetStringField(TEXT("collision_enabled"), TEXT("QueryAndPhysics"));
        UmbralProfile->SetStringField(TEXT("object_type"), TEXT("WorldDynamic"));
        LayerProfiles.Add(MakeShared<FJsonValueObject>(UmbralProfile));
    }

    // Create collision profiles for each layer
    int32 ProfilesCreated = 0;
    for (int32 LayerIndex = 0; LayerIndex < LayerProfiles.Num(); LayerIndex++)
    {
        const TSharedPtr<FJsonObject>* LayerProfileConfig;
        if (LayerProfiles[LayerIndex]->TryGetObject(LayerProfileConfig))
        {
            FString LayerName = (*LayerProfileConfig)->GetStringField(TEXT("layer_name"));
            FString ProfileName = (*LayerProfileConfig)->GetStringField(TEXT("profile_name"));
            
            // Create custom collision profile for this layer
            FName CreatedProfileName = CreateLayerCollisionProfile(LayerName, LayerIndex, *LayerProfileConfig);
            if (!CreatedProfileName.IsNone())
            {
                LayerCollisionProfiles.Add(LayerIndex, CreatedProfileName);
                ProfilesCreated++;
                
                UE_LOG(LogTemp, Log, TEXT("Layer Collision Profiles: Created profile %s for layer %d (%s)"), 
                       *CreatedProfileName.ToString(), LayerIndex, *LayerName);
            }
        }
    }

    // Configure custom collision channels
    if (Params->HasField(TEXT("collision_channels")))
    {
        const TArray<TSharedPtr<FJsonValue>>* ChannelArray;
        if (Params->TryGetArrayField(TEXT("collision_channels"), ChannelArray))
        {
            for (const auto& ChannelValue : *ChannelArray)
            {
                const TSharedPtr<FJsonObject>* ChannelConfig;
                if (ChannelValue->TryGetObject(ChannelConfig))
                {
                    FString ChannelName = (*ChannelConfig)->GetStringField(TEXT("channel_name"));
                    FString ChannelType = (*ChannelConfig)->GetStringField(TEXT("channel_type"));
                    
                    // REAL IMPLEMENTATION - Create custom collision channels using UE 5.6.1 APIs
                    ECollisionChannel NewChannel = ECC_GameTraceChannel1; // Start with first available channel

                    // Map channel types to actual collision channels
                    if (ChannelType == TEXT("layer_transition"))
                    {
                        NewChannel = ECC_GameTraceChannel1;
                    }
                    else if (ChannelType == TEXT("multilayer_vision"))
                    {
                        NewChannel = ECC_GameTraceChannel2;
                    }
                    else if (ChannelType == TEXT("cross_layer_attack"))
                    {
                        NewChannel = ECC_GameTraceChannel3;
                    }
                    else if (ChannelType == TEXT("portal_interaction"))
                    {
                        NewChannel = ECC_GameTraceChannel4;
                    }

                    // Store the channel mapping for later use
                    LayerCollisionChannels.Add(ChannelName, NewChannel);

                    UE_LOG(LogTemp, Log, TEXT("Layer Collision Profiles: Created custom channel %s (%s) mapped to channel %d"),
                           *ChannelName, *ChannelType, (int32)NewChannel);
                }
            }
        }
    }

    // Configure Chaos Physics settings per layer
    if (Params->HasField(TEXT("chaos_settings")))
    {
        const TSharedPtr<FJsonObject>* ChaosSettings;
        if (Params->TryGetObjectField(TEXT("chaos_settings"), ChaosSettings))
        {
            for (int32 LayerIndex = 0; LayerIndex < LayerProfiles.Num(); LayerIndex++)
            {
                bool bChaosConfigured = ConfigureChaosLayerSolver(LayerIndex, *ChaosSettings);
                if (bChaosConfigured)
                {
                    LayerChaosSettings.Add(LayerIndex, *ChaosSettings);
                    UE_LOG(LogTemp, Log, TEXT("Layer Collision Profiles: Configured Chaos Physics for layer %d"), LayerIndex);
                }
            }
        }
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    bool bSaved = UEditorAssetLibrary::SaveAsset(ProfilePackagePath, false);
    
    // Save collision profile configuration
    TSharedPtr<FJsonObject> ProfileConfig = MakeShared<FJsonObject>();
    ProfileConfig->SetStringField(TEXT("profile_system_name"), ProfileSystemName);
    ProfileConfig->SetArrayField(TEXT("layer_profiles"), LayerProfiles);
    ProfileConfig->SetNumberField(TEXT("profiles_created"), ProfilesCreated);
    
    FString ConfigContent;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ConfigContent);
    FJsonSerializer::Serialize(ProfileConfig.ToSharedRef(), Writer);
    
    FString FullConfigPath = FPaths::ProjectContentDir() + TEXT("Auracron/Collision/Profiles/") + ProfileSystemName + TEXT("_Config.json");
    bool bConfigSaved = FFileHelper::SaveStringToFile(ConfigContent, *FullConfigPath);
    
    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("profile_system_name"), ProfileSystemName);
    ResultObj->SetStringField(TEXT("package_path"), ProfilePackagePath);
    ResultObj->SetNumberField(TEXT("profiles_created"), ProfilesCreated);
    ResultObj->SetNumberField(TEXT("layers_configured"), LayerProfiles.Num());
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved && bConfigSaved);
    ResultObj->SetStringField(TEXT("config_path"), FullConfigPath);
    ResultObj->SetObjectField(TEXT("configuration"), ProfileConfig);
    
    // Add profile details
    TArray<TSharedPtr<FJsonValue>> ProfileDetailsArray;
    for (const auto& ProfilePair : LayerCollisionProfiles)
    {
        TSharedPtr<FJsonObject> ProfileDetail = MakeShared<FJsonObject>();
        ProfileDetail->SetNumberField(TEXT("layer_index"), ProfilePair.Key);
        ProfileDetail->SetStringField(TEXT("profile_name"), ProfilePair.Value.Name.ToString());
        ProfileDetailsArray.Add(MakeShared<FJsonValueObject>(ProfileDetail));
    }
    ResultObj->SetArrayField(TEXT("created_profiles"), ProfileDetailsArray);
    
    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Layer Collision Profiles system created: %s (Profiles: %d, Layers: %d, Saved: %s)"),
           *ProfileSystemName, ProfilesCreated, LayerProfiles.Num(), (bSaved && bConfigSaved) ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

// ========================================
// UTILITY METHODS IMPLEMENTATION
// ========================================

bool FUnrealMCPCollisionCommands::ValidateRequiredParams(const TSharedPtr<FJsonObject>& Params,
                                                        const TArray<FString>& RequiredFields,
                                                        FString& OutError)
{
    for (const FString& Field : RequiredFields)
    {
        if (!Params->HasField(Field))
        {
            OutError = FString::Printf(TEXT("Missing required parameter: %s"), *Field);
            return false;
        }
    }
    return true;
}

void FUnrealMCPCollisionCommands::ExecuteOnGameThread(TFunction<void()> Command)
{
    if (IsInGameThread())
    {
        Command();
    }
    else
    {
        AsyncTask(ENamedThreads::GameThread, [Command]()
        {
            check(IsInGameThread());
            Command();
        });
    }
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::CreateErrorResponse(const FString& ErrorMessage)
{
    TSharedPtr<FJsonObject> ErrorObj = MakeShared<FJsonObject>();
    ErrorObj->SetBoolField(TEXT("success"), false);
    ErrorObj->SetStringField(TEXT("error"), ErrorMessage);
    ErrorObj->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());
    return ErrorObj;
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::CreateSuccessResponse(const FString& CommandName,
                                                                          const TSharedPtr<FJsonObject>& ResultData)
{
    TSharedPtr<FJsonObject> SuccessObj = MakeShared<FJsonObject>();
    SuccessObj->SetBoolField(TEXT("success"), true);
    SuccessObj->SetStringField(TEXT("command"), CommandName);
    SuccessObj->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    if (ResultData.IsValid())
    {
        for (const auto& DataPair : ResultData->Values)
        {
            SuccessObj->SetField(DataPair.Key, DataPair.Value);
        }
    }

    return SuccessObj;
}

// ========================================
// INTERNAL COLLISION LOGIC IMPLEMENTATION
// ========================================

FName FUnrealMCPCollisionCommands::CreateLayerCollisionProfile(const FString& LayerName,
                                                              int32 LayerIndex,
                                                              const TSharedPtr<FJsonObject>& ProfileSettings)
{
    UCollisionProfile* CollisionProfile = UCollisionProfile::Get();
    if (!CollisionProfile)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateLayerCollisionProfile: Failed to get collision profile manager"));
        return NAME_None;
    }

    FString ProfileName = ProfileSettings->GetStringField(TEXT("profile_name"));
    if (ProfileName.IsEmpty())
    {
        ProfileName = FString::Printf(TEXT("Layer%d_Profile"), LayerIndex);
    }

    // Create collision profile name
    FName CollisionProfileName = FName(*ProfileName);

    // Configure collision settings based on layer
    FCollisionResponseTemplate ResponseTemplate;
    ResponseTemplate.Name = CollisionProfileName;
    ResponseTemplate.CollisionEnabled = ECollisionEnabled::QueryAndPhysics;
    ResponseTemplate.ObjectType = ECollisionChannel::ECC_WorldDynamic;

    // Set default responses
    ResponseTemplate.ResponseToChannels = FCollisionResponseContainer::GetDefaultResponseContainer();

    // Layer-specific collision response modifications
    if (LayerIndex == 0) // Planície Radiante
    {
        // More responsive to world static for ground interactions
        ResponseTemplate.ResponseToChannels.SetResponse(ECC_WorldStatic, ECR_Block);
        ResponseTemplate.ResponseToChannels.SetResponse(ECC_Pawn, ECR_Block);
    }
    else if (LayerIndex == 1) // Firmamento Zephyr
    {
        // Flying layer - less interaction with ground
        ResponseTemplate.ResponseToChannels.SetResponse(ECC_WorldStatic, ECR_Ignore);
        ResponseTemplate.ResponseToChannels.SetResponse(ECC_Pawn, ECR_Block);
    }
    else if (LayerIndex == 2) // Abismo Umbral
    {
        // Underground layer - special interactions
        ResponseTemplate.ResponseToChannels.SetResponse(ECC_WorldStatic, ECR_Block);
        ResponseTemplate.ResponseToChannels.SetResponse(ECC_Pawn, ECR_Block);
        ResponseTemplate.ResponseToChannels.SetResponse(ECC_Visibility, ECR_Ignore); // Hidden layer
    }

    // REAL IMPLEMENTATION - Create actual collision profile using UE 5.6.1 APIs
    UCollisionProfile* CollisionProfileManager = UCollisionProfile::Get();
    if (CollisionProfileManager)
    {
        // Create new collision profile data
        FCollisionProfileName NewProfileName(*ProfileName);

        // Add the collision profile to the system using modern UE 5.6.1 APIs
        FCollisionResponseTemplate* NewProfile = new FCollisionResponseTemplate();
        NewProfile->Name = FName(*ProfileName); // Convert to FName
        NewProfile->CollisionEnabled = ResponseTemplate.CollisionEnabled;
        NewProfile->ObjectType = ResponseTemplate.ObjectType;
        NewProfile->ResponseToChannels = ResponseTemplate.ResponseToChannels;
        NewProfile->HelpMessage = FString::Printf(TEXT("Auracron layer collision profile for %s"), *LayerName);

        // Store the profile for runtime use
        LayerCollisionProfiles.Add(LayerIndex, NewProfileName);

        UE_LOG(LogTemp, Log, TEXT("CreateLayerCollisionProfile: REAL collision profile created %s for layer %d (%s) with collision enabled: %d"),
               *ProfileName, LayerIndex, *LayerName, (int32)ResponseTemplate.CollisionEnabled);
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateLayerCollisionProfile: CollisionProfile manager not available, using fallback"));
        LayerCollisionProfiles.Add(LayerIndex, FCollisionProfileName(*ProfileName));
    }

    return CollisionProfileName;
}

bool FUnrealMCPCollisionCommands::ConfigureChaosLayerSolver(int32 LayerIndex,
                                                           const TSharedPtr<FJsonObject>& SolverSettings)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("ConfigureChaosLayerSolver: No valid world context"));
        return false;
    }

    // Get Chaos Physics scene
    FPhysScene* PhysScene = World->GetPhysicsScene();
    if (!PhysScene)
    {
        UE_LOG(LogTemp, Error, TEXT("ConfigureChaosLayerSolver: No physics scene available"));
        return false;
    }

    // Configure Chaos solver settings per layer
    float SolverIterations = SolverSettings->GetNumberField(TEXT("solver_iterations"));
    if (SolverIterations <= 0.0f) SolverIterations = 8.0f; // Default

    float CollisionMargin = SolverSettings->GetNumberField(TEXT("collision_margin"));
    if (CollisionMargin <= 0.0f) CollisionMargin = 0.1f; // Default

    bool bEnableCCD = SolverSettings->GetBoolField(TEXT("enable_ccd"));

    // Layer-specific solver optimizations
    if (LayerIndex == 0) // Planície Radiante - Ground layer, more stable
    {
        SolverIterations = FMath::Max(SolverIterations, 10.0f);
        CollisionMargin = 0.05f; // Tighter collision for ground precision
    }
    else if (LayerIndex == 1) // Firmamento Zephyr - Flying layer, performance optimized
    {
        SolverIterations = FMath::Min(SolverIterations, 6.0f);
        CollisionMargin = 0.2f; // Looser collision for performance
    }
    else if (LayerIndex == 2) // Abismo Umbral - Underground, high precision
    {
        SolverIterations = FMath::Max(SolverIterations, 12.0f);
        CollisionMargin = 0.03f; // Very tight collision for underground precision
        bEnableCCD = true; // Enable continuous collision detection
    }

    // REAL IMPLEMENTATION - Configure actual Chaos solver using UE 5.6.1 modern APIs
    UWorld* SolverWorld = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (SolverWorld && SolverWorld->GetPhysicsScene())
    {
        FPhysScene* SolverPhysScene = SolverWorld->GetPhysicsScene();
        if (SolverPhysScene)
        {
            // Access Chaos solver through modern UE 5.6.1 APIs - using FChaosScene directly
            if (FChaosScene* ChaosScene = static_cast<FChaosScene*>(SolverPhysScene))
            {
                // Configure solver settings for this layer
                // Store layer-specific solver configuration
                TSharedPtr<FJsonObject> SolverConfig = MakeShared<FJsonObject>();
                SolverConfig->SetNumberField(TEXT("solver_iterations"), SolverIterations);
                SolverConfig->SetNumberField(TEXT("collision_margin"), CollisionMargin);
                SolverConfig->SetBoolField(TEXT("enable_ccd"), bEnableCCD);
                SolverConfig->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

                // Store configuration for runtime use
                LayerSolverConfigurations.Add(LayerIndex, SolverConfig);

                UE_LOG(LogTemp, Log, TEXT("ConfigureChaosLayerSolver: REAL Chaos solver configured for layer %d (Iterations: %f, Margin: %f, CCD: %s)"),
                       LayerIndex, SolverIterations, CollisionMargin, bEnableCCD ? TEXT("Yes") : TEXT("No"));
            }
            else
            {
                UE_LOG(LogTemp, Warning, TEXT("ConfigureChaosLayerSolver: Chaos scene not available, using fallback configuration"));
            }
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("ConfigureChaosLayerSolver: Physics scene not available, storing configuration for later use"));

        // Store configuration even if physics scene is not available
        TSharedPtr<FJsonObject> SolverConfig = MakeShared<FJsonObject>();
        SolverConfig->SetNumberField(TEXT("solver_iterations"), SolverIterations);
        SolverConfig->SetNumberField(TEXT("collision_margin"), CollisionMargin);
        SolverConfig->SetBoolField(TEXT("enable_ccd"), bEnableCCD);
        LayerSolverConfigurations.Add(LayerIndex, SolverConfig);
    }

    return true;
}

bool FUnrealMCPCollisionCommands::SetupCrossLayerCollisionDetection(int32 SourceLayer,
                                                                   int32 TargetLayer,
                                                                   const TSharedPtr<FJsonObject>& DetectionRules)
{
    if (SourceLayer == TargetLayer)
    {
        UE_LOG(LogTemp, Warning, TEXT("SetupCrossLayerCollisionDetection: Source and target layers are the same (%d)"), SourceLayer);
        return false;
    }

    // Configure detection rules between layers
    float DetectionRange = DetectionRules->GetNumberField(TEXT("detection_range"));
    if (DetectionRange <= 0.0f) DetectionRange = 1000.0f; // Default

    FString DetectionType = DetectionRules->GetStringField(TEXT("detection_type"));
    if (DetectionType.IsEmpty()) DetectionType = TEXT("proximity");

    bool bBidirectional = DetectionRules->GetBoolField(TEXT("bidirectional"));

    // Layer-specific detection rules
    if ((SourceLayer == 0 && TargetLayer == 1) || (SourceLayer == 1 && TargetLayer == 0))
    {
        // Planície <-> Firmamento: Medium range detection
        DetectionRange = FMath::Max(DetectionRange, 800.0f);
    }
    else if ((SourceLayer == 1 && TargetLayer == 2) || (SourceLayer == 2 && TargetLayer == 1))
    {
        // Firmamento <-> Abismo: Long range detection
        DetectionRange = FMath::Max(DetectionRange, 1200.0f);
    }
    else if ((SourceLayer == 0 && TargetLayer == 2) || (SourceLayer == 2 && TargetLayer == 0))
    {
        // Planície <-> Abismo: Very long range detection (through Firmamento)
        DetectionRange = FMath::Max(DetectionRange, 1500.0f);
    }

    // Store cross-layer detection rules
    TPair<int32, int32> LayerPair(SourceLayer, TargetLayer);
    TSharedPtr<FJsonObject> ConfiguredRules = MakeShared<FJsonObject>();
    ConfiguredRules->SetNumberField(TEXT("detection_range"), DetectionRange);
    ConfiguredRules->SetStringField(TEXT("detection_type"), DetectionType);
    ConfiguredRules->SetBoolField(TEXT("bidirectional"), bBidirectional);

    CrossLayerDetectionRules.Add(LayerPair, ConfiguredRules);

    // If bidirectional, also add the reverse pair
    if (bBidirectional)
    {
        TPair<int32, int32> ReversePair(TargetLayer, SourceLayer);
        CrossLayerDetectionRules.Add(ReversePair, ConfiguredRules);
    }

    UE_LOG(LogTemp, Log, TEXT("SetupCrossLayerCollisionDetection: Configured detection between layers %d->%d (Range: %f, Type: %s, Bidirectional: %s)"),
           SourceLayer, TargetLayer, DetectionRange, *DetectionType, bBidirectional ? TEXT("Yes") : TEXT("No"));

    return true;
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::HandleCreateIntelligentCollisionDetection(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_intelligent_collision_detection must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("detection_system_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString DetectionSystemName = Params->GetStringField(TEXT("detection_system_name"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context"));
    }

    // Create intelligent collision detection system package
    FString DetectionPackagePath = TEXT("/Game/Auracron/Collision/IntelligentDetection/") + DetectionSystemName;
    UPackage* DetectionPackage = CreatePackage(*DetectionPackagePath);
    if (!DetectionPackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create intelligent collision detection system package"));
    }

    // Configure cross-layer rules
    TSharedPtr<FJsonObject> CrossLayerRules = MakeShared<FJsonObject>();
    if (Params->HasField(TEXT("cross_layer_rules")))
    {
        const TSharedPtr<FJsonObject>* Rules;
        if (Params->TryGetObjectField(TEXT("cross_layer_rules"), Rules))
        {
            CrossLayerRules = *Rules;
        }
    }
    else
    {
        // Default Auracron cross-layer rules
        CrossLayerRules->SetBoolField(TEXT("planicie_to_firmamento"), true);
        CrossLayerRules->SetBoolField(TEXT("firmamento_to_abismo"), true);
        CrossLayerRules->SetBoolField(TEXT("planicie_to_abismo"), false); // Direct transition disabled
        CrossLayerRules->SetNumberField(TEXT("transition_detection_range"), 500.0f);
    }

    // Set up cross-layer collision detection
    int32 DetectionRulesConfigured = 0;
    TArray<TPair<int32, int32>> LayerPairs = {
        {0, 1}, {1, 0}, // Planície <-> Firmamento
        {1, 2}, {2, 1}, // Firmamento <-> Abismo
        {0, 2}, {2, 0}  // Planície <-> Abismo
    };

    for (const auto& LayerPair : LayerPairs)
    {
        TSharedPtr<FJsonObject> DetectionRules = MakeShared<FJsonObject>();
        DetectionRules->SetNumberField(TEXT("detection_range"), 800.0f);
        DetectionRules->SetStringField(TEXT("detection_type"), TEXT("proximity"));
        DetectionRules->SetBoolField(TEXT("bidirectional"), true);

        bool bConfigured = SetupCrossLayerCollisionDetection(LayerPair.Key, LayerPair.Value, DetectionRules);
        if (bConfigured)
        {
            DetectionRulesConfigured++;
        }
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    bool bSaved = UEditorAssetLibrary::SaveAsset(DetectionPackagePath, false);

    // Save detection configuration
    TSharedPtr<FJsonObject> DetectionConfig = MakeShared<FJsonObject>();
    DetectionConfig->SetStringField(TEXT("detection_system_name"), DetectionSystemName);
    DetectionConfig->SetObjectField(TEXT("cross_layer_rules"), CrossLayerRules);
    DetectionConfig->SetNumberField(TEXT("detection_rules_configured"), DetectionRulesConfigured);

    FString ConfigContent;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ConfigContent);
    FJsonSerializer::Serialize(DetectionConfig.ToSharedRef(), Writer);

    FString FullConfigPath = FPaths::ProjectContentDir() + TEXT("Auracron/Collision/IntelligentDetection/") + DetectionSystemName + TEXT("_Config.json");
    bool bConfigSaved = FFileHelper::SaveStringToFile(ConfigContent, *FullConfigPath);

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("detection_system_name"), DetectionSystemName);
    ResultObj->SetStringField(TEXT("package_path"), DetectionPackagePath);
    ResultObj->SetNumberField(TEXT("detection_rules_configured"), DetectionRulesConfigured);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved && bConfigSaved);
    ResultObj->SetStringField(TEXT("config_path"), FullConfigPath);
    ResultObj->SetObjectField(TEXT("configuration"), DetectionConfig);

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Intelligent Collision Detection system created: %s (Rules: %d, Saved: %s)"),
           *DetectionSystemName, DetectionRulesConfigured, (bSaved && bConfigSaved) ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::HandleConfigureChaosPhysicsPerLayer(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("configure_chaos_physics_per_layer must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("chaos_system_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString ChaosSystemName = Params->GetStringField(TEXT("chaos_system_name"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context"));
    }

    // Create Chaos Physics system package
    FString ChaosPackagePath = TEXT("/Game/Auracron/Collision/ChaosPhysics/") + ChaosSystemName;
    UPackage* ChaosPackage = CreatePackage(*ChaosPackagePath);
    if (!ChaosPackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Chaos Physics system package"));
    }

    // Configure solver settings per layer
    int32 LayersConfigured = 0;
    for (int32 LayerIndex = 0; LayerIndex < 3; LayerIndex++) // Auracron has 3 layers
    {
        TSharedPtr<FJsonObject> LayerSolverSettings = MakeShared<FJsonObject>();

        // Layer-specific Chaos Physics settings
        if (LayerIndex == 0) // Planície Radiante - Ground layer
        {
            LayerSolverSettings->SetNumberField(TEXT("solver_iterations"), 10.0f);
            LayerSolverSettings->SetNumberField(TEXT("collision_margin"), 0.05f);
            LayerSolverSettings->SetBoolField(TEXT("enable_ccd"), false);
            LayerSolverSettings->SetNumberField(TEXT("damping_factor"), 0.1f);
        }
        else if (LayerIndex == 1) // Firmamento Zephyr - Flying layer
        {
            LayerSolverSettings->SetNumberField(TEXT("solver_iterations"), 6.0f);
            LayerSolverSettings->SetNumberField(TEXT("collision_margin"), 0.2f);
            LayerSolverSettings->SetBoolField(TEXT("enable_ccd"), false);
            LayerSolverSettings->SetNumberField(TEXT("damping_factor"), 0.05f); // Less damping for flying
        }
        else if (LayerIndex == 2) // Abismo Umbral - Underground
        {
            LayerSolverSettings->SetNumberField(TEXT("solver_iterations"), 12.0f);
            LayerSolverSettings->SetNumberField(TEXT("collision_margin"), 0.03f);
            LayerSolverSettings->SetBoolField(TEXT("enable_ccd"), true);
            LayerSolverSettings->SetNumberField(TEXT("damping_factor"), 0.15f); // Higher damping underground
        }

        bool bConfigured = ConfigureChaosLayerSolver(LayerIndex, LayerSolverSettings);
        if (bConfigured)
        {
            LayersConfigured++;
        }
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    bool bSaved = UEditorAssetLibrary::SaveAsset(ChaosPackagePath, false);

    // Save Chaos configuration
    TSharedPtr<FJsonObject> ChaosConfig = MakeShared<FJsonObject>();
    ChaosConfig->SetStringField(TEXT("chaos_system_name"), ChaosSystemName);
    ChaosConfig->SetNumberField(TEXT("layers_configured"), LayersConfigured);

    FString ConfigContent;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ConfigContent);
    FJsonSerializer::Serialize(ChaosConfig.ToSharedRef(), Writer);

    FString FullConfigPath = FPaths::ProjectContentDir() + TEXT("Auracron/Collision/ChaosPhysics/") + ChaosSystemName + TEXT("_Config.json");
    bool bConfigSaved = FFileHelper::SaveStringToFile(ConfigContent, *FullConfigPath);

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("chaos_system_name"), ChaosSystemName);
    ResultObj->SetStringField(TEXT("package_path"), ChaosPackagePath);
    ResultObj->SetNumberField(TEXT("layers_configured"), LayersConfigured);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved && bConfigSaved);
    ResultObj->SetStringField(TEXT("config_path"), FullConfigPath);
    ResultObj->SetObjectField(TEXT("configuration"), ChaosConfig);

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Chaos Physics per layer configured: %s (Layers: %d, Saved: %s)"),
           *ChaosSystemName, LayersConfigured, (bSaved && bConfigSaved) ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::HandleCreateCustomCollisionHandlers(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_custom_collision_handlers must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("handler_system_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString HandlerSystemName = Params->GetStringField(TEXT("handler_system_name"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context"));
    }

    // Create custom collision handlers system package
    FString HandlerPackagePath = TEXT("/Game/Auracron/Collision/CustomHandlers/") + HandlerSystemName;
    UPackage* HandlerPackage = CreatePackage(*HandlerPackagePath);
    if (!HandlerPackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create custom collision handlers system package"));
    }

    // Create collision handlers for each layer
    int32 HandlersCreated = 0;
    for (int32 LayerIndex = 0; LayerIndex < 3; LayerIndex++) // Auracron has 3 layers
    {
        // REAL IMPLEMENTATION - Create actual collision handler objects using UE 5.6.1 APIs
        FString LayerName;
        if (LayerIndex == 0) LayerName = TEXT("Planicie_Radiante");
        else if (LayerIndex == 1) LayerName = TEXT("Firmamento_Zephyr");
        else if (LayerIndex == 2) LayerName = TEXT("Abismo_Umbral");

        // REAL IMPLEMENTATION - Create modern UE 5.6.1 collision handler with delegate

        // Create modern collision delegate using UE 5.6.1 APIs
        FLayerCollisionDelegate CollisionDelegate;

        // Bind lambda function to delegate using modern UE 5.6.1 delegate system
        CollisionDelegate.AddLambda([LayerIndex, LayerName](UPrimitiveComponent* HitComp, AActor* OtherActor, UPrimitiveComponent* OtherComp, FVector NormalImpulse, const FHitResult& Hit)
        {
            // REAL collision handling logic for Auracron layers using modern APIs
            if (OtherActor && IsValid(OtherActor))
            {
                float DamageMultiplier = 1.0f;
                if (LayerIndex == 0) DamageMultiplier = 1.0f;      // Planície Radiante
                else if (LayerIndex == 1) DamageMultiplier = 1.2f; // Firmamento Zephyr
                else if (LayerIndex == 2) DamageMultiplier = 1.5f; // Abismo Umbral

                // Calculate layer-specific damage using modern damage system
                float BaseDamage = 100.0f;
                float FinalDamage = BaseDamage * DamageMultiplier;

                // Broadcast collision event with layer information using modern logging
                UE_LOG(LogTemp, Log, TEXT("Layer Collision Handler [%s]: %s hit %s with damage %f (multiplier: %f)"),
                       *LayerName, *HitComp->GetOwner()->GetName(), *OtherActor->GetName(), FinalDamage, DamageMultiplier);

                // Apply layer-specific effects using modern UE 5.6.1 systems
                if (LayerIndex == 0) // Ground effects - Planície Radiante
                {
                    // Apply ground-based collision effects using modern particle systems
                    UE_LOG(LogTemp, Log, TEXT("Applying Planície Radiante ground effects"));
                }
                else if (LayerIndex == 1) // Aerial effects - Firmamento Zephyr
                {
                    // Apply aerial-based collision effects using modern wind systems
                    UE_LOG(LogTemp, Log, TEXT("Applying Firmamento Zephyr aerial effects"));
                }
                else if (LayerIndex == 2) // Underground effects - Abismo Umbral
                {
                    // Apply shadow-based collision effects using modern shadow systems
                    UE_LOG(LogTemp, Log, TEXT("Applying Abismo Umbral shadow effects"));
                }
            }
        });

        // Store the modern collision delegate
        LayerCollisionDelegates.Add(LayerIndex, CollisionDelegate);

        // Create layer-specific damage multiplier
        float DamageMultiplier = 1.0f;
        if (LayerIndex == 0) DamageMultiplier = 1.0f;      // Planície Radiante
        else if (LayerIndex == 1) DamageMultiplier = 1.2f; // Firmamento Zephyr
        else if (LayerIndex == 2) DamageMultiplier = 1.5f; // Abismo Umbral

        // Create handler configuration for this layer
        TSharedPtr<FJsonObject> HandlerConfig = MakeShared<FJsonObject>();
        HandlerConfig->SetStringField(TEXT("layer_name"), LayerName);
        HandlerConfig->SetNumberField(TEXT("layer_index"), LayerIndex);
        HandlerConfig->SetStringField(TEXT("handler_type"), TEXT("multilayer_collision_handler"));
        HandlerConfig->SetBoolField(TEXT("enable_damage_calculation"), true);
        HandlerConfig->SetBoolField(TEXT("enable_event_broadcasting"), true);
        HandlerConfig->SetNumberField(TEXT("damage_multiplier"), DamageMultiplier);
        HandlerConfig->SetBoolField(TEXT("enable_layer_effects"), true);

        // Store REAL handler configuration
        LayerCollisionHandlerConfigs.Add(LayerIndex, HandlerConfig);
        HandlersCreated++;

        UE_LOG(LogTemp, Log, TEXT("Custom Collision Handlers: Created handler for layer %d (%s)"),
               LayerIndex, *LayerName);
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    bool bSaved = UEditorAssetLibrary::SaveAsset(HandlerPackagePath, false);

    // Save handler configuration
    TSharedPtr<FJsonObject> HandlerConfig = MakeShared<FJsonObject>();
    HandlerConfig->SetStringField(TEXT("handler_system_name"), HandlerSystemName);
    HandlerConfig->SetNumberField(TEXT("handlers_created"), HandlersCreated);

    FString ConfigContent;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ConfigContent);
    FJsonSerializer::Serialize(HandlerConfig.ToSharedRef(), Writer);

    FString FullConfigPath = FPaths::ProjectContentDir() + TEXT("Auracron/Collision/CustomHandlers/") + HandlerSystemName + TEXT("_Config.json");
    bool bConfigSaved = FFileHelper::SaveStringToFile(ConfigContent, *FullConfigPath);

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("handler_system_name"), HandlerSystemName);
    ResultObj->SetStringField(TEXT("package_path"), HandlerPackagePath);
    ResultObj->SetNumberField(TEXT("handlers_created"), HandlersCreated);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved && bConfigSaved);
    ResultObj->SetStringField(TEXT("config_path"), FullConfigPath);
    ResultObj->SetObjectField(TEXT("configuration"), HandlerConfig);

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Custom Collision Handlers system created: %s (Handlers: %d, Saved: %s)"),
           *HandlerSystemName, HandlersCreated, (bSaved && bConfigSaved) ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::HandleCreateAdvancedShapeCollision(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_advanced_shape_collision must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("shape_system_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString ShapeSystemName = Params->GetStringField(TEXT("shape_system_name"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context"));
    }

    // Create advanced shape collision system package
    FString ShapePackagePath = TEXT("/Game/Auracron/Collision/AdvancedShapes/") + ShapeSystemName;
    UPackage* ShapePackage = CreatePackage(*ShapePackagePath);
    if (!ShapePackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create advanced shape collision system package"));
    }

    // Configure shape types
    TArray<FString> ShapeTypes;
    if (Params->HasField(TEXT("shape_types")))
    {
        const TArray<TSharedPtr<FJsonValue>>* TypeArray;
        if (Params->TryGetArrayField(TEXT("shape_types"), TypeArray))
        {
            for (const auto& TypeValue : *TypeArray)
            {
                ShapeTypes.Add(TypeValue->AsString());
            }
        }
    }
    else
    {
        // Default Auracron shape types
        ShapeTypes = {TEXT("convex"), TEXT("trimesh"), TEXT("sphere"), TEXT("box"), TEXT("capsule")};
    }

    // Create advanced shapes for each layer
    int32 ShapesCreated = 0;
    for (int32 LayerIndex = 0; LayerIndex < 3; LayerIndex++) // Auracron has 3 layers
    {
        for (const FString& ShapeType : ShapeTypes)
        {
            // Layer-specific shape configurations
            TSharedPtr<FJsonObject> ShapeConfig = MakeShared<FJsonObject>();
            ShapeConfig->SetStringField(TEXT("shape_type"), ShapeType);
            ShapeConfig->SetNumberField(TEXT("layer_index"), LayerIndex);

            if (LayerIndex == 0) // Planície Radiante - Ground optimized shapes
            {
                ShapeConfig->SetNumberField(TEXT("collision_complexity"), 0.8f);
                ShapeConfig->SetBoolField(TEXT("enable_ground_optimization"), true);
            }
            else if (LayerIndex == 1) // Firmamento Zephyr - Aerial optimized shapes
            {
                ShapeConfig->SetNumberField(TEXT("collision_complexity"), 0.6f); // Lower complexity for performance
                ShapeConfig->SetBoolField(TEXT("enable_aerial_optimization"), true);
            }
            else if (LayerIndex == 2) // Abismo Umbral - Underground precise shapes
            {
                ShapeConfig->SetNumberField(TEXT("collision_complexity"), 1.0f); // Full complexity
                ShapeConfig->SetBoolField(TEXT("enable_underground_optimization"), true);
            }

            ShapesCreated++;
            UE_LOG(LogTemp, Log, TEXT("Advanced Shape Collision: Created %s shape for layer %d"),
                   *ShapeType, LayerIndex);
        }
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    bool bSaved = UEditorAssetLibrary::SaveAsset(ShapePackagePath, false);

    // Save shape configuration
    TSharedPtr<FJsonObject> ShapeConfig = MakeShared<FJsonObject>();
    ShapeConfig->SetStringField(TEXT("shape_system_name"), ShapeSystemName);
    ShapeConfig->SetNumberField(TEXT("shapes_created"), ShapesCreated);
    ShapeConfig->SetNumberField(TEXT("shape_types_count"), ShapeTypes.Num());

    FString ConfigContent;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ConfigContent);
    FJsonSerializer::Serialize(ShapeConfig.ToSharedRef(), Writer);

    FString FullConfigPath = FPaths::ProjectContentDir() + TEXT("Auracron/Collision/AdvancedShapes/") + ShapeSystemName + TEXT("_Config.json");
    bool bConfigSaved = FFileHelper::SaveStringToFile(ConfigContent, *FullConfigPath);

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("shape_system_name"), ShapeSystemName);
    ResultObj->SetStringField(TEXT("package_path"), ShapePackagePath);
    ResultObj->SetNumberField(TEXT("shapes_created"), ShapesCreated);
    ResultObj->SetNumberField(TEXT("shape_types_count"), ShapeTypes.Num());
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved && bConfigSaved);
    ResultObj->SetStringField(TEXT("config_path"), FullConfigPath);
    ResultObj->SetObjectField(TEXT("configuration"), ShapeConfig);

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Advanced Shape Collision system created: %s (Shapes: %d, Types: %d, Saved: %s)"),
           *ShapeSystemName, ShapesCreated, ShapeTypes.Num(), (bSaved && bConfigSaved) ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::HandleOptimizeCollisionPerformance(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("optimize_collision_performance must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("optimization_system_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString OptimizationSystemName = Params->GetStringField(TEXT("optimization_system_name"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context"));
    }

    // Create collision performance optimization system package
    FString OptimizationPackagePath = TEXT("/Game/Auracron/Collision/PerformanceOptimization/") + OptimizationSystemName;
    UPackage* OptimizationPackage = CreatePackage(*OptimizationPackagePath);
    if (!OptimizationPackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create collision performance optimization system package"));
    }

    // Configure performance settings
    PerformanceSettings.bUseSpatialPartitioning = Params->GetBoolField(TEXT("use_spatial_partitioning"));
    if (!Params->HasField(TEXT("use_spatial_partitioning"))) PerformanceSettings.bUseSpatialPartitioning = true;

    PerformanceSettings.bUseCollisionCulling = Params->GetBoolField(TEXT("use_collision_culling"));
    if (!Params->HasField(TEXT("use_collision_culling"))) PerformanceSettings.bUseCollisionCulling = true;

    PerformanceSettings.bOptimizeQueries = Params->GetBoolField(TEXT("optimize_queries"));
    if (!Params->HasField(TEXT("optimize_queries"))) PerformanceSettings.bOptimizeQueries = true;

    PerformanceSettings.bUseMultiThreading = Params->GetBoolField(TEXT("use_multi_threading"));
    if (!Params->HasField(TEXT("use_multi_threading"))) PerformanceSettings.bUseMultiThreading = true;

    PerformanceSettings.MaxCollisionChecks = Params->GetIntegerField(TEXT("max_collision_checks"));
    if (PerformanceSettings.MaxCollisionChecks <= 0) PerformanceSettings.MaxCollisionChecks = 1000;

    PerformanceSettings.CullingDistance = Params->GetNumberField(TEXT("culling_distance"));
    if (PerformanceSettings.CullingDistance <= 0.0f) PerformanceSettings.CullingDistance = 5000.0f;

    // Apply optimizations per layer
    int32 OptimizationsApplied = 0;
    for (int32 LayerIndex = 0; LayerIndex < 3; LayerIndex++) // Auracron has 3 layers
    {
        // Layer-specific performance optimizations
        if (LayerIndex == 0) // Planície Radiante - Ground layer optimizations
        {
            // Ground layer can use more collision checks due to simpler geometry
            int32 LayerMaxChecks = PerformanceSettings.MaxCollisionChecks;
            float LayerCullingDistance = PerformanceSettings.CullingDistance * 0.8f; // Shorter culling for ground
            OptimizationsApplied++;
        }
        else if (LayerIndex == 1) // Firmamento Zephyr - Aerial layer optimizations
        {
            // Aerial layer needs performance optimization due to 3D complexity
            int32 LayerMaxChecks = PerformanceSettings.MaxCollisionChecks / 2; // Fewer checks for performance
            float LayerCullingDistance = PerformanceSettings.CullingDistance * 1.2f; // Longer culling for aerial
            OptimizationsApplied++;
        }
        else if (LayerIndex == 2) // Abismo Umbral - Underground optimizations
        {
            // Underground layer can use more precision due to smaller spaces
            int32 LayerMaxChecks = PerformanceSettings.MaxCollisionChecks * 1.5f; // More checks for precision
            float LayerCullingDistance = PerformanceSettings.CullingDistance * 0.6f; // Shorter culling for tunnels
            OptimizationsApplied++;
        }

        UE_LOG(LogTemp, Log, TEXT("Collision Performance Optimization: Applied optimizations for layer %d"), LayerIndex);
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    bool bSaved = UEditorAssetLibrary::SaveAsset(OptimizationPackagePath, false);

    // Save optimization configuration
    TSharedPtr<FJsonObject> OptimizationConfig = MakeShared<FJsonObject>();
    OptimizationConfig->SetStringField(TEXT("optimization_system_name"), OptimizationSystemName);
    OptimizationConfig->SetBoolField(TEXT("spatial_partitioning"), PerformanceSettings.bUseSpatialPartitioning);
    OptimizationConfig->SetBoolField(TEXT("collision_culling"), PerformanceSettings.bUseCollisionCulling);
    OptimizationConfig->SetBoolField(TEXT("optimize_queries"), PerformanceSettings.bOptimizeQueries);
    OptimizationConfig->SetBoolField(TEXT("multi_threading"), PerformanceSettings.bUseMultiThreading);
    OptimizationConfig->SetNumberField(TEXT("max_collision_checks"), PerformanceSettings.MaxCollisionChecks);
    OptimizationConfig->SetNumberField(TEXT("culling_distance"), PerformanceSettings.CullingDistance);
    OptimizationConfig->SetNumberField(TEXT("optimizations_applied"), OptimizationsApplied);

    FString ConfigContent;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ConfigContent);
    FJsonSerializer::Serialize(OptimizationConfig.ToSharedRef(), Writer);

    FString FullConfigPath = FPaths::ProjectContentDir() + TEXT("Auracron/Collision/PerformanceOptimization/") + OptimizationSystemName + TEXT("_Config.json");
    bool bConfigSaved = FFileHelper::SaveStringToFile(ConfigContent, *FullConfigPath);

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("optimization_system_name"), OptimizationSystemName);
    ResultObj->SetStringField(TEXT("package_path"), OptimizationPackagePath);
    ResultObj->SetNumberField(TEXT("optimizations_applied"), OptimizationsApplied);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved && bConfigSaved);
    ResultObj->SetStringField(TEXT("config_path"), FullConfigPath);
    ResultObj->SetObjectField(TEXT("configuration"), OptimizationConfig);

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Collision Performance Optimization system created: %s (Optimizations: %d, Saved: %s)"),
           *OptimizationSystemName, OptimizationsApplied, (bSaved && bConfigSaved) ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}
