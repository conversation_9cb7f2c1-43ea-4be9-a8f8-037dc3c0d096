#pragma once

#include "CoreMinimal.h"
#include "Json.h"

// Core GameFramework APIs - UE 5.6.1 Modern
#include "GameFramework/Actor.h"
#include "GameFramework/GameMode.h"
#include "GameFramework/GameState.h"
#include "GameFramework/PlayerState.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/Controller.h"
#include "GameFramework/DamageType.h"

// AI and Spawning APIs - UE 5.6.1 Advanced
#include "AIController.h"
#include "Blueprint/AIBlueprintHelperLibrary.h"
#include "BehaviorTree/BehaviorTree.h"
#include "BehaviorTree/BlackboardComponent.h"

// Component APIs - UE 5.6.1 Enhanced
#include "Components/StaticMeshComponent.h"
#include "Components/SkeletalMeshComponent.h"
#include "Components/CapsuleComponent.h"
#include "Components/SphereComponent.h"
#include "Components/BoxComponent.h"
#include "Components/AudioComponent.h"
#include "Components/SceneComponent.h"
#include "Materials/MaterialInstanceDynamic.h"

// Timer and Gameplay APIs - UE 5.6.1
#include "Engine/TimerHandle.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"

// Editor APIs - UE 5.6.1 Enhanced
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"

// Utilities
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "UObject/Package.h"
#include "Engine/Engine.h"
#include "Engine/World.h"

/**
 * Handler class for Advanced MOBA Objectives System
 * 
 * Implements PRODUCTION READY MOBA objectives for Auracron's 3-layer system
 * with towers per layer, cascading inhibitors, specialized neutral camps,
 * and epic objectives (Guardião da Aurora, Senhor dos Ventos, Arqui-Sombra).
 * 
 * Features:
 * - Layer-specific tower systems with unique mechanics per layer
 * - Cascading inhibitor system affecting all layers
 * - Specialized neutral camps with layer-specific rewards
 * - Epic objectives with multilayer impact
 * - Dynamic buff system per layer
 * - Automated minion spawning and routing
 * - Team-based objective control
 * - Performance-optimized objective management
 * 
 * All implementations are PRODUCTION READY with:
 * - Thread safety (Game Thread execution)
 * - Robust parameter validation
 * - Mandatory disk saving
 * - Detailed logging
 * - Modern UE 5.6.1 APIs only
 */
class UNREALMCP_API FUnrealMCPMOBACommands
{
public:
    FUnrealMCPMOBACommands();

    /**
     * Main command handler dispatcher
     * @param CommandType The specific command to execute
     * @param Params JSON parameters for the command
     * @return JSON response with execution results
     */
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);

private:
    // ========================================
    // TOWER SYSTEM COMMANDS
    // ========================================

    /**
     * Creates multilayer tower system with layer-specific mechanics
     * 
     * Parameters:
     * - tower_system_name (string): Name of the tower system
     * - layer_configurations (array): Tower configs per layer
     * - tower_types (array): Different tower types (basic, advanced, nexus)
     * - damage_scaling (object): Damage scaling per layer
     * - range_modifiers (object): Attack range per layer
     * - special_abilities (array): Layer-specific tower abilities
     * 
     * Implementation:
     * - Creates custom Tower Actor classes per layer
     * - Implements layer-specific damage calculations
     * - Sets up tower targeting and attack systems
     * - Configures special abilities per layer
     * 
     * @param Params JSON parameters
     * @return JSON response with tower system details
     */
    TSharedPtr<FJsonObject> HandleCreateMultilayerTowerSystem(const TSharedPtr<FJsonObject>& Params);

    /**
     * Sets up cascading inhibitor system affecting all layers
     * 
     * Parameters:
     * - inhibitor_system_name (string): Name of the inhibitor system
     * - inhibitor_positions (array): Positions for inhibitors per layer
     * - cascade_effects (object): Effects when inhibitors are destroyed
     * - respawn_timers (object): Respawn times per inhibitor type
     * - layer_interactions (object): How inhibitors affect other layers
     * 
     * Implementation:
     * - Creates Inhibitor Actor classes with cascade logic
     * - Implements cross-layer effect system
     * - Sets up respawn and regeneration mechanics
     * - Configures team-based inhibitor control
     * 
     * @param Params JSON parameters
     * @return JSON response with inhibitor system results
     */
    TSharedPtr<FJsonObject> HandleCreateCascadingInhibitorSystem(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // NEUTRAL CAMPS COMMANDS
    // ========================================

    /**
     * Creates specialized neutral camps with layer-specific rewards
     * 
     * Parameters:
     * - camp_system_name (string): Name of the neutral camp system
     * - camp_types (array): Different camp types per layer
     * - spawn_schedules (object): Respawn timers and schedules
     * - reward_systems (object): Rewards and buffs per camp type
     * - difficulty_scaling (object): Camp difficulty per layer
     * 
     * Implementation:
     * - Creates NeutralCamp Actor classes
     * - Implements dynamic spawning system
     * - Sets up reward and buff distribution
     * - Configures camp difficulty scaling
     * 
     * @param Params JSON parameters
     * @return JSON response with neutral camp system results
     */
    TSharedPtr<FJsonObject> HandleCreateSpecializedNeutralCamps(const TSharedPtr<FJsonObject>& Params);

    /**
     * Sets up epic objectives with multilayer impact
     * 
     * Parameters:
     * - epic_system_name (string): Name of the epic objectives system
     * - epic_objectives (array): Guardião da Aurora, Senhor dos Ventos, Arqui-Sombra
     * - spawn_conditions (object): Conditions for epic objective spawning
     * - multilayer_effects (object): Effects across all layers
     * - team_benefits (object): Benefits for controlling team
     * 
     * Implementation:
     * - Creates EpicObjective Actor classes
     * - Implements multilayer effect system
     * - Sets up team control mechanics
     * - Configures epic objective abilities
     * 
     * @param Params JSON parameters
     * @return JSON response with epic objectives system results
     */
    TSharedPtr<FJsonObject> HandleCreateEpicObjectives(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // MINION AND SPAWNING COMMANDS
    // ========================================

    /**
     * Creates automated minion spawning and routing system
     * 
     * Parameters:
     * - minion_system_name (string): Name of the minion system
     * - spawn_points (array): Spawn locations per layer and team
     * - minion_types (array): Different minion types per layer
     * - routing_paths (array): Paths for minion movement
     * - spawn_intervals (object): Spawn timing per wave
     * 
     * Implementation:
     * - Creates MinionSpawner Actor classes
     * - Implements automated spawning system
     * - Sets up pathfinding integration
     * - Configures wave-based spawning
     * 
     * @param Params JSON parameters
     * @return JSON response with minion system results
     */
    TSharedPtr<FJsonObject> HandleCreateMinionSpawningSystem(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // BUFF AND TEAM SYSTEM COMMANDS
    // ========================================

    /**
     * Implements dynamic buff system per layer
     * 
     * Parameters:
     * - buff_system_name (string): Name of the buff system
     * - buff_types (array): Different buff types per layer
     * - duration_settings (object): Buff durations and stacking
     * - layer_modifiers (object): How buffs work per layer
     * - team_effects (object): Team-wide buff effects
     * 
     * Implementation:
     * - Creates BuffComponent system
     * - Implements buff stacking and duration
     * - Sets up layer-specific buff effects
     * - Configures team-wide buff distribution
     * 
     * @param Params JSON parameters
     * @return JSON response with buff system results
     */
    TSharedPtr<FJsonObject> HandleCreateDynamicBuffSystem(const TSharedPtr<FJsonObject>& Params);

    /**
     * Sets up team-based objective control system
     * 
     * Parameters:
     * - team_system_name (string): Name of the team system
     * - team_configurations (array): Team setups and properties
     * - control_mechanics (object): How teams control objectives
     * - scoring_system (object): Points and victory conditions
     * - layer_advantages (object): Team advantages per layer
     * 
     * Implementation:
     * - Extends GameState with team management
     * - Implements objective control logic
     * - Sets up scoring and victory systems
     * - Configures team-based layer advantages
     * 
     * @param Params JSON parameters
     * @return JSON response with team system results
     */
    TSharedPtr<FJsonObject> HandleCreateTeamObjectiveControl(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // UTILITY METHODS
    // ========================================

    /**
     * Validates required parameters for commands
     * @param Params Input parameters
     * @param RequiredFields Array of required field names
     * @param OutError Error message if validation fails
     * @return true if all required fields are present
     */
    bool ValidateRequiredParams(const TSharedPtr<FJsonObject>& Params, 
                               const TArray<FString>& RequiredFields, 
                               FString& OutError);

    /**
     * Ensures command execution on Game Thread
     * @param Command Lambda to execute on Game Thread
     */
    void ExecuteOnGameThread(TFunction<void()> Command);

    /**
     * Creates standardized error response
     * @param ErrorMessage The error message
     * @return JSON error response
     */
    TSharedPtr<FJsonObject> CreateErrorResponse(const FString& ErrorMessage);

    /**
     * Creates standardized success response
     * @param CommandName The executed command name
     * @param ResultData Additional result data
     * @return JSON success response
     */
    TSharedPtr<FJsonObject> CreateSuccessResponse(const FString& CommandName, 
                                                 const TSharedPtr<FJsonObject>& ResultData);

    // ========================================
    // INTERNAL MOBA LOGIC
    // ========================================

    /**
     * Spawns a tower actor with specified configuration
     * @param TowerType Type of tower to spawn
     * @param Location Spawn location
     * @param LayerIndex Layer where tower is spawned
     * @param TeamIndex Team that owns the tower
     * @return Spawned tower actor
     */
    AActor* SpawnTowerActor(const FString& TowerType, 
                           const FVector& Location,
                           int32 LayerIndex, 
                           int32 TeamIndex);

    /**
     * Calculates damage scaling based on layer and tower type
     * @param BaseDamage Base damage value
     * @param LayerIndex Layer index for scaling
     * @param TowerType Type of tower
     * @return Scaled damage value
     */
    float CalculateLayerDamageScaling(float BaseDamage, 
                                    int32 LayerIndex,
                                    const FString& TowerType);

    /**
     * Sets up minion spawn timer for automated spawning
     * @param SpawnPoint Spawn location
     * @param MinionType Type of minion to spawn
     * @param SpawnInterval Time between spawns
     * @param TeamIndex Team for spawned minions
     * @return Success status
     */
    bool SetupMinionSpawnTimer(const FVector& SpawnPoint,
                              const FString& MinionType,
                              float SpawnInterval,
                              int32 TeamIndex);

    /**
     * Creates a robust tower actor with all components using modern UE 5.6.1 APIs
     * @param World Target world for spawning
     * @param TowerName Name of the tower
     * @param Location Spawn location
     * @param LayerIndex Layer index for tower configuration
     * @param Lane Lane index
     * @param TowerIndex Tower index in lane
     * @return Created tower actor or nullptr if failed
     */
    AActor* CreateRobustTowerActor(UWorld* World,
                                  const FString& TowerName,
                                  const FVector& Location,
                                  int32 LayerIndex,
                                  int32 Lane,
                                  int32 TowerIndex);

    /**
     * Creates a robust minion actor with all components using modern UE 5.6.1 APIs
     * @param World Target world for spawning
     * @param MinionType Type of minion (Melee, Ranged, Siege)
     * @param SpawnPoint Spawn location
     * @param TeamIndex Team index (0 or 1)
     * @return Created minion actor or nullptr if failed
     */
    AActor* CreateRobustMinionActor(UWorld* World,
                                   const FString& MinionType,
                                   const FVector& SpawnPoint,
                                   int32 TeamIndex);

private:
    // Tower actors per layer and team
    TMap<int32, TMap<int32, TArray<TWeakObjectPtr<AActor>>>> LayerTowers;
    
    // Inhibitor actors per layer
    TMap<int32, TArray<TWeakObjectPtr<AActor>>> LayerInhibitors;
    
    // Neutral camp actors
    TArray<TWeakObjectPtr<AActor>> NeutralCamps;
    
    // Epic objective actors
    TArray<TWeakObjectPtr<AActor>> EpicObjectives;
    
    // Minion spawn timers
    TArray<FTimerHandle> MinionSpawnTimers;
    
    // Buff system components
    TMap<FString, TWeakObjectPtr<UActorComponent>> BuffComponents;
    
    // Team control data
    struct FTeamControlData
    {
        int32 TeamIndex = 0;
        TArray<TWeakObjectPtr<AActor>> ControlledObjectives;
        float TeamScore = 0.0f;
        TMap<int32, float> LayerAdvantages;
    };
    TArray<FTeamControlData> TeamControlData;
};
