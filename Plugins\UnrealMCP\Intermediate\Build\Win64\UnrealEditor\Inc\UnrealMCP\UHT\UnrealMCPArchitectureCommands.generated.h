// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Commands/UnrealMCPArchitectureCommands.h"

#ifdef UNREALMCP_UnrealMCPArchitectureCommands_generated_h
#error "UnrealMCPArchitectureCommands.generated.h already included, missing '#pragma once' in UnrealMCPArchitectureCommands.h"
#endif
#define UNREALMCP_UnrealMCPArchitectureCommands_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FAuracronTowerConfig **********************************************
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPArchitectureCommands_h_49_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronTowerConfig;
// ********** End ScriptStruct FAuracronTowerConfig ************************************************

// ********** Begin ScriptStruct FBuildingElementConfig ********************************************
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPArchitectureCommands_h_106_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FBuildingElementConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FBuildingElementConfig;
// ********** End ScriptStruct FBuildingElementConfig **********************************************

// ********** Begin ScriptStruct FSplineStructureConfig ********************************************
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPArchitectureCommands_h_143_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSplineStructureConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSplineStructureConfig;
// ********** End ScriptStruct FSplineStructureConfig **********************************************

// ********** Begin Class UUnrealMCPArchitectureCommands *******************************************
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPArchitectureCommands_NoRegister();

#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPArchitectureCommands_h_180_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUUnrealMCPArchitectureCommands(); \
	friend struct Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPArchitectureCommands_NoRegister(); \
public: \
	DECLARE_CLASS2(UUnrealMCPArchitectureCommands, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/UnrealMCP"), Z_Construct_UClass_UUnrealMCPArchitectureCommands_NoRegister) \
	DECLARE_SERIALIZER(UUnrealMCPArchitectureCommands)


#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPArchitectureCommands_h_180_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UUnrealMCPArchitectureCommands(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UUnrealMCPArchitectureCommands(UUnrealMCPArchitectureCommands&&) = delete; \
	UUnrealMCPArchitectureCommands(const UUnrealMCPArchitectureCommands&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UUnrealMCPArchitectureCommands); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UUnrealMCPArchitectureCommands); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UUnrealMCPArchitectureCommands) \
	NO_API virtual ~UUnrealMCPArchitectureCommands();


#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPArchitectureCommands_h_177_PROLOG
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPArchitectureCommands_h_180_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPArchitectureCommands_h_180_INCLASS_NO_PURE_DECLS \
	FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPArchitectureCommands_h_180_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UUnrealMCPArchitectureCommands;

// ********** End Class UUnrealMCPArchitectureCommands *********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPArchitectureCommands_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
