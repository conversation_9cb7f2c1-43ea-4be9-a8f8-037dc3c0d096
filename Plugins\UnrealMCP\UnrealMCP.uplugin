{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "UnrealMCP", "Description": "Model Context Protocol implementation for Unreal Engine", "Category": "Editor", "CreatedBy": "Chong-<PERSON> Lim", "CreatedByURL": "", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": false, "Installed": false, "EngineVersion": "5.6.0", "Modules": [{"Name": "UnrealMCP", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "WhitelistPlatforms": ["Win64", "<PERSON>", "Linux"]}], "Plugins": [{"Name": "EditorScriptingUtilities", "Enabled": true}, {"Name": "PCG", "Enabled": true}, {"Name": "ComputeFramework", "Enabled": true}]}