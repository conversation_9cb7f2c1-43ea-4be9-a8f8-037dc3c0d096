#pragma once

#include "CoreMinimal.h"
#include "Json.h"

// Modern UE 5.6.1 Collision APIs
#include "PhysicsEngine/BodySetup.h"
#include "PhysicsEngine/AggregateGeom.h"
#include "PhysicsEngine/ConvexElem.h"
#include "PhysicsEngine/BoxElem.h"
#include "PhysicsEngine/SphereElem.h"
#include "PhysicsEngine/SphylElem.h"
#include "PhysicsEngine/TaperedCapsuleElem.h"
#include "PhysicsEngine/LevelSetElem.h"

// Modern UE 5.6.1 Physical Material APIs
#include "PhysicalMaterials/PhysicalMaterial.h"
#include "PhysicalMaterials/PhysicalMaterialMask.h"

// Experimental UE 5.6.1 Chaos Physics APIs - ENCONTRADAS!
#include "Chaos/ChaosPhysicalMaterial.h"
#include "PhysicsEngine/ChaosBlueprintLibrary.h"
#include "Physics/Experimental/ChaosEventRelay.h"
#include "LandscapePhysicalMaterial.h"

// Collision Profile APIs
#include "Engine/CollisionProfile.h"

// Engine APIs
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Components/StaticMeshComponent.h"
#include "Components/PrimitiveComponent.h"
#include "Components/BoxComponent.h"
#include "GameFramework/Actor.h"

// Editor APIs
#include "Editor.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"

#include "UnrealMCPCollisionAdvancedCommands.generated.h"

// ========================================
// MODERN UE 5.6.1 COLLISION STRUCTURES
// ========================================

/**
 * Auracron collision configuration
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FAuracronCollisionConfig
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString CollisionName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 LayerIndex = 0;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TEnumAsByte<ECollisionEnabled::Type> CollisionEnabled = ECollisionEnabled::QueryAndPhysics;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TEnumAsByte<ECollisionResponse> CollisionResponse = ECollisionResponse::ECR_Block;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FName CollisionProfileName = TEXT("BlockAll");

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bUseComplexAsSimple = false;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bUseChaosPhysics = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bGenerateOverlapEvents = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TSoftObjectPtr<UPhysicalMaterial> PhysicalMaterial;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TSoftObjectPtr<UChaosPhysicalMaterial> ChaosPhysicalMaterial;

    FAuracronCollisionConfig()
    {
        CollisionName = TEXT("AuracronCollision");
        LayerIndex = 0;
        CollisionEnabled = ECollisionEnabled::QueryAndPhysics;
        CollisionResponse = ECollisionResponse::ECR_Block;
        CollisionProfileName = TEXT("BlockAll");
        bUseComplexAsSimple = false;
        bUseChaosPhysics = true;
        bGenerateOverlapEvents = true;
        PhysicalMaterial = nullptr;
        ChaosPhysicalMaterial = nullptr;
    }
};

/**
 * Precise collision geometry configuration
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FPreciseCollisionConfig
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString GeometryName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString GeometryType; // box, sphere, capsule, convex, levelset

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector GeometrySize = FVector(100.0f, 100.0f, 100.0f);

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FTransform GeometryTransform;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float GeometryRadius = 50.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float GeometryHeight = 100.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bUseExperimentalLevelSet = false;

    FPreciseCollisionConfig()
    {
        GeometryName = TEXT("PreciseGeometry");
        GeometryType = TEXT("box");
        GeometrySize = FVector(100.0f, 100.0f, 100.0f);
        GeometryTransform = FTransform::Identity;
        GeometryRadius = 50.0f;
        GeometryHeight = 100.0f;
        bUseExperimentalLevelSet = false;
    }
};

/**
 * Chaos physics configuration
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FChaosPhysicsConfig
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString PhysicsName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float Mass = 1.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float LinearDamping = 0.01f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float AngularDamping = 0.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bEnableGravity = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bSimulatePhysics = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bUseChaosFlesh = false;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bUseChaosMover = false;

    FChaosPhysicsConfig()
    {
        PhysicsName = TEXT("ChaosPhysics");
        Mass = 1.0f;
        LinearDamping = 0.01f;
        AngularDamping = 0.0f;
        bEnableGravity = true;
        bSimulatePhysics = true;
        bUseChaosFlesh = false;
        bUseChaosMover = false;
    }
};

/**
 * Trigger volume configuration
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FTriggerVolumeConfig
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString TriggerName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString TriggerType; // enter, exit, overlap, touch

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector TriggerSize = FVector(200.0f, 200.0f, 200.0f);

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector TriggerLocation = FVector::ZeroVector;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bTriggerOnlyPlayers = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bUseChaosEvents = true;

    FTriggerVolumeConfig()
    {
        TriggerName = TEXT("TriggerVolume");
        TriggerType = TEXT("overlap");
        TriggerSize = FVector(200.0f, 200.0f, 200.0f);
        TriggerLocation = FVector::ZeroVector;
        bTriggerOnlyPlayers = true;
        bUseChaosEvents = true;
    }
};

/**
 * UnrealMCP Collision Advanced Commands - Modern UE 5.6.1 Implementation
 * Handles advanced collision and physics configuration with Chaos Physics integration
 */
UCLASS()
class UNREALMCP_API UUnrealMCPCollisionAdvancedCommands : public UObject
{
    GENERATED_BODY()

public:
    /**
     * Handle collision command routing
     * @param CommandName Name of the command to execute
     * @param Params JSON parameters for the command
     * @return JSON response with command results
     */
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params);

    /**
     * Create precise collision using modern UE 5.6.1 APIs
     * @param Params - Must include:
     *                "collision_name" - Name of the collision
     *                "target_actor" - Target actor name
     *                "geometry_type" - Type of geometry (box, sphere, capsule, convex, levelset)
     *                "geometry_size" - Size of the geometry
     *                "layer_index" - Layer index for layer-specific settings
     * @return JSON response with collision creation results
     */
    TSharedPtr<FJsonObject> HandleCreatePreciseCollision(const TSharedPtr<FJsonObject>& Params);

    /**
     * Setup physics materials using modern APIs
     * @param Params - Must include:
     *                "material_name" - Name of the physics material
     *                "material_type" - Type (standard, chaos, landscape)
     *                "friction" - Friction coefficient
     *                "restitution" - Restitution coefficient
     *                "density" - Material density
     * @return JSON response with material setup results
     */
    TSharedPtr<FJsonObject> HandleSetupPhysicsMaterials(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create collision profiles for different object types
     * @param Params - Must include:
     *                "profile_name" - Name of the collision profile
     *                "collision_enabled" - Collision enabled type
     *                "object_type" - Object type
     *                "collision_responses" - Array of collision responses
     * @return JSON response with profile creation results
     */
    TSharedPtr<FJsonObject> HandleCreateCollisionProfiles(const TSharedPtr<FJsonObject>& Params);

    /**
     * Setup Chaos Physics with experimental features
     * @param Params - Must include:
     *                "physics_name" - Name of the physics setup
     *                "target_actor" - Target actor name
     *                "chaos_config" - Chaos physics configuration
     *                "use_experimental" - Use experimental Chaos features
     * @return JSON response with Chaos setup results
     */
    TSharedPtr<FJsonObject> HandleSetupChaosPhysics(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create trigger volumes with Chaos event integration
     * @param Params - Must include:
     *                "trigger_name" - Name of the trigger volume
     *                "trigger_type" - Type of trigger
     *                "location" - Trigger location
     *                "size" - Trigger size
     *                "layer_index" - Layer index
     * @return JSON response with trigger creation results
     */
    TSharedPtr<FJsonObject> HandleCreateTriggerVolumes(const TSharedPtr<FJsonObject>& Params);

private:
    /**
     * Create robust collision geometry using modern UE 5.6.1 APIs
     * @param CollisionConfig Collision configuration
     * @param GeometryConfig Geometry configuration
     * @param TargetActor Target actor for collision
     * @return Success status
     */
    bool CreateRobustCollisionGeometry(const FAuracronCollisionConfig& CollisionConfig, const FPreciseCollisionConfig& GeometryConfig, AActor* TargetActor);

    /**
     * Setup advanced physics material using modern APIs
     * @param MaterialName Name of the material
     * @param MaterialType Type of material
     * @param LayerIndex Layer index
     * @return Created physics material
     */
    UPhysicalMaterial* SetupAdvancedPhysicsMaterial(const FString& MaterialName, const FString& MaterialType, int32 LayerIndex);

    /**
     * Create Chaos physics material using experimental APIs
     * @param MaterialName Name of the material
     * @param LayerIndex Layer index
     * @return Created Chaos physics material
     */
    UChaosPhysicalMaterial* CreateChaosPhysicsMaterial(const FString& MaterialName, int32 LayerIndex);

    /**
     * Setup collision profile using modern APIs
     * @param ProfileName Name of the profile
     * @param CollisionConfig Collision configuration
     * @return Success status
     */
    bool SetupCollisionProfile(const FString& ProfileName, const FAuracronCollisionConfig& CollisionConfig);

    /**
     * Configure Chaos physics with experimental features
     * @param PhysicsConfig Chaos physics configuration
     * @param TargetActor Target actor
     * @return Success status
     */
    bool ConfigureChaosPhysics(const FChaosPhysicsConfig& PhysicsConfig, AActor* TargetActor);

    /**
     * Create advanced trigger volume using Chaos events
     * @param TriggerConfig Trigger volume configuration
     * @return Created trigger volume actor
     */
    AActor* CreateAdvancedTriggerVolume(const FTriggerVolumeConfig& TriggerConfig);

    /**
     * Get layer-specific collision settings
     * @param LayerIndex Layer index
     * @return Collision configuration for the layer
     */
    FAuracronCollisionConfig GetLayerCollisionSettings(int32 LayerIndex);

private:
    // Cache for created collision geometries
    UPROPERTY()
    TMap<FString, TObjectPtr<UBodySetup>> CollisionGeometries;

    // Cache for physics materials
    UPROPERTY()
    TMap<FString, TObjectPtr<UPhysicalMaterial>> PhysicsMaterials;

    // Cache for Chaos physics materials
    UPROPERTY()
    TMap<FString, TObjectPtr<UChaosPhysicalMaterial>> ChaosPhysicsMaterials;

    // Cache for trigger volumes
    UPROPERTY()
    TMap<FString, TObjectPtr<AActor>> TriggerVolumes;
};
