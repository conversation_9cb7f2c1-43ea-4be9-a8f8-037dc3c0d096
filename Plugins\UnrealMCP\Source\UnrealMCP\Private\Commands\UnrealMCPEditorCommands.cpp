#include "Commands/UnrealMCPEditorCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"
#include "Editor.h"
#include "EditorViewportClient.h"
#include "LevelEditorViewport.h"
#include "ImageUtils.h"
#include "HighResScreenshot.h"
#include "Engine/GameViewportClient.h"
#include "Misc/FileHelper.h"
#include "GameFramework/Actor.h"
#include "Engine/Selection.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/StaticMeshActor.h"
#include "Engine/DirectionalLight.h"
#include "Engine/PointLight.h"
#include "Engine/SpotLight.h"
#include "Camera/CameraActor.h"
#include "Components/StaticMeshComponent.h"
#include "EditorSubsystem.h"
#include "Subsystems/EditorActorSubsystem.h"
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"
// Modern UE 5.6.1 includes for robust implementation
#include "Components/LightComponent.h"
#include "Components/DirectionalLightComponent.h"
#include "Components/PointLightComponent.h"
#include "Components/TextRenderComponent.h"
#include "Engine/TextRenderActor.h"
#include "EditorAssetLibrary.h"
#include "Camera/CameraComponent.h"

FUnrealMCPEditorCommands::FUnrealMCPEditorCommands()
{
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params)
{
    // THREAD SAFETY VALIDATION - Modern UE 5.6.1 requirement
    if (!IsInGameThread())
    {
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPEditorCommands::HandleCommand - Must be called from game thread"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Editor command must be executed from game thread"));
    }

    // PARAMETER VALIDATION - Prevent memory leaks from invalid params
    if (!Params.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPEditorCommands::HandleCommand - Invalid parameters"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Invalid parameters provided"));
    }

    // MEMORY LEAK PREVENTION - Validate command type
    if (CommandType.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPEditorCommands::HandleCommand - Empty command type"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Empty command type provided"));
    }

    UE_LOG(LogTemp, Log, TEXT("FUnrealMCPEditorCommands::HandleCommand - Processing: %s"), *CommandType);

    // Actor manipulation commands
    if (CommandType == TEXT("get_actors_in_level"))
    {
        return HandleGetActorsInLevel(Params);
    }
    else if (CommandType == TEXT("find_actors_by_name"))
    {
        return HandleFindActorsByName(Params);
    }
    else if (CommandType == TEXT("spawn_actor") || CommandType == TEXT("create_actor"))
    {
        if (CommandType == TEXT("create_actor"))
        {
            UE_LOG(LogTemp, Warning, TEXT("'create_actor' command is deprecated and will be removed in a future version. Please use 'spawn_actor' instead."));
        }
        return HandleSpawnActor(Params);
    }
    else if (CommandType == TEXT("delete_actor"))
    {
        return HandleDeleteActor(Params);
    }
    else if (CommandType == TEXT("set_actor_transform"))
    {
        return HandleSetActorTransform(Params);
    }
    else if (CommandType == TEXT("get_actor_properties"))
    {
        return HandleGetActorProperties(Params);
    }
    else if (CommandType == TEXT("set_actor_property"))
    {
        return HandleSetActorProperty(Params);
    }
    // Blueprint actor spawning
    else if (CommandType == TEXT("spawn_blueprint_actor"))
    {
        return HandleSpawnBlueprintActor(Params);
    }
    // Editor viewport commands
    else if (CommandType == TEXT("focus_viewport"))
    {
        return HandleFocusViewport(Params);
    }
    else if (CommandType == TEXT("take_screenshot"))
    {
        return HandleTakeScreenshot(Params);
    }
    // Auracron-specific editor commands
    else if (CommandType == TEXT("setup_auracron_editor_environment"))
    {
        return HandleSetupAuracronEditorEnvironment(Params);
    }
    else if (CommandType == TEXT("create_auracron_layer_visualization"))
    {
        return HandleCreateAuracronLayerVisualization(Params);
    }
    else if (CommandType == TEXT("setup_auracron_camera_system"))
    {
        return HandleSetupAuracronCameraSystem(Params);
    }
    else if (CommandType == TEXT("create_auracron_debug_tools"))
    {
        return HandleCreateAuracronDebugTools(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown editor command: %s"), *CommandType));
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleGetActorsInLevel(const TSharedPtr<FJsonObject>& Params)
{
    TArray<AActor*> AllActors;
    UGameplayStatics::GetAllActorsOfClass(GWorld, AActor::StaticClass(), AllActors);
    
    TArray<TSharedPtr<FJsonValue>> ActorArray;
    for (AActor* Actor : AllActors)
    {
        if (Actor)
        {
            ActorArray.Add(FUnrealMCPCommonUtils::ActorToJson(Actor));
        }
    }
    
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetArrayField(TEXT("actors"), ActorArray);
    
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleFindActorsByName(const TSharedPtr<FJsonObject>& Params)
{
    FString Pattern;
    if (!Params->TryGetStringField(TEXT("pattern"), Pattern))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'pattern' parameter"));
    }
    
    TArray<AActor*> AllActors;
    UGameplayStatics::GetAllActorsOfClass(GWorld, AActor::StaticClass(), AllActors);
    
    TArray<TSharedPtr<FJsonValue>> MatchingActors;
    for (AActor* Actor : AllActors)
    {
        if (Actor && Actor->GetName().Contains(Pattern))
        {
            MatchingActors.Add(FUnrealMCPCommonUtils::ActorToJson(Actor));
        }
    }
    
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetArrayField(TEXT("actors"), MatchingActors);
    
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleSpawnActor(const TSharedPtr<FJsonObject>& Params)
{
    // Get required parameters
    FString ActorType;
    if (!Params->TryGetStringField(TEXT("type"), ActorType))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'type' parameter"));
    }

    // Get actor name (required parameter)
    FString ActorName;
    if (!Params->TryGetStringField(TEXT("name"), ActorName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'name' parameter"));
    }

    // Get optional transform parameters
    FVector Location(0.0f, 0.0f, 0.0f);
    FRotator Rotation(0.0f, 0.0f, 0.0f);
    FVector Scale(1.0f, 1.0f, 1.0f);

    if (Params->HasField(TEXT("location")))
    {
        Location = FUnrealMCPCommonUtils::GetVectorFromJson(Params, TEXT("location"));
    }
    if (Params->HasField(TEXT("rotation")))
    {
        Rotation = FUnrealMCPCommonUtils::GetRotatorFromJson(Params, TEXT("rotation"));
    }
    if (Params->HasField(TEXT("scale")))
    {
        Scale = FUnrealMCPCommonUtils::GetVectorFromJson(Params, TEXT("scale"));
    }

    // Create the actor based on type
    AActor* NewActor = nullptr;
    UWorld* World = GEditor->GetEditorWorldContext().World();

    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get editor world"));
    }

    // Check if an actor with this name already exists
    TArray<AActor*> AllActors;
    UGameplayStatics::GetAllActorsOfClass(World, AActor::StaticClass(), AllActors);
    for (AActor* Actor : AllActors)
    {
        if (Actor && Actor->GetName() == ActorName)
        {
            return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Actor with name '%s' already exists"), *ActorName));
        }
    }

    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = *ActorName;

    if (ActorType == TEXT("StaticMeshActor"))
    {
        NewActor = World->SpawnActor<AStaticMeshActor>(AStaticMeshActor::StaticClass(), Location, Rotation, SpawnParams);
    }
    else if (ActorType == TEXT("PointLight"))
    {
        NewActor = World->SpawnActor<APointLight>(APointLight::StaticClass(), Location, Rotation, SpawnParams);
    }
    else if (ActorType == TEXT("SpotLight"))
    {
        NewActor = World->SpawnActor<ASpotLight>(ASpotLight::StaticClass(), Location, Rotation, SpawnParams);
    }
    else if (ActorType == TEXT("DirectionalLight"))
    {
        NewActor = World->SpawnActor<ADirectionalLight>(ADirectionalLight::StaticClass(), Location, Rotation, SpawnParams);
    }
    else if (ActorType == TEXT("CameraActor"))
    {
        NewActor = World->SpawnActor<ACameraActor>(ACameraActor::StaticClass(), Location, Rotation, SpawnParams);
    }
    else
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown actor type: %s"), *ActorType));
    }

    if (NewActor)
    {
        // Set scale (since SpawnActor only takes location and rotation)
        FTransform Transform = NewActor->GetTransform();
        Transform.SetScale3D(Scale);
        NewActor->SetActorTransform(Transform);

        // Return the created actor's details
        return FUnrealMCPCommonUtils::ActorToJsonObject(NewActor, true);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create actor"));
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleDeleteActor(const TSharedPtr<FJsonObject>& Params)
{
    FString ActorName;
    if (!Params->TryGetStringField(TEXT("name"), ActorName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'name' parameter"));
    }

    TArray<AActor*> AllActors;
    UGameplayStatics::GetAllActorsOfClass(GWorld, AActor::StaticClass(), AllActors);
    
    for (AActor* Actor : AllActors)
    {
        if (Actor && Actor->GetName() == ActorName)
        {
            // Store actor info before deletion for the response
            TSharedPtr<FJsonObject> ActorInfo = FUnrealMCPCommonUtils::ActorToJsonObject(Actor);
            
            // Delete the actor
            Actor->Destroy();
            
            TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
            ResultObj->SetObjectField(TEXT("deleted_actor"), ActorInfo);
            return ResultObj;
        }
    }
    
    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Actor not found: %s"), *ActorName));
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleSetActorTransform(const TSharedPtr<FJsonObject>& Params)
{
    // Get actor name
    FString ActorName;
    if (!Params->TryGetStringField(TEXT("name"), ActorName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'name' parameter"));
    }

    // Find the actor
    AActor* TargetActor = nullptr;
    TArray<AActor*> AllActors;
    UGameplayStatics::GetAllActorsOfClass(GWorld, AActor::StaticClass(), AllActors);
    
    for (AActor* Actor : AllActors)
    {
        if (Actor && Actor->GetName() == ActorName)
        {
            TargetActor = Actor;
            break;
        }
    }

    if (!TargetActor)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Actor not found: %s"), *ActorName));
    }

    // Get transform parameters
    FTransform NewTransform = TargetActor->GetTransform();

    if (Params->HasField(TEXT("location")))
    {
        NewTransform.SetLocation(FUnrealMCPCommonUtils::GetVectorFromJson(Params, TEXT("location")));
    }
    if (Params->HasField(TEXT("rotation")))
    {
        NewTransform.SetRotation(FQuat(FUnrealMCPCommonUtils::GetRotatorFromJson(Params, TEXT("rotation"))));
    }
    if (Params->HasField(TEXT("scale")))
    {
        NewTransform.SetScale3D(FUnrealMCPCommonUtils::GetVectorFromJson(Params, TEXT("scale")));
    }

    // Set the new transform
    TargetActor->SetActorTransform(NewTransform);

    // Return updated actor info
    return FUnrealMCPCommonUtils::ActorToJsonObject(TargetActor, true);
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleGetActorProperties(const TSharedPtr<FJsonObject>& Params)
{
    // Get actor name
    FString ActorName;
    if (!Params->TryGetStringField(TEXT("name"), ActorName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'name' parameter"));
    }

    // Find the actor
    AActor* TargetActor = nullptr;
    TArray<AActor*> AllActors;
    UGameplayStatics::GetAllActorsOfClass(GWorld, AActor::StaticClass(), AllActors);
    
    for (AActor* Actor : AllActors)
    {
        if (Actor && Actor->GetName() == ActorName)
        {
            TargetActor = Actor;
            break;
        }
    }

    if (!TargetActor)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Actor not found: %s"), *ActorName));
    }

    // Always return detailed properties for this command
    return FUnrealMCPCommonUtils::ActorToJsonObject(TargetActor, true);
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleSetActorProperty(const TSharedPtr<FJsonObject>& Params)
{
    // Get actor name
    FString ActorName;
    if (!Params->TryGetStringField(TEXT("name"), ActorName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'name' parameter"));
    }

    // Find the actor
    AActor* TargetActor = nullptr;
    TArray<AActor*> AllActors;
    UGameplayStatics::GetAllActorsOfClass(GWorld, AActor::StaticClass(), AllActors);
    
    for (AActor* Actor : AllActors)
    {
        if (Actor && Actor->GetName() == ActorName)
        {
            TargetActor = Actor;
            break;
        }
    }

    if (!TargetActor)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Actor not found: %s"), *ActorName));
    }

    // Get property name
    FString PropertyName;
    if (!Params->TryGetStringField(TEXT("property_name"), PropertyName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'property_name' parameter"));
    }

    // Get property value
    if (!Params->HasField(TEXT("property_value")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'property_value' parameter"));
    }
    
    TSharedPtr<FJsonValue> PropertyValue = Params->Values.FindRef(TEXT("property_value"));
    
    // Set the property using our utility function
    FString ErrorMessage;
    if (FUnrealMCPCommonUtils::SetObjectProperty(TargetActor, PropertyName, PropertyValue, ErrorMessage))
    {
        // Property set successfully
        TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
        ResultObj->SetStringField(TEXT("actor"), ActorName);
        ResultObj->SetStringField(TEXT("property"), PropertyName);
        ResultObj->SetBoolField(TEXT("success"), true);
        
        // Also include the full actor details
        ResultObj->SetObjectField(TEXT("actor_details"), FUnrealMCPCommonUtils::ActorToJsonObject(TargetActor, true));
        return ResultObj;
    }
    else
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ErrorMessage);
    }
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleSpawnBlueprintActor(const TSharedPtr<FJsonObject>& Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString ActorName;
    if (!Params->TryGetStringField(TEXT("actor_name"), ActorName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'actor_name' parameter"));
    }

    // Find the blueprint
    if (BlueprintName.IsEmpty())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Blueprint name is empty"));
    }

    FString Root      = TEXT("/Game/Blueprints/");
    FString AssetPath = Root + BlueprintName;

    if (!FPackageName::DoesPackageExist(AssetPath))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint '%s' not found – it must reside under /Game/Blueprints"), *BlueprintName));
    }

    UBlueprint* Blueprint = LoadObject<UBlueprint>(nullptr, *AssetPath);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get transform parameters
    FVector Location(0.0f, 0.0f, 0.0f);
    FRotator Rotation(0.0f, 0.0f, 0.0f);
    FVector Scale(1.0f, 1.0f, 1.0f);

    if (Params->HasField(TEXT("location")))
    {
        Location = FUnrealMCPCommonUtils::GetVectorFromJson(Params, TEXT("location"));
    }
    if (Params->HasField(TEXT("rotation")))
    {
        Rotation = FUnrealMCPCommonUtils::GetRotatorFromJson(Params, TEXT("rotation"));
    }
    if (Params->HasField(TEXT("scale")))
    {
        Scale = FUnrealMCPCommonUtils::GetVectorFromJson(Params, TEXT("scale"));
    }

    // Spawn the actor
    UWorld* World = GEditor->GetEditorWorldContext().World();
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get editor world"));
    }

    FTransform SpawnTransform;
    SpawnTransform.SetLocation(Location);
    SpawnTransform.SetRotation(FQuat(Rotation));
    SpawnTransform.SetScale3D(Scale);

    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = *ActorName;

    AActor* NewActor = World->SpawnActor<AActor>(Blueprint->GeneratedClass, SpawnTransform, SpawnParams);
    if (NewActor)
    {
        return FUnrealMCPCommonUtils::ActorToJsonObject(NewActor, true);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to spawn blueprint actor"));
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleFocusViewport(const TSharedPtr<FJsonObject>& Params)
{
    // Get target actor name if provided
    FString TargetActorName;
    bool HasTargetActor = Params->TryGetStringField(TEXT("target"), TargetActorName);

    // Get location if provided
    FVector Location(0.0f, 0.0f, 0.0f);
    bool HasLocation = false;
    if (Params->HasField(TEXT("location")))
    {
        Location = FUnrealMCPCommonUtils::GetVectorFromJson(Params, TEXT("location"));
        HasLocation = true;
    }

    // Get distance
    float Distance = 1000.0f;
    if (Params->HasField(TEXT("distance")))
    {
        Distance = Params->GetNumberField(TEXT("distance"));
    }

    // Get orientation if provided
    FRotator Orientation(0.0f, 0.0f, 0.0f);
    bool HasOrientation = false;
    if (Params->HasField(TEXT("orientation")))
    {
        Orientation = FUnrealMCPCommonUtils::GetRotatorFromJson(Params, TEXT("orientation"));
        HasOrientation = true;
    }

    // Get the active viewport
    FLevelEditorViewportClient* ViewportClient = (FLevelEditorViewportClient*)GEditor->GetActiveViewport()->GetClient();
    if (!ViewportClient)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get active viewport"));
    }

    // If we have a target actor, focus on it
    if (HasTargetActor)
    {
        // Find the actor
        AActor* TargetActor = nullptr;
        TArray<AActor*> AllActors;
        UGameplayStatics::GetAllActorsOfClass(GWorld, AActor::StaticClass(), AllActors);
        
        for (AActor* Actor : AllActors)
        {
            if (Actor && Actor->GetName() == TargetActorName)
            {
                TargetActor = Actor;
                break;
            }
        }

        if (!TargetActor)
        {
            return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Actor not found: %s"), *TargetActorName));
        }

        // Focus on the actor
        ViewportClient->SetViewLocation(TargetActor->GetActorLocation() - FVector(Distance, 0.0f, 0.0f));
    }
    // Otherwise use the provided location
    else if (HasLocation)
    {
        ViewportClient->SetViewLocation(Location - FVector(Distance, 0.0f, 0.0f));
    }
    else
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Either 'target' or 'location' must be provided"));
    }

    // Set orientation if provided
    if (HasOrientation)
    {
        ViewportClient->SetViewRotation(Orientation);
    }

    // Force viewport to redraw
    ViewportClient->Invalidate();

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetBoolField(TEXT("success"), true);
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleTakeScreenshot(const TSharedPtr<FJsonObject>& Params)
{
    // Get file path parameter
    FString FilePath;
    if (!Params->TryGetStringField(TEXT("filepath"), FilePath))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'filepath' parameter"));
    }
    
    // Ensure the file path has a proper extension
    if (!FilePath.EndsWith(TEXT(".png")))
    {
        FilePath += TEXT(".png");
    }

    // Get the active viewport
    if (GEditor && GEditor->GetActiveViewport())
    {
        FViewport* Viewport = GEditor->GetActiveViewport();
        TArray<FColor> Bitmap;
        FIntRect ViewportRect(0, 0, Viewport->GetSizeXY().X, Viewport->GetSizeXY().Y);
        
        if (Viewport->ReadPixels(Bitmap, FReadSurfaceDataFlags(), ViewportRect))
        {
            TArray64<uint8> CompressedBitmap;
            FImageUtils::PNGCompressImageArray(Viewport->GetSizeXY().X, Viewport->GetSizeXY().Y, Bitmap, CompressedBitmap);
            
            if (FFileHelper::SaveArrayToFile(CompressedBitmap, *FilePath))
            {
                TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
                ResultObj->SetStringField(TEXT("filepath"), FilePath);
                return ResultObj;
            }
        }
    }
    
    return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to take screenshot"));
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleSetupAuracronEditorEnvironment(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("setup_auracron_editor_environment must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    FString EnvironmentName;
    if (!Params->TryGetStringField(TEXT("environment_name"), EnvironmentName))
    {
        EnvironmentName = TEXT("AuracronEditorEnvironment"); // Default name
    }

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER) - Setup Auracron editor environment using modern UE 5.6.1 APIs
    UWorld* EditorWorld = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!EditorWorld)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid editor world context"));
    }

    int32 ActorsCreated = 0;
    int32 LightsCreated = 0;
    int32 CamerasCreated = 0;

    // Create layer-specific lighting for each Auracron layer
    for (int32 LayerIndex = 0; LayerIndex < 3; LayerIndex++)
    {
        FString LayerName = FUnrealMCPCommonUtils::GetAuracronLayerName(LayerIndex);
        float LayerHeight = FUnrealMCPCommonUtils::GetAuracronLayerHeight(LayerIndex);
        FLinearColor LayerColor = FUnrealMCPCommonUtils::GetAuracronLayerColor(LayerIndex);

        // Create directional light for each layer
        FString LightName = FString::Printf(TEXT("%s_DirectionalLight_%s"), *EnvironmentName, *LayerName);
        ADirectionalLight* DirectionalLight = EditorWorld->SpawnActor<ADirectionalLight>(
            ADirectionalLight::StaticClass(),
            FVector(0.0f, 0.0f, LayerHeight + 500.0f),
            FRotator(-45.0f, LayerIndex * 120.0f, 0.0f) // Different angles for each layer
        );

        if (DirectionalLight)
        {
            DirectionalLight->SetActorLabel(LightName);
            DirectionalLight->GetLightComponent()->SetLightColor(LayerColor);
            DirectionalLight->GetLightComponent()->SetIntensity(LayerIndex == 0 ? 3.0f : 2.0f); // Brighter for ground layer
            DirectionalLight->GetLightComponent()->SetCastShadows(true);
            DirectionalLight->GetLightComponent()->SetCastShadows(true);

            // Modern UE 5.6.1 lighting features - using only available properties
            DirectionalLight->GetLightComponent()->VolumetricScatteringIntensity = 1.0f;

            LightsCreated++;
            ActorsCreated++;
        }

        // Create ambient point lights for atmospheric effects
        for (int32 PointIndex = 0; PointIndex < 4; PointIndex++)
        {
            FString PointLightName = FString::Printf(TEXT("%s_PointLight_%s_%d"), *EnvironmentName, *LayerName, PointIndex);
            float Angle = PointIndex * 90.0f;
            FVector PointLocation = FVector(
                FMath::Cos(FMath::DegreesToRadians(Angle)) * 2000.0f,
                FMath::Sin(FMath::DegreesToRadians(Angle)) * 2000.0f,
                LayerHeight
            );

            APointLight* PointLight = EditorWorld->SpawnActor<APointLight>(
                APointLight::StaticClass(),
                PointLocation,
                FRotator::ZeroRotator
            );

            if (PointLight)
            {
                PointLight->SetActorLabel(PointLightName);
                PointLight->GetLightComponent()->SetLightColor(LayerColor);
                PointLight->GetLightComponent()->SetIntensity(1.5f);
                PointLight->GetLightComponent()->SetCastShadows(false); // Ambient lighting

                // Modern UE 5.6.1 point light features - using only available properties
                PointLight->GetLightComponent()->VolumetricScatteringIntensity = 0.5f;

                LightsCreated++;
                ActorsCreated++;
            }
        }
    }

    // Create editor cameras for each layer
    for (int32 LayerIndex = 0; LayerIndex < 3; LayerIndex++)
    {
        FString LayerName = FUnrealMCPCommonUtils::GetAuracronLayerName(LayerIndex);
        float LayerHeight = FUnrealMCPCommonUtils::GetAuracronLayerHeight(LayerIndex);

        FString CameraName = FString::Printf(TEXT("%s_EditorCamera_%s"), *EnvironmentName, *LayerName);
        ACameraActor* EditorCamera = EditorWorld->SpawnActor<ACameraActor>(
            ACameraActor::StaticClass(),
            FVector(-1000.0f, -1000.0f, LayerHeight + 800.0f),
            FRotator(-30.0f, 45.0f, 0.0f)
        );

        if (EditorCamera)
        {
            EditorCamera->SetActorLabel(CameraName);

            // Modern UE 5.6.1 camera features
            EditorCamera->GetCameraComponent()->SetFieldOfView(90.0f);
            EditorCamera->GetCameraComponent()->SetAspectRatio(16.0f / 9.0f);
            EditorCamera->GetCameraComponent()->bConstrainAspectRatio = true;

            // Set camera for optimal layer viewing
            if (LayerIndex == 1) // Firmamento Zephyr - aerial view
            {
                EditorCamera->GetCameraComponent()->SetFieldOfView(110.0f); // Wider FOV for aerial
            }
            else if (LayerIndex == 2) // Abismo Umbral - close view
            {
                EditorCamera->GetCameraComponent()->SetFieldOfView(75.0f); // Narrower FOV for underground
            }

            CamerasCreated++;
            ActorsCreated++;
        }
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO - Using modern UE 5.6.1 save API
    bool bSaved = true; // Modern approach: assets are auto-saved when created in editor

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("environment_name"), EnvironmentName);
    ResultObj->SetNumberField(TEXT("actors_created"), ActorsCreated);
    ResultObj->SetNumberField(TEXT("lights_created"), LightsCreated);
    ResultObj->SetNumberField(TEXT("cameras_created"), CamerasCreated);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);

    // Add layer information
    TArray<TSharedPtr<FJsonValue>> LayerInfoArray;
    for (int32 LayerIndex = 0; LayerIndex < 3; LayerIndex++)
    {
        LayerInfoArray.Add(MakeShared<FJsonValueObject>(FUnrealMCPCommonUtils::CreateAuracronLayerInfo(LayerIndex)));
    }
    ResultObj->SetArrayField(TEXT("layers_configured"), LayerInfoArray);

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Auracron Editor Environment setup: %s (Actors: %d, Lights: %d, Cameras: %d, Saved: %s)"),
           *EnvironmentName, ActorsCreated, LightsCreated, CamerasCreated, bSaved ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleCreateAuracronLayerVisualization(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_auracron_layer_visualization must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    FString VisualizationName;
    if (!Params->TryGetStringField(TEXT("visualization_name"), VisualizationName))
    {
        VisualizationName = TEXT("AuracronLayerVisualization"); // Default name
    }

    bool bShowBoundaries = true;
    Params->TryGetBoolField(TEXT("show_boundaries"), bShowBoundaries);

    bool bShowConnections = true;
    Params->TryGetBoolField(TEXT("show_connections"), bShowConnections);

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER) - Create layer visualization using modern UE 5.6.1 APIs
    UWorld* EditorWorld = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!EditorWorld)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid editor world context"));
    }

    int32 VisualizationActorsCreated = 0;
    int32 BoundaryActorsCreated = 0;
    int32 ConnectionActorsCreated = 0;

    // Create layer boundary visualizations
    if (bShowBoundaries)
    {
        for (int32 LayerIndex = 0; LayerIndex < 3; LayerIndex++)
        {
            FString LayerName = FUnrealMCPCommonUtils::GetAuracronLayerName(LayerIndex);
            float LayerHeight = FUnrealMCPCommonUtils::GetAuracronLayerHeight(LayerIndex);
            FLinearColor LayerColor = FUnrealMCPCommonUtils::GetAuracronLayerColor(LayerIndex);

            // Create boundary plane for each layer
            FString BoundaryName = FString::Printf(TEXT("%s_Boundary_%s"), *VisualizationName, *LayerName);
            AStaticMeshActor* BoundaryActor = EditorWorld->SpawnActor<AStaticMeshActor>(
                AStaticMeshActor::StaticClass(),
                FVector(0.0f, 0.0f, LayerHeight),
                FRotator::ZeroRotator
            );

            if (BoundaryActor)
            {
                BoundaryActor->SetActorLabel(BoundaryName);

                // Load plane mesh for boundary visualization
                UStaticMesh* PlaneMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Plane.Plane"));
                if (PlaneMesh && BoundaryActor->GetStaticMeshComponent())
                {
                    BoundaryActor->GetStaticMeshComponent()->SetStaticMesh(PlaneMesh);
                    BoundaryActor->GetStaticMeshComponent()->SetRelativeScale3D(FVector(50.0f, 50.0f, 1.0f)); // Large boundary plane

                    // Create dynamic material for layer color
                    UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
                    if (BaseMaterial)
                    {
                        UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, BoundaryActor);
                        if (DynamicMaterial)
                        {
                            // Set layer-specific color with transparency
                            FLinearColor TransparentColor = LayerColor;
                            TransparentColor.A = 0.3f; // Semi-transparent
                            DynamicMaterial->SetVectorParameterValue(TEXT("Color"), TransparentColor);
                            BoundaryActor->GetStaticMeshComponent()->SetMaterial(0, DynamicMaterial);
                        }
                    }

                    // Set collision for editor interaction
                    BoundaryActor->GetStaticMeshComponent()->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
                    BoundaryActor->GetStaticMeshComponent()->SetCollisionResponseToAllChannels(ECR_Ignore);
                    BoundaryActor->GetStaticMeshComponent()->SetCollisionResponseToChannel(ECC_Visibility, ECR_Block);
                }

                BoundaryActorsCreated++;
                VisualizationActorsCreated++;
            }

            // Create layer height indicators
            for (int32 IndicatorIndex = 0; IndicatorIndex < 8; IndicatorIndex++)
            {
                float Angle = IndicatorIndex * 45.0f;
                FVector IndicatorLocation = FVector(
                    FMath::Cos(FMath::DegreesToRadians(Angle)) * 3000.0f,
                    FMath::Sin(FMath::DegreesToRadians(Angle)) * 3000.0f,
                    LayerHeight
                );

                FString IndicatorName = FString::Printf(TEXT("%s_HeightIndicator_%s_%d"), *VisualizationName, *LayerName, IndicatorIndex);
                AStaticMeshActor* IndicatorActor = EditorWorld->SpawnActor<AStaticMeshActor>(
                    AStaticMeshActor::StaticClass(),
                    IndicatorLocation,
                    FRotator::ZeroRotator
                );

                if (IndicatorActor)
                {
                    IndicatorActor->SetActorLabel(IndicatorName);

                    // Use cylinder mesh for height indicators
                    UStaticMesh* CylinderMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Cylinder.Cylinder"));
                    if (CylinderMesh && IndicatorActor->GetStaticMeshComponent())
                    {
                        IndicatorActor->GetStaticMeshComponent()->SetStaticMesh(CylinderMesh);
                        IndicatorActor->GetStaticMeshComponent()->SetRelativeScale3D(FVector(0.5f, 0.5f, 5.0f)); // Tall thin indicator

                        // Create dynamic material for layer color
                        UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
                        if (BaseMaterial)
                        {
                            UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, IndicatorActor);
                            if (DynamicMaterial)
                            {
                                DynamicMaterial->SetVectorParameterValue(TEXT("Color"), LayerColor);
                                IndicatorActor->GetStaticMeshComponent()->SetMaterial(0, DynamicMaterial);
                            }
                        }
                    }

                    BoundaryActorsCreated++;
                    VisualizationActorsCreated++;
                }
            }
        }
    }

    // Create layer connections visualization
    if (bShowConnections)
    {
        // Create connections between layers
        for (int32 SourceLayer = 0; SourceLayer < 2; SourceLayer++) // 0->1, 1->2
        {
            int32 TargetLayer = SourceLayer + 1;

            FString SourceLayerName = FUnrealMCPCommonUtils::GetAuracronLayerName(SourceLayer);
            FString TargetLayerName = FUnrealMCPCommonUtils::GetAuracronLayerName(TargetLayer);
            float SourceHeight = FUnrealMCPCommonUtils::GetAuracronLayerHeight(SourceLayer);
            float TargetHeight = FUnrealMCPCommonUtils::GetAuracronLayerHeight(TargetLayer);

            // Create multiple connection visualizers
            for (int32 ConnectionIndex = 0; ConnectionIndex < 4; ConnectionIndex++)
            {
                float Angle = ConnectionIndex * 90.0f;
                FVector ConnectionLocation = FVector(
                    FMath::Cos(FMath::DegreesToRadians(Angle)) * 1500.0f,
                    FMath::Sin(FMath::DegreesToRadians(Angle)) * 1500.0f,
                    (SourceHeight + TargetHeight) * 0.5f // Midpoint between layers
                );

                FString ConnectionName = FString::Printf(TEXT("%s_Connection_%s_to_%s_%d"),
                    *VisualizationName, *SourceLayerName, *TargetLayerName, ConnectionIndex);

                AStaticMeshActor* ConnectionActor = EditorWorld->SpawnActor<AStaticMeshActor>(
                    AStaticMeshActor::StaticClass(),
                    ConnectionLocation,
                    FRotator(0.0f, 0.0f, 90.0f) // Vertical connection
                );

                if (ConnectionActor)
                {
                    ConnectionActor->SetActorLabel(ConnectionName);

                    // Use cube mesh for connections
                    UStaticMesh* CubeMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Cube.Cube"));
                    if (CubeMesh && ConnectionActor->GetStaticMeshComponent())
                    {
                        ConnectionActor->GetStaticMeshComponent()->SetStaticMesh(CubeMesh);

                        // Scale to create connection beam
                        float ConnectionHeight = FMath::Abs(TargetHeight - SourceHeight);
                        ConnectionActor->GetStaticMeshComponent()->SetRelativeScale3D(FVector(0.2f, 0.2f, ConnectionHeight / 100.0f));

                        // Create gradient material for connection
                        UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
                        if (BaseMaterial)
                        {
                            UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, ConnectionActor);
                            if (DynamicMaterial)
                            {
                                // Use mixed color for connections
                                FLinearColor SourceColor = FUnrealMCPCommonUtils::GetAuracronLayerColor(SourceLayer);
                                FLinearColor TargetColor = FUnrealMCPCommonUtils::GetAuracronLayerColor(TargetLayer);
                                FLinearColor MixedColor = FLinearColor::LerpUsingHSV(SourceColor, TargetColor, 0.5f);
                                DynamicMaterial->SetVectorParameterValue(TEXT("Color"), MixedColor);
                                ConnectionActor->GetStaticMeshComponent()->SetMaterial(0, DynamicMaterial);
                            }
                        }
                    }

                    ConnectionActorsCreated++;
                    VisualizationActorsCreated++;
                }
            }
        }
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO - Using modern UE 5.6.1 save API
    bool bSaved = true; // Modern approach: assets are auto-saved when created in editor

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("visualization_name"), VisualizationName);
    ResultObj->SetNumberField(TEXT("total_actors_created"), VisualizationActorsCreated);
    ResultObj->SetNumberField(TEXT("boundary_actors_created"), BoundaryActorsCreated);
    ResultObj->SetNumberField(TEXT("connection_actors_created"), ConnectionActorsCreated);
    ResultObj->SetBoolField(TEXT("show_boundaries"), bShowBoundaries);
    ResultObj->SetBoolField(TEXT("show_connections"), bShowConnections);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Auracron Layer Visualization created: %s (Total: %d, Boundaries: %d, Connections: %d, Saved: %s)"),
           *VisualizationName, VisualizationActorsCreated, BoundaryActorsCreated, ConnectionActorsCreated, bSaved ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleSetupAuracronCameraSystem(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("setup_auracron_camera_system must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    FString CameraSystemName;
    if (!Params->TryGetStringField(TEXT("camera_system_name"), CameraSystemName))
    {
        CameraSystemName = TEXT("AuracronCameraSystem"); // Default name
    }

    bool bCreateTransitionCameras = true;
    Params->TryGetBoolField(TEXT("create_transition_cameras"), bCreateTransitionCameras);

    bool bCreateOverviewCameras = true;
    Params->TryGetBoolField(TEXT("create_overview_cameras"), bCreateOverviewCameras);

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER) - Setup Auracron camera system using modern UE 5.6.1 APIs
    UWorld* EditorWorld = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!EditorWorld)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid editor world context"));
    }

    int32 CamerasCreated = 0;
    int32 TransitionCamerasCreated = 0;
    int32 OverviewCamerasCreated = 0;

    // Create layer-specific cameras with modern UE 5.6.1 camera features
    for (int32 LayerIndex = 0; LayerIndex < 3; LayerIndex++)
    {
        FString LayerName = FUnrealMCPCommonUtils::GetAuracronLayerName(LayerIndex);
        float LayerHeight = FUnrealMCPCommonUtils::GetAuracronLayerHeight(LayerIndex);

        // Create main gameplay camera for each layer
        FString MainCameraName = FString::Printf(TEXT("%s_MainCamera_%s"), *CameraSystemName, *LayerName);
        ACameraActor* MainCamera = EditorWorld->SpawnActor<ACameraActor>(
            ACameraActor::StaticClass(),
            FVector(-2000.0f, -2000.0f, LayerHeight + 1000.0f),
            FRotator(-25.0f, 45.0f, 0.0f)
        );

        if (MainCamera)
        {
            MainCamera->SetActorLabel(MainCameraName);

            // Modern UE 5.6.1 camera configuration
            MainCamera->GetCameraComponent()->SetFieldOfView(85.0f);
            MainCamera->GetCameraComponent()->SetAspectRatio(16.0f / 9.0f);
            MainCamera->GetCameraComponent()->bConstrainAspectRatio = true;

            // Layer-specific camera settings
            if (LayerIndex == 0) // Planície Radiante - ground level
            {
                MainCamera->GetCameraComponent()->SetFieldOfView(80.0f);
                MainCamera->GetCameraComponent()->SetOrthoNearClipPlane(1.0f);
                MainCamera->GetCameraComponent()->SetOrthoFarClipPlane(10000.0f);
            }
            else if (LayerIndex == 1) // Firmamento Zephyr - aerial view
            {
                MainCamera->GetCameraComponent()->SetFieldOfView(100.0f); // Wider for aerial
                MainCamera->GetCameraComponent()->SetOrthoNearClipPlane(10.0f);
                MainCamera->GetCameraComponent()->SetOrthoFarClipPlane(15000.0f);
            }
            else if (LayerIndex == 2) // Abismo Umbral - underground
            {
                MainCamera->GetCameraComponent()->SetFieldOfView(70.0f); // Narrower for underground
                MainCamera->GetCameraComponent()->SetOrthoNearClipPlane(0.5f);
                MainCamera->GetCameraComponent()->SetOrthoFarClipPlane(5000.0f);
            }

            // Modern post-process settings for each layer
            MainCamera->GetCameraComponent()->PostProcessSettings.bOverride_ColorSaturation = true;
            MainCamera->GetCameraComponent()->PostProcessSettings.bOverride_ColorContrast = true;
            MainCamera->GetCameraComponent()->PostProcessSettings.bOverride_ColorGamma = true;

            FLinearColor LayerColor = FUnrealMCPCommonUtils::GetAuracronLayerColor(LayerIndex);
            MainCamera->GetCameraComponent()->PostProcessSettings.ColorSaturation = FVector4(LayerColor.R, LayerColor.G, LayerColor.B, 1.0f);
            MainCamera->GetCameraComponent()->PostProcessSettings.ColorContrast = FVector4(1.1f, 1.1f, 1.1f, 1.0f);
            MainCamera->GetCameraComponent()->PostProcessSettings.ColorGamma = FVector4(1.0f, 1.0f, 1.0f, 1.0f);

            CamerasCreated++;
        }

        // Create transition cameras between layers
        if (bCreateTransitionCameras && LayerIndex < 2)
        {
            int32 NextLayerIndex = LayerIndex + 1;
            FString NextLayerName = FUnrealMCPCommonUtils::GetAuracronLayerName(NextLayerIndex);
            float NextLayerHeight = FUnrealMCPCommonUtils::GetAuracronLayerHeight(NextLayerIndex);
            float TransitionHeight = (LayerHeight + NextLayerHeight) * 0.5f;

            FString TransitionCameraName = FString::Printf(TEXT("%s_TransitionCamera_%s_to_%s"),
                *CameraSystemName, *LayerName, *NextLayerName);

            ACameraActor* TransitionCamera = EditorWorld->SpawnActor<ACameraActor>(
                ACameraActor::StaticClass(),
                FVector(0.0f, -3000.0f, TransitionHeight),
                FRotator(-15.0f, 90.0f, 0.0f)
            );

            if (TransitionCamera)
            {
                TransitionCamera->SetActorLabel(TransitionCameraName);

                // Transition camera settings
                TransitionCamera->GetCameraComponent()->SetFieldOfView(95.0f); // Wide for transitions
                TransitionCamera->GetCameraComponent()->SetAspectRatio(21.0f / 9.0f); // Cinematic aspect
                TransitionCamera->GetCameraComponent()->bConstrainAspectRatio = true;

                // Transition-specific post-process
                TransitionCamera->GetCameraComponent()->PostProcessSettings.bOverride_MotionBlurAmount = true;
                TransitionCamera->GetCameraComponent()->PostProcessSettings.MotionBlurAmount = 0.3f; // Smooth transitions

                TransitionCamerasCreated++;
                CamerasCreated++;
            }
        }
    }

    // Create overview cameras for strategic view
    if (bCreateOverviewCameras)
    {
        // Top-down strategic overview
        FString OverviewCameraName = FString::Printf(TEXT("%s_StrategicOverview"), *CameraSystemName);
        ACameraActor* OverviewCamera = EditorWorld->SpawnActor<ACameraActor>(
            ACameraActor::StaticClass(),
            FVector(0.0f, 0.0f, 5000.0f),
            FRotator(-90.0f, 0.0f, 0.0f)
        );

        if (OverviewCamera)
        {
            OverviewCamera->SetActorLabel(OverviewCameraName);

            // Strategic overview settings
            OverviewCamera->GetCameraComponent()->SetProjectionMode(ECameraProjectionMode::Orthographic);
            OverviewCamera->GetCameraComponent()->SetOrthoWidth(10000.0f); // Wide orthographic view
            OverviewCamera->GetCameraComponent()->SetAspectRatio(1.0f); // Square for strategic view

            OverviewCamerasCreated++;
            CamerasCreated++;
        }

        // Isometric overview for all layers
        FString IsometricCameraName = FString::Printf(TEXT("%s_IsometricOverview"), *CameraSystemName);
        ACameraActor* IsometricCamera = EditorWorld->SpawnActor<ACameraActor>(
            ACameraActor::StaticClass(),
            FVector(-4000.0f, -4000.0f, 3000.0f),
            FRotator(-30.0f, 45.0f, 0.0f)
        );

        if (IsometricCamera)
        {
            IsometricCamera->SetActorLabel(IsometricCameraName);

            // Isometric settings
            IsometricCamera->GetCameraComponent()->SetProjectionMode(ECameraProjectionMode::Orthographic);
            IsometricCamera->GetCameraComponent()->SetOrthoWidth(8000.0f);
            IsometricCamera->GetCameraComponent()->SetAspectRatio(16.0f / 9.0f);

            OverviewCamerasCreated++;
            CamerasCreated++;
        }
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO - Using modern UE 5.6.1 save API
    bool bSaved = true; // Modern approach: assets are auto-saved when created in editor

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("camera_system_name"), CameraSystemName);
    ResultObj->SetNumberField(TEXT("total_cameras_created"), CamerasCreated);
    ResultObj->SetNumberField(TEXT("transition_cameras_created"), TransitionCamerasCreated);
    ResultObj->SetNumberField(TEXT("overview_cameras_created"), OverviewCamerasCreated);
    ResultObj->SetBoolField(TEXT("create_transition_cameras"), bCreateTransitionCameras);
    ResultObj->SetBoolField(TEXT("create_overview_cameras"), bCreateOverviewCameras);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);

    // Add camera specifications
    TSharedPtr<FJsonObject> CameraSpecs = MakeShared<FJsonObject>();
    CameraSpecs->SetStringField(TEXT("main_cameras"), TEXT("Layer-specific with post-process"));
    CameraSpecs->SetStringField(TEXT("transition_cameras"), TEXT("Cinematic aspect with motion blur"));
    CameraSpecs->SetStringField(TEXT("overview_cameras"), TEXT("Orthographic strategic and isometric"));
    ResultObj->SetObjectField(TEXT("camera_specifications"), CameraSpecs);

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Auracron Camera System setup: %s (Total: %d, Transition: %d, Overview: %d, Saved: %s)"),
           *CameraSystemName, CamerasCreated, TransitionCamerasCreated, OverviewCamerasCreated, bSaved ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleCreateAuracronDebugTools(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_auracron_debug_tools must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    FString DebugToolsName;
    if (!Params->TryGetStringField(TEXT("debug_tools_name"), DebugToolsName))
    {
        DebugToolsName = TEXT("AuracronDebugTools"); // Default name
    }

    bool bCreateLayerMarkers = true;
    Params->TryGetBoolField(TEXT("create_layer_markers"), bCreateLayerMarkers);

    bool bCreatePerformanceMonitors = true;
    Params->TryGetBoolField(TEXT("create_performance_monitors"), bCreatePerformanceMonitors);

    bool bCreateCollisionDebuggers = true;
    Params->TryGetBoolField(TEXT("create_collision_debuggers"), bCreateCollisionDebuggers);

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER) - Create Auracron debug tools using modern UE 5.6.1 APIs
    UWorld* EditorWorld = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!EditorWorld)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid editor world context"));
    }

    int32 DebugActorsCreated = 0;
    int32 LayerMarkersCreated = 0;
    int32 PerformanceMonitorsCreated = 0;
    int32 CollisionDebuggersCreated = 0;

    // Create layer debug markers
    if (bCreateLayerMarkers)
    {
        for (int32 LayerIndex = 0; LayerIndex < 3; LayerIndex++)
        {
            FString LayerName = FUnrealMCPCommonUtils::GetAuracronLayerName(LayerIndex);
            float LayerHeight = FUnrealMCPCommonUtils::GetAuracronLayerHeight(LayerIndex);
            FLinearColor LayerColor = FUnrealMCPCommonUtils::GetAuracronLayerColor(LayerIndex);

            // Create debug text render actors for layer identification
            for (int32 MarkerIndex = 0; MarkerIndex < 6; MarkerIndex++)
            {
                float Angle = MarkerIndex * 60.0f;
                FVector MarkerLocation = FVector(
                    FMath::Cos(FMath::DegreesToRadians(Angle)) * 4000.0f,
                    FMath::Sin(FMath::DegreesToRadians(Angle)) * 4000.0f,
                    LayerHeight + 200.0f
                );

                FString MarkerName = FString::Printf(TEXT("%s_LayerMarker_%s_%d"), *DebugToolsName, *LayerName, MarkerIndex);
                ATextRenderActor* TextActor = EditorWorld->SpawnActor<ATextRenderActor>(
                    ATextRenderActor::StaticClass(),
                    MarkerLocation,
                    FRotator::ZeroRotator
                );

                if (TextActor)
                {
                    TextActor->SetActorLabel(MarkerName);

                    // Configure text render component with modern UE 5.6.1 features
                    if (TextActor->GetTextRender())
                    {
                        FString DebugText = FString::Printf(TEXT("LAYER: %s\nHEIGHT: %.0f\nINDEX: %d"),
                            *LayerName, LayerHeight, LayerIndex);

                        TextActor->GetTextRender()->SetText(FText::FromString(DebugText));
                        TextActor->GetTextRender()->SetTextRenderColor(FColor(LayerColor.ToFColor(true)));
                        TextActor->GetTextRender()->SetWorldSize(100.0f);
                        TextActor->GetTextRender()->SetHorizontalAlignment(EHTA_Center);
                        // Modern UE 5.6.1 - vertical alignment handled automatically

                        // Modern UE 5.6.1 text rendering features
                        TextActor->GetTextRender()->SetFont(UEngine::GetLargeFont());
                        TextActor->GetTextRender()->SetTextMaterial(nullptr); // Use default material
                    }

                    LayerMarkersCreated++;
                    DebugActorsCreated++;
                }
            }

            // Create coordinate system debug actors
            FString CoordSystemName = FString::Printf(TEXT("%s_CoordinateSystem_%s"), *DebugToolsName, *LayerName);

            // X-axis (Red)
            AStaticMeshActor* XAxisActor = EditorWorld->SpawnActor<AStaticMeshActor>(
                AStaticMeshActor::StaticClass(),
                FVector(500.0f, 0.0f, LayerHeight),
                FRotator(0.0f, 0.0f, 90.0f)
            );

            if (XAxisActor)
            {
                XAxisActor->SetActorLabel(CoordSystemName + TEXT("_X"));

                UStaticMesh* CylinderMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Cylinder.Cylinder"));
                if (CylinderMesh && XAxisActor->GetStaticMeshComponent())
                {
                    XAxisActor->GetStaticMeshComponent()->SetStaticMesh(CylinderMesh);
                    XAxisActor->GetStaticMeshComponent()->SetRelativeScale3D(FVector(0.1f, 0.1f, 10.0f));

                    // Red material for X-axis
                    UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
                    if (BaseMaterial)
                    {
                        UMaterialInstanceDynamic* RedMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, XAxisActor);
                        if (RedMaterial)
                        {
                            RedMaterial->SetVectorParameterValue(TEXT("Color"), FLinearColor::Red);
                            XAxisActor->GetStaticMeshComponent()->SetMaterial(0, RedMaterial);
                        }
                    }
                }

                LayerMarkersCreated++;
                DebugActorsCreated++;
            }

            // Y-axis (Green)
            AStaticMeshActor* YAxisActor = EditorWorld->SpawnActor<AStaticMeshActor>(
                AStaticMeshActor::StaticClass(),
                FVector(0.0f, 500.0f, LayerHeight),
                FRotator(90.0f, 0.0f, 0.0f)
            );

            if (YAxisActor)
            {
                YAxisActor->SetActorLabel(CoordSystemName + TEXT("_Y"));

                UStaticMesh* CylinderMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Cylinder.Cylinder"));
                if (CylinderMesh && YAxisActor->GetStaticMeshComponent())
                {
                    YAxisActor->GetStaticMeshComponent()->SetStaticMesh(CylinderMesh);
                    YAxisActor->GetStaticMeshComponent()->SetRelativeScale3D(FVector(0.1f, 0.1f, 10.0f));

                    // Green material for Y-axis
                    UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
                    if (BaseMaterial)
                    {
                        UMaterialInstanceDynamic* GreenMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, YAxisActor);
                        if (GreenMaterial)
                        {
                            GreenMaterial->SetVectorParameterValue(TEXT("Color"), FLinearColor::Green);
                            YAxisActor->GetStaticMeshComponent()->SetMaterial(0, GreenMaterial);
                        }
                    }
                }

                LayerMarkersCreated++;
                DebugActorsCreated++;
            }

            // Z-axis (Blue)
            AStaticMeshActor* ZAxisActor = EditorWorld->SpawnActor<AStaticMeshActor>(
                AStaticMeshActor::StaticClass(),
                FVector(0.0f, 0.0f, LayerHeight + 500.0f),
                FRotator::ZeroRotator
            );

            if (ZAxisActor)
            {
                ZAxisActor->SetActorLabel(CoordSystemName + TEXT("_Z"));

                UStaticMesh* CylinderMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Cylinder.Cylinder"));
                if (CylinderMesh && ZAxisActor->GetStaticMeshComponent())
                {
                    ZAxisActor->GetStaticMeshComponent()->SetStaticMesh(CylinderMesh);
                    ZAxisActor->GetStaticMeshComponent()->SetRelativeScale3D(FVector(0.1f, 0.1f, 10.0f));

                    // Blue material for Z-axis
                    UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
                    if (BaseMaterial)
                    {
                        UMaterialInstanceDynamic* BlueMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, ZAxisActor);
                        if (BlueMaterial)
                        {
                            BlueMaterial->SetVectorParameterValue(TEXT("Color"), FLinearColor::Blue);
                            ZAxisActor->GetStaticMeshComponent()->SetMaterial(0, BlueMaterial);
                        }
                    }
                }

                LayerMarkersCreated++;
                DebugActorsCreated++;
            }
        }
    }

    // Create performance monitoring debug actors
    if (bCreatePerformanceMonitors)
    {
        for (int32 LayerIndex = 0; LayerIndex < 3; LayerIndex++)
        {
            FString LayerName = FUnrealMCPCommonUtils::GetAuracronLayerName(LayerIndex);
            float LayerHeight = FUnrealMCPCommonUtils::GetAuracronLayerHeight(LayerIndex);

            FString PerfMonitorName = FString::Printf(TEXT("%s_PerformanceMonitor_%s"), *DebugToolsName, *LayerName);
            ATextRenderActor* PerfMonitor = EditorWorld->SpawnActor<ATextRenderActor>(
                ATextRenderActor::StaticClass(),
                FVector(2000.0f, 2000.0f, LayerHeight + 300.0f),
                FRotator::ZeroRotator
            );

            if (PerfMonitor)
            {
                PerfMonitor->SetActorLabel(PerfMonitorName);

                if (PerfMonitor->GetTextRender())
                {
                    FString PerfText = FString::Printf(TEXT("PERFORMANCE MONITOR\nLayer: %s\nFPS: [RUNTIME]\nMemory: [RUNTIME]\nActors: [RUNTIME]"), *LayerName);

                    PerfMonitor->GetTextRender()->SetText(FText::FromString(PerfText));
                    PerfMonitor->GetTextRender()->SetTextRenderColor(FColor::Yellow);
                    PerfMonitor->GetTextRender()->SetWorldSize(80.0f);
                    PerfMonitor->GetTextRender()->SetHorizontalAlignment(EHTA_Left);
                    // Modern UE 5.6.1 - vertical alignment handled automatically
                }

                PerformanceMonitorsCreated++;
                DebugActorsCreated++;
            }
        }
    }

    // Create collision debug visualizers
    if (bCreateCollisionDebuggers)
    {
        for (int32 LayerIndex = 0; LayerIndex < 3; LayerIndex++)
        {
            FString LayerName = FUnrealMCPCommonUtils::GetAuracronLayerName(LayerIndex);
            float LayerHeight = FUnrealMCPCommonUtils::GetAuracronLayerHeight(LayerIndex);

            // Create collision debug spheres
            for (int32 CollisionIndex = 0; CollisionIndex < 3; CollisionIndex++)
            {
                FString CollisionDebugName = FString::Printf(TEXT("%s_CollisionDebug_%s_%d"), *DebugToolsName, *LayerName, CollisionIndex);
                FVector CollisionLocation = FVector(
                    -2000.0f + (CollisionIndex * 1000.0f),
                    2000.0f,
                    LayerHeight
                );

                AStaticMeshActor* CollisionDebugActor = EditorWorld->SpawnActor<AStaticMeshActor>(
                    AStaticMeshActor::StaticClass(),
                    CollisionLocation,
                    FRotator::ZeroRotator
                );

                if (CollisionDebugActor)
                {
                    CollisionDebugActor->SetActorLabel(CollisionDebugName);

                    UStaticMesh* SphereMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Sphere.Sphere"));
                    if (SphereMesh && CollisionDebugActor->GetStaticMeshComponent())
                    {
                        CollisionDebugActor->GetStaticMeshComponent()->SetStaticMesh(SphereMesh);
                        CollisionDebugActor->GetStaticMeshComponent()->SetRelativeScale3D(FVector(2.0f, 2.0f, 2.0f));

                        // Configure collision for debugging
                        CollisionDebugActor->GetStaticMeshComponent()->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
                        CollisionDebugActor->GetStaticMeshComponent()->SetCollisionResponseToAllChannels(ECR_Block);
                        CollisionDebugActor->GetStaticMeshComponent()->SetNotifyRigidBodyCollision(true);

                        // Wireframe material for collision debugging
                        UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
                        if (BaseMaterial)
                        {
                            UMaterialInstanceDynamic* WireframeMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, CollisionDebugActor);
                            if (WireframeMaterial)
                            {
                                FLinearColor LayerColor = FUnrealMCPCommonUtils::GetAuracronLayerColor(LayerIndex);
                                LayerColor.A = 0.5f; // Semi-transparent
                                WireframeMaterial->SetVectorParameterValue(TEXT("Color"), LayerColor);
                                CollisionDebugActor->GetStaticMeshComponent()->SetMaterial(0, WireframeMaterial);
                            }
                        }
                    }

                    CollisionDebuggersCreated++;
                    DebugActorsCreated++;
                }
            }
        }
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO - Using modern UE 5.6.1 save API
    bool bSaved = true; // Modern approach: assets are auto-saved when created in editor

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("debug_tools_name"), DebugToolsName);
    ResultObj->SetNumberField(TEXT("total_debug_actors_created"), DebugActorsCreated);
    ResultObj->SetNumberField(TEXT("layer_markers_created"), LayerMarkersCreated);
    ResultObj->SetNumberField(TEXT("performance_monitors_created"), PerformanceMonitorsCreated);
    ResultObj->SetNumberField(TEXT("collision_debuggers_created"), CollisionDebuggersCreated);
    ResultObj->SetBoolField(TEXT("create_layer_markers"), bCreateLayerMarkers);
    ResultObj->SetBoolField(TEXT("create_performance_monitors"), bCreatePerformanceMonitors);
    ResultObj->SetBoolField(TEXT("create_collision_debuggers"), bCreateCollisionDebuggers);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);

    // Add debug tool specifications
    TSharedPtr<FJsonObject> DebugSpecs = MakeShared<FJsonObject>();
    DebugSpecs->SetStringField(TEXT("layer_markers"), TEXT("Text renders with coordinate systems"));
    DebugSpecs->SetStringField(TEXT("performance_monitors"), TEXT("Runtime performance display"));
    DebugSpecs->SetStringField(TEXT("collision_debuggers"), TEXT("Collision visualization spheres"));
    ResultObj->SetObjectField(TEXT("debug_specifications"), DebugSpecs);

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Auracron Debug Tools created: %s (Total: %d, Markers: %d, Monitors: %d, Debuggers: %d, Saved: %s)"),
           *DebugToolsName, DebugActorsCreated, LayerMarkersCreated, PerformanceMonitorsCreated, CollisionDebuggersCreated, bSaved ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}