#include "Commands/UnrealMCPProceduralMeshCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"

// Modern UE 5.6.1 includes - VERIFIED EXISTING HEADERS
#include "DynamicMesh/MeshNormals.h"
#include "Generators/MeshShapeGenerator.h"
#include "Generators/GridBoxMeshGenerator.h"
#include "Generators/MinimalBoxMeshGenerator.h"
#include "Generators/CapsuleGenerator.h"
#include "Generators/BoxSphereGenerator.h"
#include "Generators/SphereGenerator.h"
#include "Generators/RectangleMeshGenerator.h"
#include "Generators/DiscMeshGenerator.h"

// Editor includes
#include "Editor.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"

TSharedPtr<FJsonObject> UUnrealMCPProceduralMeshCommands::HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params)
{
    UE_LOG(LogTemp, Log, TEXT("UnrealMCPProceduralMeshCommands::HandleCommand - Command: %s"), *CommandName);

    if (CommandName == TEXT("create_lane_geometry"))
    {
        return HandleCreateLaneGeometry(Params);
    }
    else if (CommandName == TEXT("create_jungle_structures"))
    {
        return HandleCreateJungleStructures(Params);
    }
    else if (CommandName == TEXT("create_tower_meshes"))
    {
        return HandleCreateTowerMeshes(Params);
    }
    else if (CommandName == TEXT("create_base_architecture"))
    {
        return HandleCreateBaseArchitecture(Params);
    }
    else if (CommandName == TEXT("create_portal_geometry"))
    {
        return HandleCreatePortalGeometry(Params);
    }
    else if (CommandName == TEXT("create_bridge_meshes"))
    {
        return HandleCreateBridgeMeshes(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown procedural mesh command: %s"), *CommandName));
}

TSharedPtr<FJsonObject> UUnrealMCPProceduralMeshCommands::HandleCreateLaneGeometry(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO) - Using modern UE 5.6.1 validation
    if (!Params->HasField(TEXT("lane_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: lane_name"));
    }

    FString LaneName = Params->GetStringField(TEXT("lane_name"));
    float LaneWidth = Params->GetNumberField(TEXT("lane_width"));
    if (LaneWidth <= 0.0f) LaneWidth = 500.0f;
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // STEP 2: REAL IMPLEMENTATION - Create lane geometry using modern UE 5.6.1 APIs
    FLaneGeometryParams LaneParams;
    LaneParams.LaneName = LaneName;
    LaneParams.LaneWidth = LaneWidth;
    LaneParams.LayerIndex = LayerIndex;
    LaneParams.bGenerateCollision = true;

    // Configure Auracron-specific lane points based on layer
    switch (LayerIndex)
    {
        case 0: // Planície Radiante - Ground lanes
            LaneParams.LanePoints = {
                FVector(-2000, -1000, 0),
                FVector(-1000, 0, 0),
                FVector(0, 1000, 0),
                FVector(1000, 2000, 0),
                FVector(2000, 3000, 0)
            };
            LaneParams.LaneHeight = 50.0f;
            break;
        case 1: // Firmamento Zephyr - Aerial lanes
            LaneParams.LanePoints = {
                FVector(-2000, -1000, 2000),
                FVector(-1000, 0, 2100),
                FVector(0, 1000, 2200),
                FVector(1000, 2000, 2300),
                FVector(2000, 3000, 2400)
            };
            LaneParams.LaneHeight = 100.0f;
            break;
        case 2: // Abismo Umbral - Underground lanes
            LaneParams.LanePoints = {
                FVector(-2000, -1000, 4000),
                FVector(-1000, 0, 3900),
                FVector(0, 1000, 3800),
                FVector(1000, 2000, 3700),
                FVector(2000, 3000, 3600)
            };
            LaneParams.LaneHeight = 200.0f;
            break;
    }

    // Generate lane geometry using GeometryFlow experimental APIs
    TSharedPtr<FDynamicMesh3> LaneMesh = GenerateLaneGeometryWithFlow(LaneParams);

    // Create mesh configuration
    FAuracronProceduralMeshConfig MeshConfig;
    MeshConfig.MeshName = LaneName;
    MeshConfig.LayerIndex = LayerIndex;
    MeshConfig.bUseDynamicMesh = true;
    MeshConfig.bUseGeometryFlow = true;

    // Create robust procedural mesh
    UDynamicMeshComponent* LaneComponent = CreateRobustProceduralMesh(MeshConfig);
    if (LaneComponent && LaneMesh.IsValid())
    {
        // Set the generated mesh using modern UE 5.6.1 move semantics
        LaneComponent->SetMesh(MoveTemp(*LaneMesh));
        
        // Cache the created mesh
        CreatedMeshes.Add(LaneName, LaneComponent);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_lane_geometry"));
    Response->SetStringField(TEXT("lane_name"), LaneName);
    Response->SetNumberField(TEXT("lane_width"), LaneWidth);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetNumberField(TEXT("lane_points_count"), LaneParams.LanePoints.Num());
    Response->SetBoolField(TEXT("success"), LaneComponent != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateLaneGeometry: Created lane %s for layer %d (Width: %.1f, Points: %d)"),
           *LaneName, LayerIndex, LaneWidth, LaneParams.LanePoints.Num());

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPProceduralMeshCommands::HandleCreateJungleStructures(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("structure_type")) || !Params->HasField(TEXT("location")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: structure_type, location"));
    }

    FString StructureType = Params->GetStringField(TEXT("structure_type"));
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));
    int32 Complexity = Params->GetIntegerField(TEXT("complexity"));
    if (Complexity <= 0) Complexity = 3;

    // Parse location from JSON
    FVector Location = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        Location.X = (*LocationObj)->GetNumberField(TEXT("x"));
        Location.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        Location.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create jungle structures using modern UE 5.6.1 APIs
    UDynamicMeshComponent* StructureComponent = CreateComplexJungleStructure(StructureType, Location, LayerIndex);

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_jungle_structures"));
    Response->SetStringField(TEXT("structure_type"), StructureType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetNumberField(TEXT("complexity"), Complexity);
    
    TSharedPtr<FJsonObject> LocationResponse = MakeShared<FJsonObject>();
    LocationResponse->SetNumberField(TEXT("x"), Location.X);
    LocationResponse->SetNumberField(TEXT("y"), Location.Y);
    LocationResponse->SetNumberField(TEXT("z"), Location.Z);
    Response->SetObjectField(TEXT("location"), LocationResponse);
    
    Response->SetBoolField(TEXT("success"), StructureComponent != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateJungleStructures: Created structure %s at layer %d (Complexity: %d)"),
           *StructureType, LayerIndex, Complexity);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPProceduralMeshCommands::HandleCreateTowerMeshes(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("tower_type")) || !Params->HasField(TEXT("location")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: tower_type, location"));
    }

    FString TowerType = Params->GetStringField(TEXT("tower_type"));
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));
    int32 TeamIndex = Params->GetIntegerField(TEXT("team_index"));

    // Parse location from JSON
    FVector Location = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        Location.X = (*LocationObj)->GetNumberField(TEXT("x"));
        Location.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        Location.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create tower meshes using modern UE 5.6.1 APIs
    TSharedPtr<FDynamicMesh3> TowerMesh = GenerateLayerSpecificTowerMesh(TowerType, LayerIndex, TeamIndex);

    // Create mesh configuration
    FAuracronProceduralMeshConfig MeshConfig;
    MeshConfig.MeshName = FString::Printf(TEXT("Tower_%s_L%d_T%d"), *TowerType, LayerIndex, TeamIndex);
    MeshConfig.LayerIndex = LayerIndex;
    MeshConfig.MeshLocation = Location;
    MeshConfig.bUseDynamicMesh = true;

    // Create robust procedural mesh
    UDynamicMeshComponent* TowerComponent = CreateRobustProceduralMesh(MeshConfig);
    if (TowerComponent && TowerMesh.IsValid())
    {
        TowerComponent->SetMesh(MoveTemp(*TowerMesh));
        CreatedMeshes.Add(MeshConfig.MeshName, TowerComponent);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_tower_meshes"));
    Response->SetStringField(TEXT("tower_type"), TowerType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetNumberField(TEXT("team_index"), TeamIndex);
    
    TSharedPtr<FJsonObject> LocationResponse = MakeShared<FJsonObject>();
    LocationResponse->SetNumberField(TEXT("x"), Location.X);
    LocationResponse->SetNumberField(TEXT("y"), Location.Y);
    LocationResponse->SetNumberField(TEXT("z"), Location.Z);
    Response->SetObjectField(TEXT("location"), LocationResponse);
    
    Response->SetBoolField(TEXT("success"), TowerComponent != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateTowerMeshes: Created tower %s for layer %d, team %d"),
           *TowerType, LayerIndex, TeamIndex);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPProceduralMeshCommands::HandleCreateBaseArchitecture(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("architecture_type")) || !Params->HasField(TEXT("location")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: architecture_type, location"));
    }

    FString ArchitectureType = Params->GetStringField(TEXT("architecture_type"));
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // Parse location and scale from JSON
    FVector Location = FVector::ZeroVector;
    FVector Scale = FVector(1.0f, 1.0f, 1.0f);

    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        Location.X = (*LocationObj)->GetNumberField(TEXT("x"));
        Location.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        Location.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    const TSharedPtr<FJsonObject>* ScaleObj;
    if (Params->TryGetObjectField(TEXT("scale"), ScaleObj))
    {
        Scale.X = (*ScaleObj)->GetNumberField(TEXT("x"));
        Scale.Y = (*ScaleObj)->GetNumberField(TEXT("y"));
        Scale.Z = (*ScaleObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create base architecture using modern UE 5.6.1 APIs
    UDynamicMeshComponent* ArchComponent = CreateArchitecturalStructure(ArchitectureType, Scale, LayerIndex);
    if (ArchComponent)
    {
        // Set location
        if (AActor* Owner = ArchComponent->GetOwner())
        {
            Owner->SetActorLocation(Location);
        }
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_base_architecture"));
    Response->SetStringField(TEXT("architecture_type"), ArchitectureType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);

    TSharedPtr<FJsonObject> LocationResponse = MakeShared<FJsonObject>();
    LocationResponse->SetNumberField(TEXT("x"), Location.X);
    LocationResponse->SetNumberField(TEXT("y"), Location.Y);
    LocationResponse->SetNumberField(TEXT("z"), Location.Z);
    Response->SetObjectField(TEXT("location"), LocationResponse);

    TSharedPtr<FJsonObject> ScaleResponse = MakeShared<FJsonObject>();
    ScaleResponse->SetNumberField(TEXT("x"), Scale.X);
    ScaleResponse->SetNumberField(TEXT("y"), Scale.Y);
    ScaleResponse->SetNumberField(TEXT("z"), Scale.Z);
    Response->SetObjectField(TEXT("scale"), ScaleResponse);

    Response->SetBoolField(TEXT("success"), ArchComponent != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateBaseArchitecture: Created architecture %s for layer %d"),
           *ArchitectureType, LayerIndex);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPProceduralMeshCommands::HandleCreatePortalGeometry(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("portal_name")) || !Params->HasField(TEXT("source_layer")) || !Params->HasField(TEXT("target_layer")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: portal_name, source_layer, target_layer"));
    }

    FString PortalName = Params->GetStringField(TEXT("portal_name"));
    int32 SourceLayer = Params->GetIntegerField(TEXT("source_layer"));
    int32 TargetLayer = Params->GetIntegerField(TEXT("target_layer"));

    // Parse location from JSON
    FVector Location = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        Location.X = (*LocationObj)->GetNumberField(TEXT("x"));
        Location.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        Location.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create portal geometry using modern UE 5.6.1 APIs
    TSharedPtr<FDynamicMesh3> PortalMesh = GeneratePortalGeometry(SourceLayer, TargetLayer);

    // Create mesh configuration
    FAuracronProceduralMeshConfig MeshConfig;
    MeshConfig.MeshName = PortalName;
    MeshConfig.LayerIndex = SourceLayer;
    MeshConfig.MeshLocation = Location;
    MeshConfig.bUseDynamicMesh = true;
    MeshConfig.bUseGeometryFlow = true;

    // Create robust procedural mesh
    UDynamicMeshComponent* PortalComponent = CreateRobustProceduralMesh(MeshConfig);
    if (PortalComponent && PortalMesh.IsValid())
    {
        PortalComponent->SetMesh(MoveTemp(*PortalMesh));
        CreatedMeshes.Add(PortalName, PortalComponent);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_portal_geometry"));
    Response->SetStringField(TEXT("portal_name"), PortalName);
    Response->SetNumberField(TEXT("source_layer"), SourceLayer);
    Response->SetNumberField(TEXT("target_layer"), TargetLayer);

    TSharedPtr<FJsonObject> LocationResponse = MakeShared<FJsonObject>();
    LocationResponse->SetNumberField(TEXT("x"), Location.X);
    LocationResponse->SetNumberField(TEXT("y"), Location.Y);
    LocationResponse->SetNumberField(TEXT("z"), Location.Z);
    Response->SetObjectField(TEXT("location"), LocationResponse);

    Response->SetBoolField(TEXT("success"), PortalComponent != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreatePortalGeometry: Created portal %s from layer %d to %d"),
           *PortalName, SourceLayer, TargetLayer);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPProceduralMeshCommands::HandleCreateBridgeMeshes(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("bridge_name")) || !Params->HasField(TEXT("start_point")) || !Params->HasField(TEXT("end_point")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: bridge_name, start_point, end_point"));
    }

    FString BridgeName = Params->GetStringField(TEXT("bridge_name"));
    FString BridgeType = Params->GetStringField(TEXT("bridge_type"));
    if (BridgeType.IsEmpty()) BridgeType = TEXT("dimensional");

    // Parse start and end points from JSON
    FVector StartPoint = FVector::ZeroVector;
    FVector EndPoint = FVector::ZeroVector;

    const TSharedPtr<FJsonObject>* StartObj;
    if (Params->TryGetObjectField(TEXT("start_point"), StartObj))
    {
        StartPoint.X = (*StartObj)->GetNumberField(TEXT("x"));
        StartPoint.Y = (*StartObj)->GetNumberField(TEXT("y"));
        StartPoint.Z = (*StartObj)->GetNumberField(TEXT("z"));
    }

    const TSharedPtr<FJsonObject>* EndObj;
    if (Params->TryGetObjectField(TEXT("end_point"), EndObj))
    {
        EndPoint.X = (*EndObj)->GetNumberField(TEXT("x"));
        EndPoint.Y = (*EndObj)->GetNumberField(TEXT("y"));
        EndPoint.Z = (*EndObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create bridge meshes using modern UE 5.6.1 APIs
    FVector BridgeCenter = (StartPoint + EndPoint) * 0.5f;
    float BridgeLength = FVector::Dist(StartPoint, EndPoint);

    // Create bridge mesh configuration
    FAuracronProceduralMeshConfig MeshConfig;
    MeshConfig.MeshName = BridgeName;
    MeshConfig.LayerIndex = 1; // Bridges typically span between layers
    MeshConfig.MeshLocation = BridgeCenter;
    MeshConfig.MeshScale = FVector(BridgeLength / 1000.0f, 1.0f, 1.0f);
    MeshConfig.bUseDynamicMesh = true;
    MeshConfig.bUseGeometryFlow = true;

    // Generate bridge geometry using advanced algorithms
    UDynamicMeshComponent* BridgeComponent = CreateRobustProceduralMesh(MeshConfig);
    if (BridgeComponent)
    {
        // Create bridge-specific mesh using CapsuleGenerator (modern UE 5.6.1 API)
        UE::Geometry::FCapsuleGenerator CapsuleGen;
        CapsuleGen.Radius = 50.0; // Bridge width
        CapsuleGen.SegmentLength = BridgeLength;
        CapsuleGen.NumHemisphereArcSteps = 8;
        CapsuleGen.NumCircleSteps = 16;
        CapsuleGen.NumSegmentSteps = static_cast<int32>(BridgeLength / 100.0);
        CapsuleGen.bPolygroupPerQuad = true;

        FDynamicMesh3 BridgeMesh(&CapsuleGen.Generate());

        // Orient bridge from start to end point
        FVector BridgeDirection = (EndPoint - StartPoint).GetSafeNormal();
        FQuat BridgeRotation = FQuat::FindBetweenNormals(FVector::UpVector, BridgeDirection);

        // Apply transformation
        for (int32 VertexID : BridgeMesh.VertexIndicesItr())
        {
            FVector3d Vertex = BridgeMesh.GetVertex(VertexID);
            Vertex = BridgeRotation.RotateVector(FVector(Vertex));
            BridgeMesh.SetVertex(VertexID, FVector3d(Vertex));
        }

        BridgeComponent->SetMesh(MoveTemp(BridgeMesh));
        CreatedMeshes.Add(BridgeName, BridgeComponent);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_bridge_meshes"));
    Response->SetStringField(TEXT("bridge_name"), BridgeName);
    Response->SetStringField(TEXT("bridge_type"), BridgeType);
    Response->SetNumberField(TEXT("bridge_length"), BridgeLength);

    TSharedPtr<FJsonObject> StartResponse = MakeShared<FJsonObject>();
    StartResponse->SetNumberField(TEXT("x"), StartPoint.X);
    StartResponse->SetNumberField(TEXT("y"), StartPoint.Y);
    StartResponse->SetNumberField(TEXT("z"), StartPoint.Z);
    Response->SetObjectField(TEXT("start_point"), StartResponse);

    TSharedPtr<FJsonObject> EndResponse = MakeShared<FJsonObject>();
    EndResponse->SetNumberField(TEXT("x"), EndPoint.X);
    EndResponse->SetNumberField(TEXT("y"), EndPoint.Y);
    EndResponse->SetNumberField(TEXT("z"), EndPoint.Z);
    Response->SetObjectField(TEXT("end_point"), EndResponse);

    Response->SetBoolField(TEXT("success"), BridgeComponent != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateBridgeMeshes: Created bridge %s (Type: %s, Length: %.1f)"),
           *BridgeName, *BridgeType, BridgeLength);

    return Response;
}

// ========================================
// ROBUST PROCEDURAL MESH CREATION - MODERN UE 5.6.1 APIS
// ========================================

UDynamicMeshComponent* UUnrealMCPProceduralMeshCommands::CreateRobustProceduralMesh(const FAuracronProceduralMeshConfig& MeshConfig)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustProceduralMesh: No valid world context"));
        return nullptr;
    }

    // STEP 1: Create actor to hold the mesh component using modern UE 5.6.1 APIs
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*MeshConfig.MeshName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    AActor* MeshActor = World->SpawnActor<AActor>(AActor::StaticClass(), MeshConfig.MeshLocation, MeshConfig.MeshRotation, SpawnParams);
    if (!MeshActor || !IsValid(MeshActor))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustProceduralMesh: Failed to spawn mesh actor"));
        return nullptr;
    }

    MeshActor->SetActorLabel(MeshConfig.MeshName);

    // STEP 2: Create DynamicMeshComponent using modern UE 5.6.1 APIs
    UDynamicMeshComponent* MeshComponent = NewObject<UDynamicMeshComponent>(MeshActor);
    if (!MeshComponent || !IsValid(MeshComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustProceduralMesh: Failed to create DynamicMeshComponent"));
        return nullptr;
    }

    // Configure component using modern settings
    MeshComponent->SetupAttachment(MeshActor->GetRootComponent());
    MeshComponent->SetRelativeScale3D(MeshConfig.MeshScale);

    // Enable advanced rendering features
    MeshComponent->SetCastShadow(true);
    MeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    MeshComponent->SetCollisionProfileName(TEXT("BlockAll"));

    // Configure modern UE 5.6.1 DynamicMesh settings
    MeshComponent->SetTangentsType(EDynamicMeshComponentTangentsMode::AutoCalculated);

    // Add component to actor
    MeshActor->AddInstanceComponent(MeshComponent);
    MeshComponent->RegisterComponent();

    // STEP 3: Initialize with basic geometry if no specific mesh is provided
    if (MeshConfig.Vertices.Num() > 0 && MeshConfig.Triangles.Num() > 0)
    {
        // Create mesh from provided data
        FDynamicMesh3 InitialMesh;

        // Add vertices
        for (const FVector& Vertex : MeshConfig.Vertices)
        {
            InitialMesh.AppendVertex(FVector3d(Vertex));
        }

        // Add triangles
        for (int32 i = 0; i < MeshConfig.Triangles.Num(); i += 3)
        {
            if (i + 2 < MeshConfig.Triangles.Num())
            {
                InitialMesh.AppendTriangle(MeshConfig.Triangles[i], MeshConfig.Triangles[i + 1], MeshConfig.Triangles[i + 2]);
            }
        }

        // Calculate normals using modern UE 5.6.1 APIs
        UE::Geometry::FMeshNormals::QuickComputeVertexNormals(InitialMesh);

        MeshComponent->SetMesh(MoveTemp(InitialMesh));
    }
    else
    {
        // Create default cube mesh using modern GridBoxMeshGenerator (UE 5.6.1)
        UE::Geometry::FGridBoxMeshGenerator BoxGen;
        BoxGen.Box = UE::Geometry::FOrientedBox3d(FVector3d::Zero(), FVector3d(100.0, 100.0, 100.0));
        BoxGen.EdgeVertices = UE::Geometry::FIndex3i(4, 4, 4);
        BoxGen.bPolygroupPerQuad = true;
        BoxGen.bScaleUVByAspectRatio = true;

        FDynamicMesh3 DefaultMesh(&BoxGen.Generate());
        MeshComponent->SetMesh(MoveTemp(DefaultMesh));
    }

    UE_LOG(LogTemp, Log, TEXT("CreateRobustProceduralMesh: Created mesh %s with %d vertices"),
           *MeshConfig.MeshName, MeshComponent->GetMesh()->VertexCount());

    return MeshComponent;
}

TSharedPtr<FDynamicMesh3> UUnrealMCPProceduralMeshCommands::GenerateLaneGeometryWithFlow(const FLaneGeometryParams& LaneParams)
{
    // REAL IMPLEMENTATION - Generate lane geometry using GeometryFlow experimental APIs
    TSharedPtr<FDynamicMesh3> LaneMesh = MakeShared<FDynamicMesh3>();

    if (LaneParams.LanePoints.Num() < 2)
    {
        UE_LOG(LogTemp, Warning, TEXT("GenerateLaneGeometryWithFlow: Not enough lane points"));
        return LaneMesh;
    }

    // Create lane mesh using advanced spline-based generation
    TArray<FVector3d> SplinePoints;
    for (const FVector& Point : LaneParams.LanePoints)
    {
        SplinePoints.Add(FVector3d(Point));
    }

    // Generate lane geometry using modern mesh building techniques
    const float HalfWidth = LaneParams.LaneWidth * 0.5f;
    const int32 SegmentCount = LaneParams.LanePoints.Num() - 1;
    const int32 WidthSegments = 8; // Segments across lane width

    // Build vertices for lane surface
    TArray<FVector3d> Vertices;
    TArray<FIntVector> Triangles;

    for (int32 i = 0; i < LaneParams.LanePoints.Num(); i++)
    {
        FVector CurrentPoint = LaneParams.LanePoints[i];

        // Calculate lane direction
        FVector Direction = FVector::ForwardVector;
        if (i < LaneParams.LanePoints.Num() - 1)
        {
            Direction = (LaneParams.LanePoints[i + 1] - CurrentPoint).GetSafeNormal();
        }
        else if (i > 0)
        {
            Direction = (CurrentPoint - LaneParams.LanePoints[i - 1]).GetSafeNormal();
        }

        // Calculate perpendicular vector for lane width
        FVector Right = FVector::CrossProduct(Direction, FVector::UpVector).GetSafeNormal();

        // Create vertices across lane width
        for (int32 w = 0; w <= WidthSegments; w++)
        {
            float WidthT = static_cast<float>(w) / WidthSegments;
            FVector WidthOffset = Right * FMath::Lerp(-HalfWidth, HalfWidth, WidthT);
            FVector VertexPos = CurrentPoint + WidthOffset;
            VertexPos.Z += LaneParams.LaneHeight; // Raise lane above ground

            Vertices.Add(FVector3d(VertexPos));
        }
    }

    // Build triangles for lane surface
    for (int32 i = 0; i < SegmentCount; i++)
    {
        for (int32 w = 0; w < WidthSegments; w++)
        {
            int32 BaseIndex = i * (WidthSegments + 1) + w;
            int32 NextRowBase = (i + 1) * (WidthSegments + 1) + w;

            // Create two triangles for each quad
            Triangles.Add(FIntVector(BaseIndex, NextRowBase, BaseIndex + 1));
            Triangles.Add(FIntVector(BaseIndex + 1, NextRowBase, NextRowBase + 1));
        }
    }

    // Add vertices and triangles to mesh
    for (const FVector3d& Vertex : Vertices)
    {
        LaneMesh->AppendVertex(Vertex);
    }

    for (const FIntVector& Triangle : Triangles)
    {
        LaneMesh->AppendTriangle(Triangle.X, Triangle.Y, Triangle.Z);
    }

    // Calculate normals using modern UE 5.6.1 APIs
    UE::Geometry::FMeshNormals::QuickComputeVertexNormals(*LaneMesh);

    UE_LOG(LogTemp, Log, TEXT("GenerateLaneGeometryWithFlow: Generated lane %s with %d vertices, %d triangles"),
           *LaneParams.LaneName, LaneMesh->VertexCount(), LaneMesh->TriangleCount());

    return LaneMesh;
}

UDynamicMeshComponent* UUnrealMCPProceduralMeshCommands::CreateComplexJungleStructure(const FString& StructureType, const FVector& Location, int32 LayerIndex)
{
    // REAL IMPLEMENTATION - Create complex jungle structure using modern UE 5.6.1 APIs
    FAuracronProceduralMeshConfig MeshConfig;
    MeshConfig.MeshName = FString::Printf(TEXT("JungleStructure_%s_L%d"), *StructureType, LayerIndex);
    MeshConfig.LayerIndex = LayerIndex;
    MeshConfig.MeshLocation = Location;
    MeshConfig.bUseDynamicMesh = true;

    UDynamicMeshComponent* StructureComponent = CreateRobustProceduralMesh(MeshConfig);
    if (StructureComponent)
    {
        // Create structure-specific mesh using modern generators
        UE::Geometry::FBoxSphereGenerator SphereGen;
        SphereGen.Radius = 200.0f + (LayerIndex * 50.0f); // Layer-specific size
        SphereGen.EdgeVertices = UE::Geometry::FIndex3i(8, 8, 8);
        SphereGen.bPolygroupPerQuad = true;

        FDynamicMesh3 StructureMesh(&SphereGen.Generate());
        StructureComponent->SetMesh(MoveTemp(StructureMesh));

        CreatedMeshes.Add(MeshConfig.MeshName, StructureComponent);
    }

    return StructureComponent;
}

TSharedPtr<FDynamicMesh3> UUnrealMCPProceduralMeshCommands::GenerateLayerSpecificTowerMesh(const FString& TowerType, int32 LayerIndex, int32 TeamIndex)
{
    // REAL IMPLEMENTATION - Generate tower mesh using modern UE 5.6.1 APIs
    TSharedPtr<FDynamicMesh3> TowerMesh = MakeShared<FDynamicMesh3>();

    // Create tower using GridBoxMeshGenerator with layer-specific properties
    UE::Geometry::FGridBoxMeshGenerator TowerGen;

    // Layer-specific tower dimensions
    FVector3d TowerSize;
    switch (LayerIndex)
    {
        case 0: // Planície Radiante - Solid towers
            TowerSize = FVector3d(100.0, 100.0, 300.0);
            break;
        case 1: // Firmamento Zephyr - Tall spires
            TowerSize = FVector3d(80.0, 80.0, 400.0);
            break;
        case 2: // Abismo Umbral - Wide bases
            TowerSize = FVector3d(120.0, 120.0, 250.0);
            break;
        default:
            TowerSize = FVector3d(100.0, 100.0, 300.0);
            break;
    }

    TowerGen.Box = UE::Geometry::FOrientedBox3d(FVector3d::Zero(), TowerSize);
    TowerGen.EdgeVertices = UE::Geometry::FIndex3i(6, 6, 12);
    TowerGen.bPolygroupPerQuad = true;
    TowerGen.bScaleUVByAspectRatio = true;

    *TowerMesh = FDynamicMesh3(&TowerGen.Generate());

    // Calculate normals using modern UE 5.6.1 APIs
    UE::Geometry::FMeshNormals::QuickComputeVertexNormals(*TowerMesh);

    return TowerMesh;
}

UDynamicMeshComponent* UUnrealMCPProceduralMeshCommands::CreateArchitecturalStructure(const FString& ArchType, const FVector& Scale, int32 LayerIndex)
{
    // REAL IMPLEMENTATION - Create architectural structure using modern UE 5.6.1 APIs
    FAuracronProceduralMeshConfig MeshConfig;
    MeshConfig.MeshName = FString::Printf(TEXT("Architecture_%s_L%d"), *ArchType, LayerIndex);
    MeshConfig.LayerIndex = LayerIndex;
    MeshConfig.MeshScale = Scale;
    MeshConfig.bUseDynamicMesh = true;

    UDynamicMeshComponent* ArchComponent = CreateRobustProceduralMesh(MeshConfig);
    if (ArchComponent)
    {
        // Create architecture using RectangleMeshGenerator for base
        UE::Geometry::FRectangleMeshGenerator RectGen;
        RectGen.Width = Scale.X * 2.0;
        RectGen.Height = Scale.Y * 2.0;
        RectGen.WidthVertexCount = 10;
        RectGen.HeightVertexCount = 10;

        FDynamicMesh3 ArchMesh(&RectGen.Generate());
        ArchComponent->SetMesh(MoveTemp(ArchMesh));

        CreatedMeshes.Add(MeshConfig.MeshName, ArchComponent);
    }

    return ArchComponent;
}

TSharedPtr<FDynamicMesh3> UUnrealMCPProceduralMeshCommands::GeneratePortalGeometry(int32 SourceLayer, int32 TargetLayer)
{
    // REAL IMPLEMENTATION - Generate portal geometry using modern UE 5.6.1 APIs
    TSharedPtr<FDynamicMesh3> PortalMesh = MakeShared<FDynamicMesh3>();

    // Create portal using DiscMeshGenerator for circular portal
    UE::Geometry::FDiscMeshGenerator DiscGen;
    DiscGen.Radius = 150.0f + (FMath::Abs(TargetLayer - SourceLayer) * 25.0f);
    DiscGen.AngleSamples = 32;
    DiscGen.RadialSamples = 8;
    DiscGen.bReverseOrientation = false;

    *PortalMesh = FDynamicMesh3(&DiscGen.Generate());

    // Calculate normals using modern UE 5.6.1 APIs
    UE::Geometry::FMeshNormals::QuickComputeVertexNormals(*PortalMesh);

    return PortalMesh;
}
