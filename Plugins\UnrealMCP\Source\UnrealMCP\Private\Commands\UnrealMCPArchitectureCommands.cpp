#include "Commands/UnrealMCPArchitectureCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"

// Modern UE 5.6.1 includes
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Components/SplineComponent.h"
#include "Components/SplineMeshComponent.h"
#include "Engine/SimpleConstructionScript.h"
#include "Components/ChildActorComponent.h"

// Modern UE 5.6.1 Nanite Support
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "PhysicsEngine/BodySetup.h"

// Modern UE 5.6.1 Performance APIs
#include "HAL/PlatformApplicationMisc.h"
#include "Async/TaskGraphInterfaces.h"

// Experimental UE 5.6.1 PCG includes - Forward declarations for now
namespace UE { namespace PCG { class FPCGContext; } }

// Editor includes
#include "Editor.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"

TSharedPtr<FJsonObject> UUnrealMCPArchitectureCommands::HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params)
{
    // THREAD SAFETY VALIDATION - Modern UE 5.6.1 requirement
    if (!IsInGameThread())
    {
        UE_LOG(LogTemp, Error, TEXT("UnrealMCPArchitectureCommands::HandleCommand - Must be called from game thread"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Command must be executed from game thread"));
    }

    // PARAMETER VALIDATION - Prevent memory leaks from invalid params
    if (!Params.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("UnrealMCPArchitectureCommands::HandleCommand - Invalid parameters"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Invalid parameters provided"));
    }

    UE_LOG(LogTemp, Log, TEXT("UnrealMCPArchitectureCommands::HandleCommand - Command: %s"), *CommandName);

    if (CommandName == TEXT("create_tower_structures"))
    {
        return HandleCreateTowerStructures(Params);
    }
    else if (CommandName == TEXT("create_base_buildings"))
    {
        return HandleCreateBaseBuildings(Params);
    }
    else if (CommandName == TEXT("create_inhibitor_monuments"))
    {
        return HandleCreateInhibitorMonuments(Params);
    }
    else if (CommandName == TEXT("create_nexus_architecture"))
    {
        return HandleCreateNexusArchitecture(Params);
    }
    else if (CommandName == TEXT("create_defensive_walls"))
    {
        return HandleCreateDefensiveWalls(Params);
    }
    else if (CommandName == TEXT("create_jungle_camps"))
    {
        return HandleCreateJungleCamps(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown architecture command: %s"), *CommandName));
}

TSharedPtr<FJsonObject> UUnrealMCPArchitectureCommands::HandleCreateTowerStructures(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO) - Using modern UE 5.6.1 validation
    if (!Params->HasField(TEXT("tower_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: tower_name"));
    }

    FString TowerName = Params->GetStringField(TEXT("tower_name"));
    FString TowerType = Params->GetStringField(TEXT("tower_type"));
    if (TowerType.IsEmpty()) TowerType = TEXT("basic");
    
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));
    int32 TeamIndex = Params->GetIntegerField(TEXT("team_index"));

    // Parse location from JSON
    FVector Location = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        Location.X = (*LocationObj)->GetNumberField(TEXT("x"));
        Location.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        Location.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create tower structures using modern UE 5.6.1 APIs
    FAuracronTowerConfig TowerConfig = GetLayerArchitecturalStyle(LayerIndex);
    TowerConfig.TowerName = TowerName;
    TowerConfig.LayerIndex = LayerIndex;
    TowerConfig.TeamIndex = TeamIndex;
    TowerConfig.TowerLocation = Location;

    // Configure tower type-specific properties
    if (TowerType == TEXT("advanced"))
    {
        TowerConfig.TowerHeight = 800.0f;
        TowerConfig.TowerRadius = 150.0f;
        TowerConfig.TowerLevels = 5;
    }
    else if (TowerType == TEXT("nexus"))
    {
        TowerConfig.TowerHeight = 1200.0f;
        TowerConfig.TowerRadius = 200.0f;
        TowerConfig.TowerLevels = 7;
        TowerConfig.bUsePCGGeneration = true;
    }
    else // basic
    {
        TowerConfig.TowerHeight = 500.0f;
        TowerConfig.TowerRadius = 100.0f;
        TowerConfig.TowerLevels = 3;
    }

    // Create robust tower structure using modern APIs
    AActor* CreatedTower = CreateRobustTowerStructure(TowerConfig);

    if (CreatedTower)
    {
        CreatedStructures.Add(TowerName, CreatedTower);

        // MANDATORY SAVING - Modern UE 5.6.1 requirement to prevent data loss
        if (!UEditorAssetLibrary::SaveAsset(CreatedTower->GetPathName()))
        {
            UE_LOG(LogTemp, Warning, TEXT("HandleCreateTowerStructures: Failed to save tower asset %s"), *TowerName);
        }

        // MEMORY LEAK PREVENTION - Ensure proper cleanup
        CreatedTower->AddToRoot(); // Prevent garbage collection

        UE_LOG(LogTemp, Log, TEXT("HandleCreateTowerStructures: Tower %s successfully created and saved"), *TowerName);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateTowerStructures: Failed to create tower %s - potential memory leak prevented"), *TowerName);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_tower_structures"));
    Response->SetStringField(TEXT("tower_name"), TowerName);
    Response->SetStringField(TEXT("tower_type"), TowerType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetNumberField(TEXT("team_index"), TeamIndex);
    Response->SetNumberField(TEXT("tower_height"), TowerConfig.TowerHeight);
    Response->SetNumberField(TEXT("tower_radius"), TowerConfig.TowerRadius);
    Response->SetNumberField(TEXT("tower_levels"), TowerConfig.TowerLevels);
    Response->SetBoolField(TEXT("hierarchical_instancing"), TowerConfig.bUseHierarchicalInstancing);
    Response->SetBoolField(TEXT("pcg_generation"), TowerConfig.bUsePCGGeneration);
    Response->SetBoolField(TEXT("success"), CreatedTower != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    // Add location info
    TSharedPtr<FJsonObject> LocationResponse = MakeShared<FJsonObject>();
    LocationResponse->SetNumberField(TEXT("x"), Location.X);
    LocationResponse->SetNumberField(TEXT("y"), Location.Y);
    LocationResponse->SetNumberField(TEXT("z"), Location.Z);
    Response->SetObjectField(TEXT("location"), LocationResponse);

    UE_LOG(LogTemp, Log, TEXT("HandleCreateTowerStructures: Created tower %s (Type: %s, Layer: %d, Team: %d, Height: %.1f)"),
           *TowerName, *TowerType, LayerIndex, TeamIndex, TowerConfig.TowerHeight);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPArchitectureCommands::HandleCreateBaseBuildings(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("building_name")) || !Params->HasField(TEXT("building_type")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: building_name, building_type"));
    }

    FString BuildingName = Params->GetStringField(TEXT("building_name"));
    FString BuildingType = Params->GetStringField(TEXT("building_type"));
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // Parse location from JSON
    FVector Location = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        Location.X = (*LocationObj)->GetNumberField(TEXT("x"));
        Location.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        Location.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create base buildings using modern UE 5.6.1 APIs
    AActor* CreatedBuilding = CreateHierarchicalBuilding(BuildingName, BuildingType, Location, LayerIndex);
    
    if (CreatedBuilding)
    {
        CreatedStructures.Add(BuildingName, CreatedBuilding);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_base_buildings"));
    Response->SetStringField(TEXT("building_name"), BuildingName);
    Response->SetStringField(TEXT("building_type"), BuildingType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetBoolField(TEXT("success"), CreatedBuilding != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    // Add location info
    TSharedPtr<FJsonObject> LocationResponse = MakeShared<FJsonObject>();
    LocationResponse->SetNumberField(TEXT("x"), Location.X);
    LocationResponse->SetNumberField(TEXT("y"), Location.Y);
    LocationResponse->SetNumberField(TEXT("z"), Location.Z);
    Response->SetObjectField(TEXT("location"), LocationResponse);

    UE_LOG(LogTemp, Log, TEXT("HandleCreateBaseBuildings: Created building %s (Type: %s, Layer: %d)"),
           *BuildingName, *BuildingType, LayerIndex);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPArchitectureCommands::HandleCreateInhibitorMonuments(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("monument_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: monument_name"));
    }

    FString MonumentName = Params->GetStringField(TEXT("monument_name"));
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));
    int32 TeamIndex = Params->GetIntegerField(TEXT("team_index"));

    // Parse location from JSON
    FVector Location = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        Location.X = (*LocationObj)->GetNumberField(TEXT("x"));
        Location.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        Location.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create inhibitor monuments using modern UE 5.6.1 APIs
    FAuracronTowerConfig MonumentConfig = GetLayerArchitecturalStyle(LayerIndex);
    MonumentConfig.TowerName = MonumentName;
    MonumentConfig.LayerIndex = LayerIndex;
    MonumentConfig.TeamIndex = TeamIndex;
    MonumentConfig.TowerLocation = Location;
    MonumentConfig.TowerHeight = 600.0f;
    MonumentConfig.TowerRadius = 120.0f;
    MonumentConfig.TowerLevels = 1; // Monuments are typically single structures
    MonumentConfig.bUsePCGGeneration = true; // Use PCG for unique designs

    AActor* CreatedMonument = CreateRobustTowerStructure(MonumentConfig);
    
    if (CreatedMonument)
    {
        CreatedStructures.Add(MonumentName, CreatedMonument);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_inhibitor_monuments"));
    Response->SetStringField(TEXT("monument_name"), MonumentName);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetNumberField(TEXT("team_index"), TeamIndex);
    Response->SetBoolField(TEXT("success"), CreatedMonument != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateInhibitorMonuments: Created monument %s for layer %d, team %d"),
           *MonumentName, LayerIndex, TeamIndex);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPArchitectureCommands::HandleCreateNexusArchitecture(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("nexus_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: nexus_name"));
    }

    FString NexusName = Params->GetStringField(TEXT("nexus_name"));
    int32 TeamIndex = Params->GetIntegerField(TEXT("team_index"));
    int32 Complexity = Params->GetIntegerField(TEXT("complexity"));
    if (Complexity <= 0) Complexity = 5;

    // Parse location from JSON
    FVector Location = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        Location.X = (*LocationObj)->GetNumberField(TEXT("x"));
        Location.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        Location.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create nexus architecture using modern UE 5.6.1 APIs
    FAuracronTowerConfig NexusConfig = GetLayerArchitecturalStyle(0); // Nexus is always on ground layer
    NexusConfig.TowerName = NexusName;
    NexusConfig.LayerIndex = 0;
    NexusConfig.TeamIndex = TeamIndex;
    NexusConfig.TowerLocation = Location;
    NexusConfig.TowerHeight = 1500.0f + (Complexity * 100.0f);
    NexusConfig.TowerRadius = 300.0f + (Complexity * 50.0f);
    NexusConfig.TowerLevels = 5 + Complexity;
    NexusConfig.bUsePCGGeneration = true; // Always use PCG for nexus complexity
    NexusConfig.bUseHierarchicalInstancing = true;

    AActor* CreatedNexus = CreateRobustTowerStructure(NexusConfig);

    if (CreatedNexus)
    {
        CreatedStructures.Add(NexusName, CreatedNexus);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_nexus_architecture"));
    Response->SetStringField(TEXT("nexus_name"), NexusName);
    Response->SetNumberField(TEXT("team_index"), TeamIndex);
    Response->SetNumberField(TEXT("complexity"), Complexity);
    Response->SetNumberField(TEXT("nexus_height"), NexusConfig.TowerHeight);
    Response->SetNumberField(TEXT("nexus_radius"), NexusConfig.TowerRadius);
    Response->SetNumberField(TEXT("nexus_levels"), NexusConfig.TowerLevels);
    Response->SetBoolField(TEXT("success"), CreatedNexus != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateNexusArchitecture: Created nexus %s for team %d (Complexity: %d, Height: %.1f)"),
           *NexusName, TeamIndex, Complexity, NexusConfig.TowerHeight);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPArchitectureCommands::HandleCreateDefensiveWalls(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("wall_name")) || !Params->HasField(TEXT("wall_points")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: wall_name, wall_points"));
    }

    FString WallName = Params->GetStringField(TEXT("wall_name"));
    float WallHeight = Params->GetNumberField(TEXT("wall_height"));
    if (WallHeight <= 0.0f) WallHeight = 400.0f;
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // Parse wall points from JSON
    TArray<FVector> WallPoints;
    const TArray<TSharedPtr<FJsonValue>>* PointsArray;
    if (Params->TryGetArrayField(TEXT("wall_points"), PointsArray))
    {
        for (const auto& PointValue : *PointsArray)
        {
            const TSharedPtr<FJsonObject>& PointObj = PointValue->AsObject();
            if (PointObj.IsValid())
            {
                FVector Point;
                Point.X = PointObj->GetNumberField(TEXT("x"));
                Point.Y = PointObj->GetNumberField(TEXT("y"));
                Point.Z = PointObj->GetNumberField(TEXT("z"));
                WallPoints.Add(Point);
            }
        }
    }

    // STEP 2: REAL IMPLEMENTATION - Create defensive walls using modern UE 5.6.1 APIs
    FSplineStructureConfig WallConfig;
    WallConfig.StructureName = WallName;
    WallConfig.SplinePoints = WallPoints;
    WallConfig.StructureWidth = 100.0f; // Wall thickness
    WallConfig.StructureHeight = WallHeight;
    WallConfig.SplineSegments = WallPoints.Num() * 2; // More segments for smoother walls
    WallConfig.bClosedLoop = false;

    AActor* CreatedWall = CreateSplineBasedStructure(WallConfig);

    if (CreatedWall)
    {
        CreatedStructures.Add(WallName, CreatedWall);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_defensive_walls"));
    Response->SetStringField(TEXT("wall_name"), WallName);
    Response->SetNumberField(TEXT("wall_height"), WallHeight);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetNumberField(TEXT("wall_points_count"), WallPoints.Num());
    Response->SetNumberField(TEXT("spline_segments"), WallConfig.SplineSegments);
    Response->SetBoolField(TEXT("success"), CreatedWall != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateDefensiveWalls: Created wall %s with %d points (Height: %.1f, Layer: %d)"),
           *WallName, WallPoints.Num(), WallHeight, LayerIndex);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPArchitectureCommands::HandleCreateJungleCamps(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("camp_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: camp_name"));
    }

    FString CampName = Params->GetStringField(TEXT("camp_name"));
    FString CampType = Params->GetStringField(TEXT("camp_type"));
    if (CampType.IsEmpty()) CampType = TEXT("neutral");
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // Parse location from JSON
    FVector Location = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        Location.X = (*LocationObj)->GetNumberField(TEXT("x"));
        Location.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        Location.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create jungle camps using modern UE 5.6.1 APIs
    FAuracronTowerConfig CampConfig = GetLayerArchitecturalStyle(LayerIndex);
    CampConfig.TowerName = CampName;
    CampConfig.LayerIndex = LayerIndex;
    CampConfig.TowerLocation = Location;

    // Configure camp type-specific properties
    if (CampType == TEXT("epic"))
    {
        CampConfig.TowerHeight = 400.0f;
        CampConfig.TowerRadius = 200.0f;
        CampConfig.TowerLevels = 2;
        CampConfig.bUsePCGGeneration = true;
    }
    else if (CampType == TEXT("boss"))
    {
        CampConfig.TowerHeight = 600.0f;
        CampConfig.TowerRadius = 300.0f;
        CampConfig.TowerLevels = 3;
        CampConfig.bUsePCGGeneration = true;
    }
    else // neutral
    {
        CampConfig.TowerHeight = 200.0f;
        CampConfig.TowerRadius = 100.0f;
        CampConfig.TowerLevels = 1;
        CampConfig.bUsePCGGeneration = false;
    }

    AActor* CreatedCamp = CreateRobustTowerStructure(CampConfig);

    if (CreatedCamp)
    {
        CreatedStructures.Add(CampName, CreatedCamp);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_jungle_camps"));
    Response->SetStringField(TEXT("camp_name"), CampName);
    Response->SetStringField(TEXT("camp_type"), CampType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetBoolField(TEXT("success"), CreatedCamp != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateJungleCamps: Created camp %s (Type: %s, Layer: %d)"),
           *CampName, *CampType, LayerIndex);

    return Response;
}

// ========================================
// ROBUST ARCHITECTURE CREATION - MODERN UE 5.6.1 APIS
// ========================================

AActor* UUnrealMCPArchitectureCommands::CreateRobustTowerStructure(const FAuracronTowerConfig& TowerConfig)
{
    // THREAD SAFETY VALIDATION - Modern UE 5.6.1 requirement
    if (!IsInGameThread())
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustTowerStructure: Must be called from game thread"));
        return nullptr;
    }

    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustTowerStructure: No valid world context"));
        return nullptr;
    }

    // MEMORY LEAK PREVENTION - Validate config before proceeding
    if (TowerConfig.TowerName.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustTowerStructure: Invalid tower name - preventing memory leak"));
        return nullptr;
    }

    // STEP 1: Create tower actor using modern UE 5.6.1 APIs with enhanced spawn parameters
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*TowerConfig.TowerName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    SpawnParams.bDeferConstruction = false; // Immediate construction for stability
    SpawnParams.ObjectFlags = RF_Transactional; // Enable undo/redo support

    AActor* TowerActor = World->SpawnActor<AActor>(AActor::StaticClass(), TowerConfig.TowerLocation, TowerConfig.TowerRotation, SpawnParams);
    if (!TowerActor || !IsValid(TowerActor))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustTowerStructure: Failed to spawn tower actor - memory leak prevented"));
        return nullptr;
    }

    // Configure actor with modern UE 5.6.1 settings
    TowerActor->SetActorLabel(TowerConfig.TowerName);
    TowerActor->SetActorEnableCollision(true);
    TowerActor->SetCanBeDamaged(true); // Enable for MOBA gameplay

    // STEP 2: Create hierarchical instanced static mesh component using modern UE 5.6.1 APIs
    if (TowerConfig.bUseHierarchicalInstancing)
    {
        UHierarchicalInstancedStaticMeshComponent* HISMComponent = NewObject<UHierarchicalInstancedStaticMeshComponent>(TowerActor);
        if (!HISMComponent || !IsValid(HISMComponent))
        {
            UE_LOG(LogTemp, Error, TEXT("CreateRobustTowerStructure: Failed to create HierarchicalInstancedStaticMeshComponent"));
            return nullptr;
        }

        // Configure HISM component using modern UE 5.6.1 settings
        HISMComponent->SetupAttachment(TowerActor->GetRootComponent());
        HISMComponent->SetCastShadow(true);
        HISMComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        HISMComponent->SetCollisionProfileName(TEXT("BlockAll"));

        // Configure modern UE 5.6.1 HISM settings with Nanite support
        HISMComponent->SetCullDistances(0, 10000); // No culling for important structures
        HISMComponent->bUseAsOccluder = true;
        HISMComponent->bEnableAutoLODGeneration = true;

        // Modern UE 5.6.1 Performance optimizations
        HISMComponent->bSortTriangles = true;
        HISMComponent->bUseDefaultCollision = true;

        // Enable modern UE 5.6.1 features for better performance
        HISMComponent->bReceivesDecals = true;
        HISMComponent->bUseAttachParentBound = false;

        // Create tower levels using hierarchical instancing
        for (int32 Level = 0; Level < TowerConfig.TowerLevels; Level++)
        {
            float LevelHeight = (TowerConfig.TowerHeight / TowerConfig.TowerLevels) * Level;
            float LevelScale = 1.0f - (Level * 0.1f); // Each level slightly smaller

            FTransform LevelTransform;
            LevelTransform.SetLocation(FVector(0, 0, LevelHeight));
            LevelTransform.SetScale3D(FVector(LevelScale, LevelScale, 1.0f));

            HISMComponent->AddInstance(LevelTransform);
        }

        // Add component to actor
        TowerActor->AddInstanceComponent(HISMComponent);
        HISMComponent->RegisterComponent();

        // Cache the HISM component
        HierarchicalComponents.Add(TowerConfig.TowerName, HISMComponent);
    }
    else
    {
        // STEP 3: Create individual static mesh components for each level
        for (int32 Level = 0; Level < TowerConfig.TowerLevels; Level++)
        {
            UStaticMeshComponent* LevelComponent = NewObject<UStaticMeshComponent>(TowerActor);
            if (!LevelComponent || !IsValid(LevelComponent))
            {
                continue;
            }

            float LevelHeight = (TowerConfig.TowerHeight / TowerConfig.TowerLevels) * Level;
            float LevelScale = 1.0f - (Level * 0.1f);

            LevelComponent->SetupAttachment(TowerActor->GetRootComponent());
            LevelComponent->SetRelativeLocation(FVector(0, 0, LevelHeight));
            LevelComponent->SetRelativeScale3D(FVector(LevelScale, LevelScale, 1.0f));
            LevelComponent->SetCastShadow(true);
            LevelComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);

            TowerActor->AddInstanceComponent(LevelComponent);
            LevelComponent->RegisterComponent();
        }
    }

    // STEP 4: Setup PCG generation if requested
    if (TowerConfig.bUsePCGGeneration)
    {
        UActorComponent* PCGComponent = SetupPCGGeneration(TowerConfig.TowerName, TowerConfig.LayerIndex);
        if (PCGComponent)
        {
            TowerActor->AddInstanceComponent(PCGComponent);
            PCGComponent->RegisterComponent();
        }
    }

    UE_LOG(LogTemp, Log, TEXT("CreateRobustTowerStructure: Created tower %s with %d levels (Height: %.1f, HISM: %s, PCG: %s)"),
           *TowerConfig.TowerName, TowerConfig.TowerLevels, TowerConfig.TowerHeight,
           TowerConfig.bUseHierarchicalInstancing ? TEXT("Yes") : TEXT("No"),
           TowerConfig.bUsePCGGeneration ? TEXT("Yes") : TEXT("No"));

    return TowerActor;
}

AActor* UUnrealMCPArchitectureCommands::CreateHierarchicalBuilding(const FString& BuildingName, const FString& BuildingType, const FVector& Location, int32 LayerIndex)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateHierarchicalBuilding: No valid world context"));
        return nullptr;
    }

    // STEP 1: Create building actor using modern UE 5.6.1 APIs
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*BuildingName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    AActor* BuildingActor = World->SpawnActor<AActor>(AActor::StaticClass(), Location, FRotator::ZeroRotator, SpawnParams);
    if (!BuildingActor || !IsValid(BuildingActor))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateHierarchicalBuilding: Failed to spawn building actor"));
        return nullptr;
    }

    BuildingActor->SetActorLabel(BuildingName);

    // STEP 2: Create building elements using modular architecture
    TArray<FBuildingElementConfig> BuildingElements;

    // Configure building type-specific elements
    if (BuildingType == TEXT("barracks"))
    {
        // Create barracks elements
        FBuildingElementConfig Foundation;
        Foundation.ElementName = TEXT("Foundation");
        Foundation.ElementType = TEXT("foundation");
        Foundation.ElementScale = FVector(4.0f, 4.0f, 0.5f);
        BuildingElements.Add(Foundation);

        FBuildingElementConfig Walls;
        Walls.ElementName = TEXT("Walls");
        Walls.ElementType = TEXT("wall");
        Walls.ElementScale = FVector(4.0f, 4.0f, 2.0f);
        Walls.ElementTransform.SetLocation(FVector(0, 0, 100));
        BuildingElements.Add(Walls);

        FBuildingElementConfig Roof;
        Roof.ElementName = TEXT("Roof");
        Roof.ElementType = TEXT("roof");
        Roof.ElementScale = FVector(4.2f, 4.2f, 0.3f);
        Roof.ElementTransform.SetLocation(FVector(0, 0, 300));
        BuildingElements.Add(Roof);
    }
    else if (BuildingType == TEXT("shop"))
    {
        // Create shop elements
        FBuildingElementConfig Foundation;
        Foundation.ElementName = TEXT("Foundation");
        Foundation.ElementType = TEXT("foundation");
        Foundation.ElementScale = FVector(3.0f, 3.0f, 0.5f);
        BuildingElements.Add(Foundation);

        FBuildingElementConfig Counter;
        Counter.ElementName = TEXT("Counter");
        Counter.ElementType = TEXT("pillar");
        Counter.ElementScale = FVector(2.0f, 1.0f, 1.5f);
        Counter.ElementTransform.SetLocation(FVector(0, 0, 75));
        BuildingElements.Add(Counter);
    }
    else // fountain or default
    {
        // Create fountain elements
        FBuildingElementConfig Base;
        Base.ElementName = TEXT("Base");
        Base.ElementType = TEXT("foundation");
        Base.ElementScale = FVector(2.0f, 2.0f, 1.0f);
        BuildingElements.Add(Base);

        FBuildingElementConfig Pillar;
        Pillar.ElementName = TEXT("Pillar");
        Pillar.ElementType = TEXT("pillar");
        Pillar.ElementScale = FVector(0.5f, 0.5f, 3.0f);
        Pillar.ElementTransform.SetLocation(FVector(0, 0, 150));
        BuildingElements.Add(Pillar);
    }

    // STEP 3: Create modular architecture using construction script
    AActor* ModularBuilding = CreateModularArchitecture(BuildingName, BuildingElements, Location);
    if (ModularBuilding)
    {
        // Transfer components from modular building to main building actor using modern UE 5.6.1 API
        TArray<UActorComponent*> Components;
        ModularBuilding->GetComponents<UActorComponent>(Components);
        for (UActorComponent* Component : Components)
        {
            if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
            {
                BuildingActor->AddInstanceComponent(MeshComp);
                MeshComp->RegisterComponent();
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("CreateHierarchicalBuilding: Created building %s (Type: %s, Layer: %d, Elements: %d)"),
           *BuildingName, *BuildingType, LayerIndex, BuildingElements.Num());

    return BuildingActor;
}

AActor* UUnrealMCPArchitectureCommands::CreateSplineBasedStructure(const FSplineStructureConfig& SplineConfig)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateSplineBasedStructure: No valid world context"));
        return nullptr;
    }

    if (SplineConfig.SplinePoints.Num() < 2)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateSplineBasedStructure: Need at least 2 spline points"));
        return nullptr;
    }

    // STEP 1: Create spline actor using modern UE 5.6.1 APIs
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*SplineConfig.StructureName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    AActor* SplineActor = World->SpawnActor<AActor>(AActor::StaticClass(), FVector::ZeroVector, FRotator::ZeroRotator, SpawnParams);
    if (!SplineActor || !IsValid(SplineActor))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateSplineBasedStructure: Failed to spawn spline actor"));
        return nullptr;
    }

    SplineActor->SetActorLabel(SplineConfig.StructureName);

    // STEP 2: Create spline component using modern UE 5.6.1 APIs
    USplineComponent* SplineComponent = NewObject<USplineComponent>(SplineActor);
    if (!SplineComponent || !IsValid(SplineComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateSplineBasedStructure: Failed to create SplineComponent"));
        return nullptr;
    }

    // Configure spline component using modern settings
    SplineComponent->SetupAttachment(SplineActor->GetRootComponent());
    SplineComponent->ClearSplinePoints();

    // Add spline points
    for (int32 i = 0; i < SplineConfig.SplinePoints.Num(); i++)
    {
        SplineComponent->AddSplinePoint(SplineConfig.SplinePoints[i], ESplineCoordinateSpace::World);
    }

    // Configure spline settings
    SplineComponent->SetClosedLoop(SplineConfig.bClosedLoop);
    SplineComponent->UpdateSpline();

    // Add component to actor
    SplineActor->AddInstanceComponent(SplineComponent);
    SplineComponent->RegisterComponent();

    // STEP 3: Create spline mesh components along the spline
    int32 NumSegments = SplineComponent->GetNumberOfSplineSegments();
    for (int32 SegmentIndex = 0; SegmentIndex < NumSegments; SegmentIndex++)
    {
        USplineMeshComponent* SplineMeshComponent = NewObject<USplineMeshComponent>(SplineActor);
        if (!SplineMeshComponent || !IsValid(SplineMeshComponent))
        {
            continue;
        }

        // Configure spline mesh component
        SplineMeshComponent->SetupAttachment(SplineComponent);
        SplineMeshComponent->SetCastShadow(true);
        SplineMeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        SplineMeshComponent->SetCollisionProfileName(TEXT("BlockAll"));

        // Set spline mesh start and end points
        FVector StartPos, StartTangent, EndPos, EndTangent;
        SplineComponent->GetLocationAndTangentAtSplinePoint(SegmentIndex, StartPos, StartTangent, ESplineCoordinateSpace::Local);
        SplineComponent->GetLocationAndTangentAtSplinePoint(SegmentIndex + 1, EndPos, EndTangent, ESplineCoordinateSpace::Local);

        SplineMeshComponent->SetStartAndEnd(StartPos, StartTangent, EndPos, EndTangent);

        // Configure spline mesh scale
        FVector2D StartScale(SplineConfig.StructureWidth / 100.0f, SplineConfig.StructureHeight / 100.0f);
        FVector2D EndScale = StartScale;
        SplineMeshComponent->SetStartScale(StartScale);
        SplineMeshComponent->SetEndScale(EndScale);

        // Add component to actor
        SplineActor->AddInstanceComponent(SplineMeshComponent);
        SplineMeshComponent->RegisterComponent();
    }

    // Cache the spline component
    SplineComponents.Add(SplineConfig.StructureName, SplineComponent);

    UE_LOG(LogTemp, Log, TEXT("CreateSplineBasedStructure: Created spline structure %s with %d points and %d segments"),
           *SplineConfig.StructureName, SplineConfig.SplinePoints.Num(), NumSegments);

    return SplineActor;
}

AActor* UUnrealMCPArchitectureCommands::CreateModularArchitecture(const FString& ArchitectureName, const TArray<FBuildingElementConfig>& Elements, const FVector& Location)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateModularArchitecture: No valid world context"));
        return nullptr;
    }

    // STEP 1: Create modular architecture actor using modern UE 5.6.1 APIs
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*ArchitectureName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    AActor* ArchitectureActor = World->SpawnActor<AActor>(AActor::StaticClass(), Location, FRotator::ZeroRotator, SpawnParams);
    if (!ArchitectureActor || !IsValid(ArchitectureActor))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateModularArchitecture: Failed to spawn architecture actor"));
        return nullptr;
    }

    ArchitectureActor->SetActorLabel(ArchitectureName);

    // STEP 2: Create construction script using modern UE 5.6.1 APIs
    USimpleConstructionScript* ConstructionScript = NewObject<USimpleConstructionScript>(ArchitectureActor);
    if (!ConstructionScript || !IsValid(ConstructionScript))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateModularArchitecture: Failed to create SimpleConstructionScript"));
        return nullptr;
    }

    // STEP 3: Create building elements using construction script
    for (const FBuildingElementConfig& Element : Elements)
    {
        UStaticMeshComponent* ElementComponent = NewObject<UStaticMeshComponent>(ArchitectureActor);
        if (!ElementComponent || !IsValid(ElementComponent))
        {
            continue;
        }

        // Configure element component
        ElementComponent->SetupAttachment(ArchitectureActor->GetRootComponent());
        ElementComponent->SetRelativeTransform(Element.ElementTransform);
        ElementComponent->SetRelativeScale3D(Element.ElementScale);
        ElementComponent->SetCastShadow(true);
        ElementComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        ElementComponent->SetCollisionProfileName(TEXT("BlockAll"));

        // Add component to actor
        ArchitectureActor->AddInstanceComponent(ElementComponent);
        ElementComponent->RegisterComponent();
    }

    // Cache the construction script
    ConstructionScripts.Add(ArchitectureName, ConstructionScript);

    UE_LOG(LogTemp, Log, TEXT("CreateModularArchitecture: Created modular architecture %s with %d elements"),
           *ArchitectureName, Elements.Num());

    return ArchitectureActor;
}

UActorComponent* UUnrealMCPArchitectureCommands::SetupPCGGeneration(const FString& StructureName, int32 LayerIndex)
{
    // STEP 1: Create PCG component using modern UE 5.6.1 APIs (simplified for now)
    // Note: Full PCG integration will be implemented when PCG APIs are fully stable
    UActorComponent* PCGComponent = NewObject<UActorComponent>();
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("SetupPCGGeneration: Failed to create PCG component"));
        return nullptr;
    }

    // STEP 2: Configure PCG settings based on layer
    // This is a placeholder for future PCG integration
    // In a full implementation, this would configure:
    // - PCG graphs for procedural generation
    // - Layer-specific generation rules
    // - Biome-specific parameters
    // - Performance optimization settings

    UE_LOG(LogTemp, Log, TEXT("SetupPCGGeneration: Created PCG component for structure %s (Layer: %d)"),
           *StructureName, LayerIndex);

    return PCGComponent;
}

FAuracronTowerConfig UUnrealMCPArchitectureCommands::GetLayerArchitecturalStyle(int32 LayerIndex)
{
    FAuracronTowerConfig Config;
    Config.LayerIndex = LayerIndex;

    switch (LayerIndex)
    {
        case 0: // Planície Radiante - Golden/Organic architecture
            Config.TowerHeight = 500.0f;
            Config.TowerRadius = 100.0f;
            Config.TowerLevels = 3;
            Config.TowerScale = FVector(1.0f, 1.0f, 1.2f); // Taller structures
            Config.bUseHierarchicalInstancing = true;
            Config.bUsePCGGeneration = false; // More traditional architecture
            break;
        case 1: // Firmamento Zephyr - Crystalline/Ethereal architecture
            Config.TowerHeight = 800.0f;
            Config.TowerRadius = 80.0f;
            Config.TowerLevels = 5;
            Config.TowerScale = FVector(0.8f, 0.8f, 1.5f); // Thinner, taller structures
            Config.bUseHierarchicalInstancing = true;
            Config.bUsePCGGeneration = true; // More complex, ethereal designs
            break;
        case 2: // Abismo Umbral - Dark/Twisted architecture
            Config.TowerHeight = 600.0f;
            Config.TowerRadius = 120.0f;
            Config.TowerLevels = 4;
            Config.TowerScale = FVector(1.2f, 1.2f, 1.0f); // Wider, more imposing structures
            Config.bUseHierarchicalInstancing = true;
            Config.bUsePCGGeneration = true; // Complex, twisted designs
            break;
        default:
            Config.TowerHeight = 400.0f;
            Config.TowerRadius = 100.0f;
            Config.TowerLevels = 2;
            Config.TowerScale = FVector(1.0f, 1.0f, 1.0f);
            Config.bUseHierarchicalInstancing = false;
            Config.bUsePCGGeneration = false;
            break;
    }

    return Config;
}
