#pragma once

#include "CoreMinimal.h"
#include "Json.h"

// Modern UE 5.6.1 Static Mesh APIs
#include "Engine/StaticMeshActor.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"

// Modern UE 5.6.1 Spline APIs
#include "Components/SplineComponent.h"
#include "Components/SplineMeshComponent.h"

// Modern UE 5.6.1 Construction Script APIs
#include "Engine/SimpleConstructionScript.h"
#include "Components/ActorComponent.h"
#include "Components/ChildActorComponent.h"

// Experimental UE 5.6.1 PCG APIs - Forward declarations to avoid header issues
// Note: PCG includes will be added in implementation file
namespace UE { namespace PCG { class FPCGContext; } }

// Engine APIs
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "GameFramework/Actor.h"
#include "Materials/MaterialInterface.h"
#include "PhysicsEngine/BodySetup.h"

// Editor APIs
#include "Editor.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"

#include "UnrealMCPArchitectureCommands.generated.h"

// ========================================
// MODERN UE 5.6.1 ARCHITECTURE STRUCTURES
// ========================================

/**
 * Auracron tower structure configuration
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FAuracronTowerConfig
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString TowerName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 LayerIndex = 0;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 TeamIndex = 0;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector TowerLocation = FVector::ZeroVector;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector TowerScale = FVector(1.0f, 1.0f, 1.0f);

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FRotator TowerRotation = FRotator::ZeroRotator;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float TowerHeight = 500.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float TowerRadius = 100.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 TowerLevels = 3;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bUseHierarchicalInstancing = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bUsePCGGeneration = true;

    FAuracronTowerConfig()
    {
        TowerName = TEXT("AuracronTower");
        LayerIndex = 0;
        TeamIndex = 0;
        TowerLocation = FVector::ZeroVector;
        TowerScale = FVector(1.0f, 1.0f, 1.0f);
        TowerRotation = FRotator::ZeroRotator;
        TowerHeight = 500.0f;
        TowerRadius = 100.0f;
        TowerLevels = 3;
        bUseHierarchicalInstancing = true;
        bUsePCGGeneration = true;
    }
};

/**
 * Building element configuration
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FBuildingElementConfig
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString ElementName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString ElementType; // wall, pillar, roof, foundation

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector ElementScale = FVector(1.0f, 1.0f, 1.0f);

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FTransform ElementTransform;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TSoftObjectPtr<UStaticMesh> ElementMesh;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TSoftObjectPtr<UMaterialInterface> ElementMaterial;

    FBuildingElementConfig()
    {
        ElementName = TEXT("BuildingElement");
        ElementType = TEXT("wall");
        ElementScale = FVector(1.0f, 1.0f, 1.0f);
        ElementTransform = FTransform::Identity;
        ElementMesh = nullptr;
        ElementMaterial = nullptr;
    }
};

/**
 * Spline-based structure configuration
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FSplineStructureConfig
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString StructureName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<FVector> SplinePoints;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float StructureWidth = 200.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float StructureHeight = 300.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 SplineSegments = 10;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bClosedLoop = false;

    FSplineStructureConfig()
    {
        StructureName = TEXT("SplineStructure");
        StructureWidth = 200.0f;
        StructureHeight = 300.0f;
        SplineSegments = 10;
        bClosedLoop = false;
    }
};

/**
 * UnrealMCP Architecture Commands - Modern UE 5.6.1 Implementation
 * Handles procedural architectural structure creation with Auracron-specific designs
 */
UCLASS()
class UNREALMCP_API UUnrealMCPArchitectureCommands : public UObject
{
    GENERATED_BODY()

public:
    /**
     * Handle architecture command routing
     * @param CommandName Name of the command to execute
     * @param Params JSON parameters for the command
     * @return JSON response with command results
     */
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params);

    /**
     * Create tower structures using modern UE 5.6.1 APIs
     * @param Params - Must include:
     *                "tower_name" - Name of the tower
     *                "tower_type" - Type of tower (basic, advanced, nexus)
     *                "location" - Tower location
     *                "layer_index" - Layer index (0=Planície, 1=Firmamento, 2=Abismo)
     *                "team_index" - Team index for styling
     * @return JSON response with the created tower details
     */
    TSharedPtr<FJsonObject> HandleCreateTowerStructures(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create base buildings using hierarchical instancing
     * @param Params - Must include:
     *                "building_name" - Name of the building
     *                "building_type" - Type of building (barracks, shop, fountain)
     *                "location" - Building location
     *                "layer_index" - Layer index
     * @return JSON response with building creation results
     */
    TSharedPtr<FJsonObject> HandleCreateBaseBuildings(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create inhibitor monuments with unique designs
     * @param Params - Must include:
     *                "monument_name" - Name of the monument
     *                "location" - Monument location
     *                "layer_index" - Layer index
     *                "team_index" - Team index
     * @return JSON response with monument creation results
     */
    TSharedPtr<FJsonObject> HandleCreateInhibitorMonuments(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create nexus architecture with advanced geometry
     * @param Params - Must include:
     *                "nexus_name" - Name of the nexus
     *                "location" - Nexus location
     *                "team_index" - Team index
     *                "complexity" - Architecture complexity level
     * @return JSON response with nexus creation results
     */
    TSharedPtr<FJsonObject> HandleCreateNexusArchitecture(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create defensive walls using spline-based generation
     * @param Params - Must include:
     *                "wall_name" - Name of the wall system
     *                "wall_points" - Array of wall control points
     *                "wall_height" - Height of the walls
     *                "layer_index" - Layer index
     * @return JSON response with wall creation results
     */
    TSharedPtr<FJsonObject> HandleCreateDefensiveWalls(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create jungle camps with natural integration
     * @param Params - Must include:
     *                "camp_name" - Name of the jungle camp
     *                "location" - Camp location
     *                "camp_type" - Type of camp (neutral, epic, boss)
     *                "layer_index" - Layer index
     * @return JSON response with camp creation results
     */
    TSharedPtr<FJsonObject> HandleCreateJungleCamps(const TSharedPtr<FJsonObject>& Params);

private:
    /**
     * Create robust tower structure using modern UE 5.6.1 APIs
     * @param TowerConfig Tower configuration
     * @return Created tower actor
     */
    AActor* CreateRobustTowerStructure(const FAuracronTowerConfig& TowerConfig);

    /**
     * Create building using hierarchical instanced static mesh
     * @param BuildingName Name of the building
     * @param BuildingType Type of building
     * @param Location Building location
     * @param LayerIndex Layer index
     * @return Created building actor
     */
    AActor* CreateHierarchicalBuilding(const FString& BuildingName, const FString& BuildingType, const FVector& Location, int32 LayerIndex);

    /**
     * Create spline-based structure using modern spline APIs
     * @param SplineConfig Spline structure configuration
     * @return Created spline structure actor
     */
    AActor* CreateSplineBasedStructure(const FSplineStructureConfig& SplineConfig);

    /**
     * Create modular architecture using construction script
     * @param ArchitectureName Name of the architecture
     * @param Elements Array of building elements
     * @param Location Base location
     * @return Created modular architecture actor
     */
    AActor* CreateModularArchitecture(const FString& ArchitectureName, const TArray<FBuildingElementConfig>& Elements, const FVector& Location);

    /**
     * Setup PCG-based procedural generation
     * @param StructureName Name of the structure
     * @param LayerIndex Layer index
     * @return PCG component for procedural generation
     */
    UActorComponent* SetupPCGGeneration(const FString& StructureName, int32 LayerIndex);

    /**
     * Get layer-specific architectural style
     * @param LayerIndex Layer index
     * @return Architectural configuration for the layer
     */
    FAuracronTowerConfig GetLayerArchitecturalStyle(int32 LayerIndex);

private:
    // Cache for created structures
    UPROPERTY()
    TMap<FString, TObjectPtr<AActor>> CreatedStructures;

    // Cache for hierarchical instanced components
    UPROPERTY()
    TMap<FString, TObjectPtr<UHierarchicalInstancedStaticMeshComponent>> HierarchicalComponents;

    // Cache for spline components
    UPROPERTY()
    TMap<FString, TObjectPtr<USplineComponent>> SplineComponents;

    // Cache for construction scripts
    UPROPERTY()
    TMap<FString, TObjectPtr<USimpleConstructionScript>> ConstructionScripts;
};
